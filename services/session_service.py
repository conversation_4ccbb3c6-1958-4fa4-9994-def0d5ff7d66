"""会话服务

负责管理会话的生命周期，包括创建、更新、删除会话，以及管理会话的上下文和配置。
"""

import time
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from uuid import UUID, uuid4

from sqlalchemy import select, update, delete, func, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from .base_service import BaseService, ServiceError
from database.models import Session, Conversation
from api.models.schemas import (
    SessionCreate, SessionUpdate, SessionResponse,
    SessionStatus, SessionListResponse
)


class SessionService(BaseService):
    """会话服务类
    
    提供会话管理的核心功能：
    - 会话的CRUD操作
    - 会话状态管理
    - 会话配置管理
    - 会话统计和分析
    """
    
    def __init__(self):
        super().__init__("SessionService")
        self._active_sessions: Dict[str, Dict[str, Any]] = {}
    
    async def initialize(self) -> None:
        """初始化会话服务"""
        self.logger.info("Initializing SessionService")
        
        # 加载活跃会话到内存
        await self._load_active_sessions()
        
        # 清理过期会话
        await self._cleanup_expired_sessions()
        
        self.logger.info(f"SessionService initialized with {len(self._active_sessions)} active sessions")
    
    async def cleanup(self) -> None:
        """清理资源"""
        self.logger.info("Cleaning up SessionService")
        
        # 保存活跃会话状态
        await self._save_active_sessions()
        
        self._active_sessions.clear()
    
    async def create_session(
        self, 
        session_data: SessionCreate,
        user_id: Optional[str] = None
    ) -> SessionResponse:
        """创建新会话
        
        Args:
            session_data: 会话创建数据
            user_id: 用户ID（可选）
        
        Returns:
            创建的会话信息
        """
        return await self.execute_with_monitoring(
            "create_session",
            self._create_session_impl,
            session_data,
            user_id
        )
    
    async def _create_session_impl(
        self,
        session_data: SessionCreate,
        user_id: Optional[str] = None
    ) -> SessionResponse:
        """创建会话的实现"""
        # 验证输入数据
        self.validate_input(
            session_data.dict(),
            required_fields=["name"]
        )
        
        # 验证模型配置
        if session_data.enabled_models:
            await self._validate_models(session_data.enabled_models)
        
        session_id = str(uuid4())
        now = datetime.utcnow()
        
        # 创建会话对象
        session = Session(
            id=session_id,
            name=session_data.name,
            description=session_data.description,
            user_id=user_id,
            enabled_models=session_data.enabled_models or [],
            model_configs=session_data.model_configs or {},
            system_prompt=session_data.system_prompt,
            status=SessionStatus.ACTIVE,
            created_at=now,
            updated_at=now,
            last_activity_at=now,
            total_messages=0,
            total_tokens=0,
            total_cost=0.0
        )
        
        # 保存到数据库
        async with self.get_db_session() as db_session:
            db_session.add(session)
            await db_session.flush()
            await db_session.refresh(session)
        
        # 添加到活跃会话缓存
        self._active_sessions[session_id] = {
            "id": session_id,
            "name": session.name,
            "status": session.status,
            "last_activity": time.time(),
            "message_count": 0
        }
        
        self.logger.info(f"Created session: {session_id}")
        
        return SessionResponse.from_orm(session)
    
    async def get_session(self, session_id: str) -> SessionResponse:
        """获取会话详情
        
        Args:
            session_id: 会话ID
        
        Returns:
            会话详情
        """
        return await self.execute_with_monitoring(
            "get_session",
            self._get_session_impl,
            session_id
        )
    
    async def _get_session_impl(self, session_id: str) -> SessionResponse:
        """获取会话的实现"""
        async with self.get_db_session() as db_session:
            query = select(Session).where(Session.id == session_id)
            result = await db_session.execute(query)
            session = result.scalar_one_or_none()
            
            if not session:
                raise ServiceError(
                    f"Session {session_id} not found",
                    error_code="SESSION_NOT_FOUND"
                )
            
            return SessionResponse.from_orm(session)
        return None

    async def update_session(
        self,
        session_id: str,
        updates: SessionUpdate
    ) -> SessionResponse:
        """更新会话
        
        Args:
            session_id: 会话ID
            updates: 更新数据
        
        Returns:
            更新后的会话信息
        """
        return await self.execute_with_monitoring(
            "update_session",
            self._update_session_impl,
            session_id,
            updates
        )
    
    async def _update_session_impl(
        self,
        session_id: str,
        updates: SessionUpdate
    ) -> SessionResponse:
        """更新会话的实现"""
        # 验证模型配置
        if updates.enabled_models:
            await self._validate_models(updates.enabled_models)
        
        update_data = {}
        for field, value in updates.dict(exclude_unset=True).items():
            if value is not None:
                update_data[field] = value
        
        if update_data:
            update_data["updated_at"] = datetime.utcnow()
        
        async with self.get_db_session() as db_session:
            # 更新数据库
            query = (
                update(Session)
                .where(Session.id == session_id)
                .values(**update_data)
                .returning(Session)
            )
            
            result = await db_session.execute(query)
            session = result.scalar_one_or_none()
            
            if not session:
                raise ServiceError(
                    f"Session {session_id} not found",
                    error_code="SESSION_NOT_FOUND"
                )
            
            # 更新活跃会话缓存
            if session_id in self._active_sessions:
                self._active_sessions[session_id].update({
                    "name": session.name,
                    "status": session.status,
                    "last_activity": time.time()
                })
            
            return SessionResponse.from_orm(session)
        return None

    async def delete_session(self, session_id: str) -> bool:
        """删除会话
        
        Args:
            session_id: 会话ID
        
        Returns:
            是否删除成功
        """
        return await self.execute_with_monitoring(
            "delete_session",
            self._delete_session_impl,
            session_id
        )
    
    async def _delete_session_impl(self, session_id: str) -> bool:
        """删除会话的实现"""
        async with self.get_db_session() as db_session:
            # 先删除相关的对话记录
            await db_session.execute(
                delete(Conversation).where(Conversation.session_id == session_id)
            )
            
            # 删除会话
            result = await db_session.execute(
                delete(Session).where(Session.id == session_id)
            )
            
            deleted_count = result.rowcount
            
            if deleted_count == 0:
                raise ServiceError(
                    f"Session {session_id} not found",
                    error_code="SESSION_NOT_FOUND"
                )
            
            # 从活跃会话缓存中移除
            self._active_sessions.pop(session_id, None)
            
            self.logger.info(f"Deleted session: {session_id}")
            
            return True
        return None

    async def list_sessions(
        self,
        user_id: Optional[str] = None,
        status: Optional[SessionStatus] = None,
        model_filter: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        page: int = 1,
        size: int = 20
    ) -> SessionListResponse:
        """获取会话列表
        
        Args:
            user_id: 用户ID过滤
            status: 状态过滤
            model_filter: 模型过滤
            start_date: 开始日期
            end_date: 结束日期
            page: 页码
            size: 每页大小
        
        Returns:
            会话列表响应
        """
        return await self.execute_with_monitoring(
            "list_sessions",
            self._list_sessions_impl,
            user_id, status, model_filter, start_date, end_date, page, size
        )
    
    async def _list_sessions_impl(
        self,
        user_id: Optional[str],
        status: Optional[SessionStatus],
        model_filter: Optional[str],
        start_date: Optional[datetime],
        end_date: Optional[datetime],
        page: int,
        size: int
    ) -> SessionListResponse:
        """获取会话列表的实现"""
        async with self.get_db_session() as db_session:
            # 构建查询条件
            conditions = []
            
            if user_id:
                conditions.append(Session.user_id == user_id)
            
            if status:
                conditions.append(Session.status == status)
            
            if model_filter:
                conditions.append(Session.enabled_models.contains([model_filter]))
            
            if start_date:
                conditions.append(Session.created_at >= start_date)
            
            if end_date:
                conditions.append(Session.created_at <= end_date)
            
            # 构建查询
            query = select(Session)
            if conditions:
                query = query.where(and_(*conditions))
            
            # 计算总数
            count_query = select(func.count(Session.id))
            if conditions:
                count_query = count_query.where(and_(*conditions))
            
            total_result = await db_session.execute(count_query)
            total = total_result.scalar()
            
            # 分页查询
            offset = (page - 1) * size
            query = (
                query
                .order_by(Session.last_activity_at.desc())
                .offset(offset)
                .limit(size)
            )
            
            result = await db_session.execute(query)
            sessions = result.scalars().all()
            
            # 转换为响应格式
            session_responses = [SessionResponse.from_orm(session) for session in sessions]
            
            return SessionListResponse(
                sessions=session_responses,
                total=total,
                page=page,
                size=size,
                pages=(total + size - 1) // size
            )
        return None

    async def activate_session(self, session_id: str) -> SessionResponse:
        """激活会话"""
        return await self.update_session(
            session_id,
            SessionUpdate(status=SessionStatus.ACTIVE)
        )
    
    async def deactivate_session(self, session_id: str) -> SessionResponse:
        """停用会话"""
        return await self.update_session(
            session_id,
            SessionUpdate(status=SessionStatus.INACTIVE)
        )
    
    async def update_session_activity(self, session_id: str) -> None:
        """更新会话活动时间"""
        now = datetime.utcnow()
        
        async with self.get_db_session() as db_session:
            await db_session.execute(
                update(Session)
                .where(Session.id == session_id)
                .values(last_activity_at=now)
            )
        
        # 更新缓存
        if session_id in self._active_sessions:
            self._active_sessions[session_id]["last_activity"] = time.time()
    
    async def update_session_stats(
        self,
        session_id: str,
        message_count_delta: int = 0,
        token_count_delta: int = 0,
        cost_delta: float = 0.0
    ) -> None:
        """更新会话统计信息"""
        async with self.get_db_session() as db_session:
            await db_session.execute(
                update(Session)
                .where(Session.id == session_id)
                .values(
                    total_messages=Session.total_messages + message_count_delta,
                    total_tokens=Session.total_tokens + token_count_delta,
                    total_cost=Session.total_cost + cost_delta,
                    last_activity_at=datetime.utcnow()
                )
            )
        
        # 更新缓存
        if session_id in self._active_sessions:
            cache_entry = self._active_sessions[session_id]
            cache_entry["message_count"] += message_count_delta
            cache_entry["last_activity"] = time.time()
    
    async def get_session_conversations(
        self,
        session_id: str,
        model_filter: Optional[str] = None,
        page: int = 1,
        size: int = 50
    ) -> Dict[str, Any]:
        """获取会话的对话记录"""
        async with self.get_db_session() as db_session:
            # 验证会话存在
            session_query = select(Session).where(Session.id == session_id)
            session_result = await db_session.execute(session_query)
            session = session_result.scalar_one_or_none()
            
            if not session:
                raise ServiceError(
                    f"Session {session_id} not found",
                    error_code="SESSION_NOT_FOUND"
                )
            
            # 构建对话查询
            conditions = [Conversation.session_id == session_id]
            
            if model_filter:
                conditions.append(Conversation.model_name == model_filter)
            
            # 计算总数
            count_query = select(func.count(Conversation.id)).where(and_(*conditions))
            total_result = await db_session.execute(count_query)
            total = total_result.scalar()
            
            # 分页查询对话
            offset = (page - 1) * size
            query = (
                select(Conversation)
                .where(and_(*conditions))
                .order_by(Conversation.created_at.desc())
                .offset(offset)
                .limit(size)
            )
            
            result = await db_session.execute(query)
            conversations = result.scalars().all()
            
            return {
                "conversations": [{
                    "id": conv.id,
                    "model_name": conv.model_name,
                    "user_message": conv.user_message,
                    "assistant_message": conv.assistant_message,
                    "status": conv.status,
                    "response_time": conv.response_time,
                    "total_tokens": conv.total_tokens,
                    "cost": conv.cost,
                    "created_at": conv.created_at.isoformat()
                } for conv in conversations],
                "total": total,
                "page": page,
                "size": size,
                "pages": (total + size - 1) // size
            }
        return None

    async def clear_session_conversations(
        self,
        session_id: str,
        model_filter: Optional[str] = None
    ) -> Dict[str, Any]:
        """清除会话的对话记录"""
        async with self.get_db_session() as db_session:
            # 验证会话存在
            session_query = select(Session).where(Session.id == session_id)
            session_result = await db_session.execute(session_query)
            session = session_result.scalar_one_or_none()
            
            if not session:
                raise ServiceError(
                    f"Session {session_id} not found",
                    error_code="SESSION_NOT_FOUND"
                )
            
            # 构建删除条件
            conditions = [Conversation.session_id == session_id]
            
            if model_filter:
                conditions.append(Conversation.model_name == model_filter)
            
            # 删除对话记录
            result = await db_session.execute(
                delete(Conversation).where(and_(*conditions))
            )
            
            deleted_count = result.rowcount
            
            # 重新计算会话统计
            await self._recalculate_session_stats(db_session, session_id)
            
            self.logger.info(
                f"Cleared {deleted_count} conversations from session {session_id}"
                + (f" for model {model_filter}" if model_filter else "")
            )
            
            return {
                "deleted_count": deleted_count,
                "session_id": session_id,
                "model_filter": model_filter
            }
        return None

    async def _recalculate_session_stats(self, db_session: AsyncSession, session_id: str) -> None:
        """重新计算会话统计信息"""
        # 查询会话的所有对话记录
        query = select(
            func.count(Conversation.id).label("message_count"),
            func.sum(Conversation.total_tokens).label("total_tokens"),
            func.sum(Conversation.cost).label("total_cost")
        ).where(Conversation.session_id == session_id)
        
        result = await db_session.execute(query)
        stats = result.first()
        
        # 更新会话统计
        await db_session.execute(
            update(Session)
            .where(Session.id == session_id)
            .values(
                total_messages=stats.message_count or 0,
                total_tokens=stats.total_tokens or 0,
                total_cost=stats.total_cost or 0.0,
                updated_at=datetime.utcnow()
            )
        )
    
    async def _validate_models(self, model_names: List[str]) -> None:
        """验证模型是否可用"""
        # 这里可以调用ModelService来验证模型
        # 暂时简单验证模型名称不为空
        if not model_names:
            raise ServiceError(
                "At least one model must be enabled",
                error_code="INVALID_MODEL_CONFIG"
            )
        
        for model_name in model_names:
            if not model_name or not isinstance(model_name, str):
                raise ServiceError(
                    f"Invalid model name: {model_name}",
                    error_code="INVALID_MODEL_NAME"
                )
    
    async def _load_active_sessions(self) -> None:
        """加载活跃会话到内存"""
        async with self.get_db_session() as db_session:
            query = select(Session).where(Session.status == SessionStatus.ACTIVE)
            result = await db_session.execute(query)
            sessions = result.scalars().all()
            
            for session in sessions:
                self._active_sessions[session.id] = {
                    "id": session.id,
                    "name": session.name,
                    "status": session.status,
                    "last_activity": session.last_activity_at.timestamp() if session.last_activity_at else time.time(),
                    "message_count": session.total_messages or 0
                }
    
    async def _save_active_sessions(self) -> None:
        """保存活跃会话状态"""
        # 这里可以实现将内存中的会话状态保存到数据库或缓存
        pass
    
    async def _cleanup_expired_sessions(self) -> None:
        """清理过期会话"""
        # 将长时间未活动的会话设为非活跃状态
        expiry_time = datetime.utcnow() - timedelta(hours=24)
        
        async with self.get_db_session() as db_session:
            await db_session.execute(
                update(Session)
                .where(
                    and_(
                        Session.status == SessionStatus.ACTIVE,
                        Session.last_activity_at < expiry_time
                    )
                )
                .values(status=SessionStatus.INACTIVE)
            )
    
    def get_active_session_count(self) -> int:
        """获取活跃会话数量"""
        return len(self._active_sessions)
    
    def is_session_active(self, session_id: str) -> bool:
        """检查会话是否活跃"""
        return session_id in self._active_sessions