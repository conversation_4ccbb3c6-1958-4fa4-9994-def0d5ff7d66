"""对话服务

负责管理对话记录，包括创建、查询、更新对话，以及处理消息的发送和接收。
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, AsyncGenerator
from uuid import uuid4

from sqlalchemy import select, delete, func, and_, desc

from api.models.schemas import (
    ConversationResponse, ConversationStatus,
    MessageRequest, MessageResponse, ModelResponse
)
from database.models import Conversation
from utils.performance_monitor import PerformanceMonitor
from .base_service import BaseService, ServiceError
from .model_service import ModelService
from .session_service import SessionService


class ConversationService(BaseService):
    """对话服务类
    
    提供对话管理的核心功能：
    - 对话的CRUD操作
    - 消息发送和接收
    - 多模型并行对话
    - 对话历史管理
    - 实时流式响应
    """
    
    def __init__(self):
        super().__init__("ConversationService")
        self.model_service: Optional[ModelService] = None
        self.session_service: Optional[SessionService] = None
        self.performance_monitor = PerformanceMonitor()
        self._active_conversations: Dict[str, Dict[str, Any]] = {}
    
    async def initialize(self) -> None:
        """初始化对话服务"""
        self.logger.info("Initializing ConversationService")
        
        # 初始化依赖服务
        self.model_service = ModelService()
        await self.model_service.initialize()
        
        self.session_service = SessionService()
        await self.session_service.initialize()
        
        # 加载活跃对话
        await self._load_active_conversations()
        
        self.logger.info(f"ConversationService initialized with {len(self._active_conversations)} active conversations")
    
    async def cleanup(self) -> None:
        """清理资源"""
        self.logger.info("Cleaning up ConversationService")
        
        # 清理依赖服务
        if self.model_service:
            await self.model_service.cleanup()
        
        if self.session_service:
            await self.session_service.cleanup()
        
        self._active_conversations.clear()
    
    async def send_message(
        self,
        session_id: str,
        message_request: MessageRequest,
        stream: bool = False
    ) -> MessageResponse:
        """发送消息并获取多模型响应
        
        Args:
            session_id: 会话ID
            message_request: 消息请求
            stream: 是否使用流式响应
        
        Returns:
            消息响应
        """
        return await self.execute_with_monitoring(
            "send_message",
            self._send_message_impl,
            session_id,
            message_request,
            stream
        )
    
    async def _send_message_impl(
        self,
        session_id: str,
        message_request: MessageRequest,
        stream: bool = False
    ) -> MessageResponse:
        """发送消息的实现"""
        start_time = time.time()
        
        # 验证会话存在
        session = await self.session_service.get_session(session_id)
        if not session:
            raise ServiceError(
                f"Session {session_id} not found",
                error_code="SESSION_NOT_FOUND"
            )
        
        # 获取启用的模型
        enabled_models = session.enabled_models
        if not enabled_models:
            raise ServiceError(
                "No models enabled for this session",
                error_code="NO_MODELS_ENABLED"
            )
        
        # 构建对话上下文
        context = await self._build_conversation_context(
            session_id,
            message_request.message,
            session.system_prompt
        )
        
        # 并行调用多个模型
        model_responses = await self._call_models_parallel(
            enabled_models,
            context,
            session.model_configs,
            stream
        )
        
        # 保存对话记录
        conversations = await self._save_conversations(
            session_id,
            message_request.message,
            model_responses,
            start_time
        )
        
        # 更新会话统计
        total_tokens = sum(resp.total_tokens for resp in model_responses.values())
        total_cost = sum(resp.cost for resp in model_responses.values())
        
        await self.session_service.update_session_stats(
            session_id,
            message_count_delta=len(model_responses),
            token_count_delta=total_tokens,
            cost_delta=total_cost
        )
        
        # 更新会话活动时间
        await self.session_service.update_session_activity(session_id)
        
        response_time = time.time() - start_time
        
        self.logger.info(
            f"Message sent to {len(model_responses)} models in {response_time:.2f}s"
        )
        
        return MessageResponse(
            message_id=str(uuid4()),
            session_id=session_id,
            user_message=message_request.message,
            model_responses=model_responses,
            total_tokens=total_tokens,
            total_cost=total_cost,
            response_time=response_time,
            timestamp=datetime.utcnow()
        )
    
    async def send_message_stream(
        self,
        session_id: str,
        message_request: MessageRequest
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """发送消息并返回流式响应
        
        Args:
            session_id: 会话ID
            message_request: 消息请求
        
        Yields:
            流式响应数据
        """
        start_time = time.time()
        
        # 验证会话存在
        session = await self.session_service.get_session(session_id)
        if not session:
            yield {
                "type": "error",
                "error": "Session not found",
                "error_code": "SESSION_NOT_FOUND"
            }
            return
        
        # 获取启用的模型
        enabled_models = session.enabled_models
        if not enabled_models:
            yield {
                "type": "error",
                "error": "No models enabled for this session",
                "error_code": "NO_MODELS_ENABLED"
            }
            return
        
        try:
            # 构建对话上下文
            context = await self._build_conversation_context(
                session_id,
                message_request.message,
                session.system_prompt
            )
            
            # 发送开始事件
            yield {
                "type": "start",
                "session_id": session_id,
                "models": enabled_models,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            # 并行流式调用模型
            model_responses = {}
            async for chunk in self._call_models_stream(
                enabled_models,
                context,
                session.model_configs
            ):
                yield chunk
                
                # 收集完整响应
                if chunk.get("type") == "complete":
                    model_name = chunk.get("model")
                    if model_name:
                        model_responses[model_name] = ModelResponse(
                            model_name=model_name,
                            response=chunk.get("response", ""),
                            status="completed",
                            response_time=chunk.get("response_time", 0),
                            total_tokens=chunk.get("total_tokens", 0),
                            prompt_tokens=chunk.get("prompt_tokens", 0),
                            completion_tokens=chunk.get("completion_tokens", 0),
                            cost=chunk.get("cost", 0.0),
                            error=None
                        )
            
            # 保存对话记录
            if model_responses:
                await self._save_conversations(
                    session_id,
                    message_request.message,
                    model_responses,
                    start_time
                )
                
                # 更新会话统计
                total_tokens = sum(resp.total_tokens for resp in model_responses.values())
                total_cost = sum(resp.cost for resp in model_responses.values())
                
                await self.session_service.update_session_stats(
                    session_id,
                    message_count_delta=len(model_responses),
                    token_count_delta=total_tokens,
                    cost_delta=total_cost
                )
                
                # 更新会话活动时间
                await self.session_service.update_session_activity(session_id)
            
            # 发送结束事件
            yield {
                "type": "end",
                "session_id": session_id,
                "total_time": time.time() - start_time,
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error in stream message: {e}")
            yield {
                "type": "error",
                "error": str(e),
                "error_code": "STREAM_ERROR"
            }
    
    async def get_conversation(
        self,
        conversation_id: str
    ) -> ConversationResponse:
        """获取对话详情
        
        Args:
            conversation_id: 对话ID
        
        Returns:
            对话详情
        """
        return await self.execute_with_monitoring(
            "get_conversation",
            self._get_conversation_impl,
            conversation_id
        )
    
    async def _get_conversation_impl(
        self,
        conversation_id: str
    ) -> ConversationResponse:
        """获取对话的实现"""
        async with self.get_db_session() as db_session:
            query = select(Conversation).where(Conversation.id == conversation_id)
            result = await db_session.execute(query)
            conversation = result.scalar_one_or_none()
            
            if not conversation:
                raise ServiceError(
                    f"Conversation {conversation_id} not found",
                    error_code="CONVERSATION_NOT_FOUND"
                )
            
            return ConversationResponse.from_orm(conversation)
        return None

    async def list_conversations(
        self,
        session_id: str,
        model_filter: Optional[str] = None,
        status_filter: Optional[ConversationStatus] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        page: int = 1,
        size: int = 50
    ) -> Dict[str, Any]:
        """获取对话列表
        
        Args:
            session_id: 会话ID
            model_filter: 模型过滤
            status_filter: 状态过滤
            start_date: 开始日期
            end_date: 结束日期
            page: 页码
            size: 每页大小
        
        Returns:
            对话列表
        """
        return await self.execute_with_monitoring(
            "list_conversations",
            self._list_conversations_impl,
            session_id, model_filter, status_filter, start_date, end_date, page, size
        )
    
    async def _list_conversations_impl(
        self,
        session_id: str,
        model_filter: Optional[str],
        status_filter: Optional[ConversationStatus],
        start_date: Optional[datetime],
        end_date: Optional[datetime],
        page: int,
        size: int
    ) -> Dict[str, Any]:
        """获取对话列表的实现"""
        async with self.get_db_session() as db_session:
            # 构建查询条件
            conditions = [Conversation.session_id == session_id]
            
            if model_filter:
                conditions.append(Conversation.model_name == model_filter)
            
            if status_filter:
                conditions.append(Conversation.status == status_filter)
            
            if start_date:
                conditions.append(Conversation.created_at >= start_date)
            
            if end_date:
                conditions.append(Conversation.created_at <= end_date)
            
            # 计算总数
            count_query = select(func.count(Conversation.id)).where(and_(*conditions))
            total_result = await db_session.execute(count_query)
            total = total_result.scalar()
            
            # 分页查询
            offset = (page - 1) * size
            query = (
                select(Conversation)
                .where(and_(*conditions))
                .order_by(desc(Conversation.created_at))
                .offset(offset)
                .limit(size)
            )
            
            result = await db_session.execute(query)
            conversations = result.scalars().all()
            
            # 转换为响应格式
            conversation_responses = [
                ConversationResponse.from_orm(conv) for conv in conversations
            ]
            
            return {
                "conversations": conversation_responses,
                "total": total,
                "page": page,
                "size": size,
                "pages": (total + size - 1) // size
            }
        return None

    async def delete_conversation(self, conversation_id: str) -> bool:
        """删除对话
        
        Args:
            conversation_id: 对话ID
        
        Returns:
            是否删除成功
        """
        return await self.execute_with_monitoring(
            "delete_conversation",
            self._delete_conversation_impl,
            conversation_id
        )
    
    async def _delete_conversation_impl(self, conversation_id: str) -> bool:
        """删除对话的实现"""
        async with self.get_db_session() as db_session:
            # 获取对话信息用于更新会话统计
            query = select(Conversation).where(Conversation.id == conversation_id)
            result = await db_session.execute(query)
            conversation = result.scalar_one_or_none()
            
            if not conversation:
                raise ServiceError(
                    f"Conversation {conversation_id} not found",
                    error_code="CONVERSATION_NOT_FOUND"
                )
            
            # 删除对话
            await db_session.execute(
                delete(Conversation).where(Conversation.id == conversation_id)
            )
            
            # 更新会话统计
            await self.session_service.update_session_stats(
                conversation.session_id,
                message_count_delta=-1,
                token_count_delta=-(conversation.total_tokens or 0),
                cost_delta=-(conversation.cost or 0.0)
            )
            
            self.logger.info(f"Deleted conversation: {conversation_id}")
            
            return True
        return None

    async def _build_conversation_context(
        self,
        session_id: str,
        current_message: str,
        system_prompt: Optional[str] = None
    ) -> List[Dict[str, str]]:
        """构建对话上下文
        
        Args:
            session_id: 会话ID
            current_message: 当前消息
            system_prompt: 系统提示词
        
        Returns:
            对话上下文列表
        """
        context = []
        
        # 添加系统提示词
        if system_prompt:
            context.append({
                "role": "system",
                "content": system_prompt
            })
        
        # 获取历史对话（最近的N条）
        async with self.get_db_session() as db_session:
            query = (
                select(Conversation)
                .where(Conversation.session_id == session_id)
                .order_by(desc(Conversation.created_at))
                .limit(10)  # 限制历史对话数量
            )
            
            result = await db_session.execute(query)
            conversations = result.scalars().all()
            
            # 按时间正序排列
            conversations = list(reversed(conversations))
            
            # 添加历史对话到上下文
            for conv in conversations:
                if conv.user_message:
                    context.append({
                        "role": "user",
                        "content": conv.user_message
                    })
                
                if conv.assistant_message:
                    context.append({
                        "role": "assistant",
                        "content": conv.assistant_message
                    })
        
        # 添加当前消息
        context.append({
            "role": "user",
            "content": current_message
        })
        
        return context
    
    async def _call_models_parallel(
        self,
        model_names: List[str],
        context: List[Dict[str, str]],
        model_configs: Dict[str, Any],
        stream: bool = False
    ) -> Dict[str, ModelResponse]:
        """并行调用多个模型
        
        Args:
            model_names: 模型名称列表
            context: 对话上下文
            model_configs: 模型配置
            stream: 是否流式响应
        
        Returns:
            模型响应字典
        """
        tasks = []
        
        for model_name in model_names:
            config = model_configs.get(model_name, {})
            task = self.model_service.chat_completion(
                model_name=model_name,
                messages=context,
                **config
            )
            tasks.append((model_name, task))
        
        # 并行执行
        results = await asyncio.gather(
            *[task for _, task in tasks],
            return_exceptions=True
        )
        
        # 处理结果
        model_responses = {}
        for (model_name, _), result in zip(tasks, results):
            if isinstance(result, Exception):
                self.logger.error(f"Error calling model {model_name}: {result}")
                model_responses[model_name] = ModelResponse(
                    model_name=model_name,
                    response="",
                    status="error",
                    response_time=0,
                    total_tokens=0,
                    prompt_tokens=0,
                    completion_tokens=0,
                    cost=0.0,
                    error=str(result)
                )
            else:
                model_responses[model_name] = result
        
        return model_responses
    
    async def _call_models_stream(
        self,
        model_names: List[str],
        context: List[Dict[str, str]],
        model_configs: Dict[str, Any]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """并行流式调用多个模型
        
        Args:
            model_names: 模型名称列表
            context: 对话上下文
            model_configs: 模型配置
        
        Yields:
            流式响应数据
        """
        # 创建并行任务
        tasks = []
        for model_name in model_names:
            config = model_configs.get(model_name, {})
            task = asyncio.create_task(
                self._stream_single_model(
                    model_name,
                    context,
                    config
                )
            )
            tasks.append(task)
        
        # 收集所有流式响应
        completed_models = set()
        
        while len(completed_models) < len(model_names):
            # 检查每个任务的状态
            for i, task in enumerate(tasks):
                model_name = model_names[i]
                
                if model_name in completed_models:
                    continue
                
                if task.done():
                    try:
                        async for chunk in task.result():
                            yield chunk
                        completed_models.add(model_name)
                    except Exception as e:
                        self.logger.error(f"Error in stream for model {model_name}: {e}")
                        yield {
                            "type": "error",
                            "model": model_name,
                            "error": str(e)
                        }
                        completed_models.add(model_name)
            
            # 短暂等待避免忙等待
            await asyncio.sleep(0.01)
    
    async def _stream_single_model(
        self,
        model_name: str,
        context: List[Dict[str, str]],
        config: Dict[str, Any]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """单个模型的流式响应
        
        Args:
            model_name: 模型名称
            context: 对话上下文
            config: 模型配置
        
        Yields:
            流式响应数据
        """
        try:
            async for chunk in self.model_service.chat_completion_stream(
                model_name=model_name,
                messages=context,
                **config
            ):
                # 添加模型标识
                chunk["model"] = model_name
                yield chunk
        except Exception as e:
            self.logger.error(f"Error in stream for model {model_name}: {e}")
            yield {
                "type": "error",
                "model": model_name,
                "error": str(e)
            }
    
    async def _save_conversations(
        self,
        session_id: str,
        user_message: str,
        model_responses: Dict[str, ModelResponse],
        start_time: float
    ) -> List[Conversation]:
        """保存对话记录
        
        Args:
            session_id: 会话ID
            user_message: 用户消息
            model_responses: 模型响应
            start_time: 开始时间
        
        Returns:
            保存的对话记录列表
        """
        conversations = []
        now = datetime.utcnow()
        
        async with self.get_db_session() as db_session:
            for model_name, response in model_responses.items():
                conversation = Conversation(
                    id=str(uuid4()),
                    session_id=session_id,
                    model_name=model_name,
                    user_message=user_message,
                    assistant_message=response.response,
                    status=ConversationStatus.COMPLETED if response.status == "completed" else ConversationStatus.ERROR,
                    response_time=response.response_time,
                    total_tokens=response.total_tokens,
                    prompt_tokens=response.prompt_tokens,
                    completion_tokens=response.completion_tokens,
                    cost=response.cost,
                    error_message=response.error,
                    created_at=now,
                    updated_at=now
                )
                
                db_session.add(conversation)
                conversations.append(conversation)
            
            await db_session.flush()
        
        return conversations
    
    async def _load_active_conversations(self) -> None:
        """加载活跃对话"""
        # 加载最近的对话到内存缓存
        cutoff_time = datetime.utcnow() - timedelta(hours=1)
        
        async with self.get_db_session() as db_session:
            query = (
                select(Conversation)
                .where(Conversation.created_at >= cutoff_time)
                .order_by(desc(Conversation.created_at))
                .limit(1000)
            )
            
            result = await db_session.execute(query)
            conversations = result.scalars().all()
            
            for conv in conversations:
                self._active_conversations[conv.id] = {
                    "id": conv.id,
                    "session_id": conv.session_id,
                    "model_name": conv.model_name,
                    "status": conv.status,
                    "created_at": conv.created_at.timestamp()
                }
    
    def get_active_conversation_count(self) -> int:
        """获取活跃对话数量"""
        return len(self._active_conversations)
    
    async def get_conversation_stats(
        self,
        session_id: Optional[str] = None,
        model_name: Optional[str] = None,
        days: int = 7
    ) -> Dict[str, Any]:
        """获取对话统计信息
        
        Args:
            session_id: 会话ID过滤
            model_name: 模型名称过滤
            days: 统计天数
        
        Returns:
            统计信息
        """
        start_date = datetime.utcnow() - timedelta(days=days)
        
        async with self.get_db_session() as db_session:
            # 构建查询条件
            conditions = [Conversation.created_at >= start_date]
            
            if session_id:
                conditions.append(Conversation.session_id == session_id)
            
            if model_name:
                conditions.append(Conversation.model_name == model_name)
            
            # 基础统计
            query = select(
                func.count(Conversation.id).label("total_conversations"),
                func.sum(Conversation.total_tokens).label("total_tokens"),
                func.sum(Conversation.cost).label("total_cost"),
                func.avg(Conversation.response_time).label("avg_response_time")
            ).where(and_(*conditions))
            
            result = await db_session.execute(query)
            stats = result.first()
            
            # 按状态统计
            status_query = select(
                Conversation.status,
                func.count(Conversation.id).label("count")
            ).where(and_(*conditions)).group_by(Conversation.status)
            
            status_result = await db_session.execute(status_query)
            status_stats = {row.status: row.count for row in status_result}
            
            # 按模型统计
            model_query = select(
                Conversation.model_name,
                func.count(Conversation.id).label("count"),
                func.avg(Conversation.response_time).label("avg_response_time"),
                func.sum(Conversation.cost).label("total_cost")
            ).where(and_(*conditions)).group_by(Conversation.model_name)
            
            model_result = await db_session.execute(model_query)
            model_stats = {
                row.model_name: {
                    "count": row.count,
                    "avg_response_time": float(row.avg_response_time or 0),
                    "total_cost": float(row.total_cost or 0)
                }
                for row in model_result
            }
            
            return {
                "total_conversations": stats.total_conversations or 0,
                "total_tokens": stats.total_tokens or 0,
                "total_cost": float(stats.total_cost or 0),
                "avg_response_time": float(stats.avg_response_time or 0),
                "status_distribution": status_stats,
                "model_distribution": model_stats,
                "period_days": days
            }
        return None