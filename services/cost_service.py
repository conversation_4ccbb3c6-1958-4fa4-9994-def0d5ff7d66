"""成本服务模块

提供模型使用成本计算、分析和预警功能。
"""

import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict
from decimal import Decimal, ROUND_HALF_UP

from sqlalchemy.orm import Session
from sqlalchemy import func, and_, desc
from loguru import logger

from database.models import Session as SessionModel, Conversation, Prompt
from api.models.schemas import CostAnalysis, ModelCostBreakdown, CostAlert, CostForecast


class CostCalculator:
    """成本计算器"""
    
    # 模型定价配置（每1000个token的价格，单位：美元）
    MODEL_PRICING = {
        "gpt-4": {"input": 0.03, "output": 0.06},
        "gpt-4-turbo": {"input": 0.01, "output": 0.03},
        "gpt-3.5-turbo": {"input": 0.0015, "output": 0.002},
        "claude-3-opus": {"input": 0.015, "output": 0.075},
        "claude-3-sonnet": {"input": 0.003, "output": 0.015},
        "claude-3-haiku": {"input": 0.00025, "output": 0.00125},
        "gemini-pro": {"input": 0.0005, "output": 0.0015},
        "gemini-pro-vision": {"input": 0.0005, "output": 0.0015},
        "qianwen-turbo": {"input": 0.0008, "output": 0.002},
        "qianwen-plus": {"input": 0.004, "output": 0.012},
        "doubao-pro": {"input": 0.0008, "output": 0.002},
        "doubao-lite": {"input": 0.0003, "output": 0.0006}
    }
    
    # 默认定价（未知模型）
    DEFAULT_PRICING = {"input": 0.001, "output": 0.002}
    
    # 成本预警阈值配置
    ALERT_THRESHOLDS = {
        "daily_limit": 100.0,  # 日成本限制（美元）
        "monthly_limit": 2000.0,  # 月成本限制（美元）
        "model_daily_limit": 50.0,  # 单模型日成本限制（美元）
        "session_limit": 10.0,  # 单会话成本限制（美元）
        "growth_rate_threshold": 0.5  # 成本增长率阈值（50%）
    }
    
    def __init__(self, db: Session):
        self.db = db
    
    def calculate_conversation_cost(self, conversation: Conversation) -> Decimal:
        """计算单次对话成本"""
        if not conversation.token_count:
            return Decimal('0')
        
        model_name = conversation.model_name.lower()
        pricing = self.MODEL_PRICING.get(model_name, self.DEFAULT_PRICING)
        
        # 估算输入和输出token比例（通常输入占30%，输出占70%）
        total_tokens = conversation.token_count
        input_tokens = int(total_tokens * 0.3)
        output_tokens = total_tokens - input_tokens
        
        # 计算成本
        input_cost = (input_tokens / 1000) * pricing["input"]
        output_cost = (output_tokens / 1000) * pricing["output"]
        
        total_cost = Decimal(str(input_cost + output_cost))
        return total_cost.quantize(Decimal('0.000001'), rounding=ROUND_HALF_UP)
    
    def analyze_costs(
        self,
        days: int = 30,
        session_id: Optional[str] = None,
        model_name: Optional[str] = None
    ) -> CostAnalysis:
        """分析成本统计"""
        try:
            # 设置时间范围
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=days)
            
            # 构建查询
            query = self.db.query(Conversation).filter(
                Conversation.timestamp >= start_date,
                Conversation.timestamp <= end_date
            )
            
            if session_id:
                query = query.filter(Conversation.session_id == session_id)
            
            if model_name:
                query = query.filter(Conversation.model_name == model_name)
            
            conversations = query.all()
            
            # 计算总成本
            total_cost = sum(self.calculate_conversation_cost(conv) for conv in conversations)
            
            # 按模型分组计算成本
            model_costs = self._calculate_model_costs(conversations)
            
            # 按日期分组计算成本趋势
            daily_costs = self._calculate_daily_costs(conversations)
            
            # 计算预测成本
            forecast = self._calculate_cost_forecast(daily_costs, days=7)
            
            # 生成成本预警
            alerts = self._generate_cost_alerts(total_cost, model_costs, daily_costs, days)
            
            # 计算统计指标
            avg_daily_cost = total_cost / days if days > 0 else Decimal('0')
            avg_conversation_cost = total_cost / len(conversations) if conversations else Decimal('0')
            
            return CostAnalysis(
                analysis_period={
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "days": days
                },
                total_cost=float(total_cost),
                avg_daily_cost=float(avg_daily_cost),
                avg_conversation_cost=float(avg_conversation_cost),
                model_breakdown=model_costs,
                daily_trend=daily_costs,
                cost_forecast=forecast,
                alerts=alerts,
                currency="USD",
                generated_at=datetime.utcnow().isoformat()
            )
            
        except Exception as e:
            logger.error(f"成本分析失败: {str(e)}")
            raise
    
    def _calculate_model_costs(self, conversations: List[Conversation]) -> List[ModelCostBreakdown]:
        """计算各模型成本分解"""
        model_stats = defaultdict(lambda: {
            "total_cost": Decimal('0'),
            "conversation_count": 0,
            "total_tokens": 0,
            "avg_cost_per_conversation": Decimal('0'),
            "avg_cost_per_1k_tokens": Decimal('0')
        })
        
        for conv in conversations:
            model = conv.model_name
            cost = self.calculate_conversation_cost(conv)
            
            model_stats[model]["total_cost"] += cost
            model_stats[model]["conversation_count"] += 1
            if conv.token_count:
                model_stats[model]["total_tokens"] += conv.token_count
        
        # 计算平均值
        breakdowns = []
        for model, stats in model_stats.items():
            conv_count = stats["conversation_count"]
            total_tokens = stats["total_tokens"]
            
            if conv_count > 0:
                stats["avg_cost_per_conversation"] = stats["total_cost"] / conv_count
            
            if total_tokens > 0:
                stats["avg_cost_per_1k_tokens"] = (stats["total_cost"] / total_tokens) * 1000
            
            breakdowns.append(ModelCostBreakdown(
                model_name=model,
                total_cost=float(stats["total_cost"]),
                conversation_count=conv_count,
                avg_cost_per_conversation=float(stats["avg_cost_per_conversation"]),
                avg_cost_per_1k_tokens=float(stats["avg_cost_per_1k_tokens"]),
                total_tokens=total_tokens
            ))
        
        return sorted(breakdowns, key=lambda x: x.total_cost, reverse=True)
    
    def _calculate_daily_costs(self, conversations: List[Conversation]) -> List[Dict[str, Any]]:
        """计算每日成本趋势"""
        daily_costs = defaultdict(Decimal)
        
        for conv in conversations:
            if conv.timestamp:
                date = conv.timestamp.strftime("%Y-%m-%d")
                cost = self.calculate_conversation_cost(conv)
                daily_costs[date] += cost
        
        return [
            {"date": date, "cost": float(cost)}
            for date, cost in sorted(daily_costs.items())
        ]
    
    def _calculate_cost_forecast(self, daily_costs: List[Dict[str, Any]], days: int = 7) -> CostForecast:
        """计算成本预测"""
        if len(daily_costs) < 3:
            return CostForecast(
                forecast_days=days,
                predicted_daily_costs=[],
                predicted_total_cost=0.0,
                confidence_level=0.0,
                trend="insufficient_data"
            )
        
        # 简单的线性趋势预测
        recent_costs = [item["cost"] for item in daily_costs[-7:]]  # 最近7天
        avg_cost = sum(recent_costs) / len(recent_costs)
        
        # 计算趋势
        if len(recent_costs) >= 2:
            trend_slope = (recent_costs[-1] - recent_costs[0]) / (len(recent_costs) - 1)
        else:
            trend_slope = 0
        
        # 预测未来成本
        predicted_costs = []
        for i in range(days):
            predicted_cost = max(0, avg_cost + (trend_slope * i))
            predicted_costs.append({
                "date": (datetime.utcnow() + timedelta(days=i+1)).strftime("%Y-%m-%d"),
                "predicted_cost": predicted_cost
            })
        
        predicted_total = sum(item["predicted_cost"] for item in predicted_costs)
        
        # 确定趋势方向
        if trend_slope > 0.1:
            trend = "increasing"
        elif trend_slope < -0.1:
            trend = "decreasing"
        else:
            trend = "stable"
        
        # 简单的置信度计算（基于数据量和变异性）
        confidence = min(0.9, len(daily_costs) / 30)  # 数据越多置信度越高
        
        return CostForecast(
            forecast_days=days,
            predicted_daily_costs=predicted_costs,
            predicted_total_cost=predicted_total,
            confidence_level=confidence,
            trend=trend
        )
    
    def _generate_cost_alerts(self, total_cost: Decimal, model_costs: List[ModelCostBreakdown], daily_costs: List[Dict[str, Any]], period_days: int) -> List[CostAlert]:
        """生成成本预警"""
        alerts = []
        
        # 检查总成本是否超过阈值
        daily_avg = float(total_cost) / period_days if period_days > 0 else 0
        monthly_projection = daily_avg * 30
        
        if daily_avg > self.ALERT_THRESHOLDS["daily_limit"]:
            alerts.append(CostAlert(
                alert_type="daily_limit_exceeded",
                severity="high",
                message=f"日均成本 ${daily_avg:.2f} 超过限制 ${self.ALERT_THRESHOLDS['daily_limit']}",
                current_value=daily_avg,
                threshold=self.ALERT_THRESHOLDS["daily_limit"],
                recommendation="考虑优化模型使用或调整使用策略"
            ))
        
        if monthly_projection > self.ALERT_THRESHOLDS["monthly_limit"]:
            alerts.append(CostAlert(
                alert_type="monthly_projection_high",
                severity="medium",
                message=f"月度成本预计 ${monthly_projection:.2f} 可能超过限制 ${self.ALERT_THRESHOLDS['monthly_limit']}",
                current_value=monthly_projection,
                threshold=self.ALERT_THRESHOLDS["monthly_limit"],
                recommendation="建议监控使用量并考虑成本控制措施"
            ))
        
        # 检查单个模型成本
        for model in model_costs:
            model_daily_avg = model.total_cost / period_days if period_days > 0 else 0
            if model_daily_avg > self.ALERT_THRESHOLDS["model_daily_limit"]:
                alerts.append(CostAlert(
                    alert_type="model_cost_high",
                    severity="medium",
                    message=f"模型 {model.model_name} 日均成本 ${model_daily_avg:.2f} 较高",
                    current_value=model_daily_avg,
                    threshold=self.ALERT_THRESHOLDS["model_daily_limit"],
                    recommendation=f"考虑使用更经济的模型替代 {model.model_name}"
                ))
        
        # 检查成本增长趋势
        if len(daily_costs) >= 7:
            recent_avg = sum(item["cost"] for item in daily_costs[-3:]) / 3
            previous_avg = sum(item["cost"] for item in daily_costs[-7:-3]) / 4
            
            if previous_avg > 0:
                growth_rate = (recent_avg - previous_avg) / previous_avg
                if growth_rate > self.ALERT_THRESHOLDS["growth_rate_threshold"]:
                    alerts.append(CostAlert(
                        alert_type="cost_growth_high",
                        severity="medium",
                        message=f"成本增长率 {growth_rate:.1%} 过高",
                        current_value=growth_rate,
                        threshold=self.ALERT_THRESHOLDS["growth_rate_threshold"],
                        recommendation="分析成本增长原因并采取控制措施"
                    ))
        
        return alerts
    
    def get_session_cost(self, session_id: str) -> Dict[str, Any]:
        """获取特定会话的成本"""
        conversations = self.db.query(Conversation).filter(
            Conversation.session_id == session_id
        ).all()
        
        total_cost = sum(self.calculate_conversation_cost(conv) for conv in conversations)
        
        return {
            "session_id": session_id,
            "total_cost": float(total_cost),
            "conversation_count": len(conversations),
            "avg_cost_per_conversation": float(total_cost / len(conversations)) if conversations else 0,
            "models_used": list(set(conv.model_name for conv in conversations))
        }
    
    def update_pricing(self, model_name: str, input_price: float, output_price: float):
        """更新模型定价"""
        self.MODEL_PRICING[model_name.lower()] = {
            "input": input_price,
            "output": output_price
        }
        logger.info(f"Updated pricing for {model_name}: input=${input_price}, output=${output_price}")
    
    def get_pricing_info(self) -> Dict[str, Dict[str, float]]:
        """获取当前定价信息"""
        return self.MODEL_PRICING.copy()


def get_cost_calculator(db: Session) -> CostCalculator:
    """获取成本计算器实例"""
    return CostCalculator(db)