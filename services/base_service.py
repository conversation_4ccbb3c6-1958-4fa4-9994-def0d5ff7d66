"""基础服务类

定义所有服务的通用接口和功能，包括错误处理、日志记录、健康检查等。
"""

import time
from abc import ABC, abstractmethod
from contextlib import asynccontextmanager
from typing import Any, Dict

from loguru import logger
from database.connection import get_async_session


class ServiceError(Exception):
    """服务层异常基类"""
    
    def __init__(self, message: str, error_code: str = None, details: Dict[str, Any] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or "SERVICE_ERROR"
        self.details = details or {}
        self.timestamp = time.time()


class BaseService(ABC):
    """基础服务类
    
    提供所有服务的通用功能：
    - 数据库会话管理
    - 错误处理和日志记录
    - 性能监控
    - 健康检查
    """
    
    def __init__(self, logger_name: str = None):
        self.logger = logger.bind(name=logger_name or self.__class__.__name__)
        self._start_time = time.time()
        self._request_count = 0
        self._error_count = 0
        
    @asynccontextmanager
    async def get_db_session(self):
        """获取数据库会话的上下文管理器"""
        async with get_async_session() as session:
            try:
                yield session
                await session.commit()
            except Exception as e:
                await session.rollback()
                self.logger.error(f"Database transaction failed: {e}")
                raise
    
    async def execute_with_monitoring(self, operation_name: str, func, *args, **kwargs):
        """执行操作并监控性能"""
        start_time = time.time()
        self._request_count += 1
        
        try:
            self.logger.debug(f"Starting operation: {operation_name}")
            result = await func(*args, **kwargs)
            
            duration = time.time() - start_time
            self.logger.info(
                f"Operation completed: {operation_name}, "
                f"duration: {duration:.3f}s"
            )
            
            return result
            
        except Exception as e:
            self._error_count += 1
            duration = time.time() - start_time
            
            self.logger.error(
                f"Operation failed: {operation_name}, "
                f"duration: {duration:.3f}s, error: {str(e)}"
            )
            
            # 包装为服务异常
            if isinstance(e, ServiceError):
                raise
            else:
                raise ServiceError(
                    f"Operation {operation_name} failed: {str(e)}",
                    error_code="OPERATION_FAILED",
                    details={"operation": operation_name, "duration": duration}
                )
    
    def log_performance_metrics(self) -> Dict[str, Any]:
        """记录性能指标"""
        uptime = time.time() - self._start_time
        success_rate = (
            (self._request_count - self._error_count) / self._request_count 
            if self._request_count > 0 else 0
        )
        
        metrics = {
            "service_name": self.__class__.__name__,
            "uptime_seconds": uptime,
            "total_requests": self._request_count,
            "error_count": self._error_count,
            "success_rate": success_rate,
            "requests_per_second": self._request_count / uptime if uptime > 0 else 0
        }
        
        self.logger.info(f"Performance metrics: {metrics}")
        return metrics
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 检查数据库连接
            async with self.get_db_session() as session:
                await session.execute("SELECT 1")
            
            db_healthy = True
            db_error = None
        except Exception as e:
            db_healthy = False
            db_error = str(e)
        
        # 检查服务状态
        service_healthy = self._error_count / max(self._request_count, 1) < 0.1
        
        health_status = {
            "service_name": self.__class__.__name__,
            "healthy": db_healthy and service_healthy,
            "database": {
                "healthy": db_healthy,
                "error": db_error
            },
            "service": {
                "healthy": service_healthy,
                "uptime_seconds": time.time() - self._start_time,
                "total_requests": self._request_count,
                "error_count": self._error_count
            },
            "timestamp": time.time()
        }
        
        return health_status
    
    @abstractmethod
    async def initialize(self) -> None:
        """初始化服务"""
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """清理资源"""
        pass
    
    def validate_input(self, data: Dict[str, Any], required_fields: list) -> None:
        """验证输入数据"""
        missing_fields = [field for field in required_fields if field not in data]
        if missing_fields:
            raise ServiceError(
                f"Missing required fields: {missing_fields}",
                error_code="INVALID_INPUT",
                details={"missing_fields": missing_fields}
            )
    
    def sanitize_output(self, data: Dict[str, Any], sensitive_fields: list = None) -> Dict[str, Any]:
        """清理输出数据，移除敏感信息"""
        if not sensitive_fields:
            sensitive_fields = ["password", "api_key", "secret", "token"]
        
        sanitized = data.copy()
        for field in sensitive_fields:
            if field in sanitized:
                sanitized[field] = "***REDACTED***"
        
        return sanitized
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.cleanup()
        
        if exc_type is not None:
            self.logger.error(
                f"Service exited with exception: {exc_type.__name__}: {exc_val}"
            )