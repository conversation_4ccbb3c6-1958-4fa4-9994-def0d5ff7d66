"""用户行为分析服务"""

from collections import defaultdict, Counter
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

from loguru import logger
from sqlalchemy.orm import Session

from api.models.schemas import UserBehaviorAnalysis, SessionPattern, ModelUsagePattern, TimePattern
from database.models import Session as SessionModel, Conversation, Prompt


class UserBehaviorAnalyzer:
    """用户行为分析器"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def analyze_user_behavior(
        self,
        days: int = 30,
        session_id: Optional[str] = None
    ) -> UserBehaviorAnalysis:
        """分析用户行为"""
        try:
            # 设置时间范围
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=days)
            
            # 基础查询条件
            base_query = self.db.query(SessionModel).filter(
                SessionModel.created_at >= start_date,
                SessionModel.created_at <= end_date
            )
            
            if session_id:
                base_query = base_query.filter(SessionModel.id == session_id)
            
            sessions = base_query.all()
            
            # 分析会话模式
            session_patterns = self._analyze_session_patterns(sessions)
            
            # 分析模型使用模式
            model_usage = self._analyze_model_usage(sessions)
            
            # 分析时间模式
            time_patterns = self._analyze_time_patterns(sessions)
            
            # 分析提示词使用
            prompt_usage = self._analyze_prompt_usage(sessions)
            
            # 分析对话质量
            conversation_quality = self._analyze_conversation_quality(sessions)
            
            # 生成洞察和建议
            insights = self._generate_insights(sessions, model_usage, time_patterns)
            
            return UserBehaviorAnalysis(
                analysis_period={
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "days": days
                },
                session_patterns=session_patterns,
                model_usage_patterns=model_usage,
                time_patterns=time_patterns,
                prompt_usage=prompt_usage,
                conversation_quality=conversation_quality,
                insights=insights,
                generated_at=datetime.utcnow().isoformat()
            )
            
        except Exception as e:
            logger.error(f"用户行为分析失败: {str(e)}")
            raise
    
    def _analyze_session_patterns(self, sessions: List[SessionModel]) -> SessionPattern:
        """分析会话模式"""
        if not sessions:
            return SessionPattern(
                total_sessions=0,
                avg_session_duration=0,
                avg_messages_per_session=0,
                avg_tokens_per_session=0,
                session_status_distribution={},
                most_used_models=[],
                session_length_distribution={}
            )
        
        # 基础统计
        total_sessions = len(sessions)
        total_duration = sum(s.duration_minutes for s in sessions)
        total_messages = sum(s.total_messages for s in sessions)
        total_tokens = sum(s.total_tokens for s in sessions)
        
        # 状态分布
        status_dist = Counter(s.status for s in sessions)
        
        # 最常用模型
        model_counter = Counter()
        for session in sessions:
            for model in session.models:
                model_counter[model] += 1
        
        # 会话长度分布
        length_dist = {
            "short": 0,  # < 5分钟
            "medium": 0,  # 5-30分钟
            "long": 0,   # 30-120分钟
            "very_long": 0  # > 120分钟
        }
        
        for session in sessions:
            duration = session.duration_minutes
            if duration < 5:
                length_dist["short"] += 1
            elif duration < 30:
                length_dist["medium"] += 1
            elif duration < 120:
                length_dist["long"] += 1
            else:
                length_dist["very_long"] += 1
        
        return SessionPattern(
            total_sessions=total_sessions,
            avg_session_duration=total_duration / total_sessions if total_sessions > 0 else 0,
            avg_messages_per_session=total_messages / total_sessions if total_sessions > 0 else 0,
            avg_tokens_per_session=total_tokens / total_sessions if total_sessions > 0 else 0,
            session_status_distribution=dict(status_dist),
            most_used_models=model_counter.most_common(5),
            session_length_distribution=length_dist
        )
    
    def _analyze_model_usage(self, sessions: List[SessionModel]) -> List[ModelUsagePattern]:
        """分析模型使用模式"""
        model_stats = defaultdict(lambda: {
            "usage_count": 0,
            "total_tokens": 0,
            "total_response_time": 0,
            "success_count": 0,
            "error_count": 0,
            "avg_response_time": 0,
            "success_rate": 0
        })
        
        # 统计每个模型的使用情况
        for session in sessions:
            conversations = self.db.query(Conversation).filter(
                Conversation.session_id == session.id
            ).all()
            
            for conv in conversations:
                model = conv.model_name
                model_stats[model]["usage_count"] += 1
                
                if conv.token_count:
                    model_stats[model]["total_tokens"] += conv.token_count
                
                if conv.response_time:
                    model_stats[model]["total_response_time"] += conv.response_time
                
                if conv.status == "success":
                    model_stats[model]["success_count"] += 1
                else:
                    model_stats[model]["error_count"] += 1
        
        # 计算平均值和成功率
        patterns = []
        for model, stats in model_stats.items():
            usage_count = stats["usage_count"]
            if usage_count > 0:
                stats["avg_response_time"] = stats["total_response_time"] / usage_count
                stats["success_rate"] = stats["success_count"] / usage_count
            
            patterns.append(ModelUsagePattern(
                model_name=model,
                usage_count=usage_count,
                avg_tokens_per_request=stats["total_tokens"] / usage_count if usage_count > 0 else 0,
                avg_response_time=stats["avg_response_time"],
                success_rate=stats["success_rate"],
                error_rate=stats["error_count"] / usage_count if usage_count > 0 else 0
            ))
        
        return sorted(patterns, key=lambda x: x.usage_count, reverse=True)
    
    def _analyze_time_patterns(self, sessions: List[SessionModel]) -> TimePattern:
        """分析时间模式"""
        if not sessions:
            return TimePattern(
                hourly_distribution={},
                daily_distribution={},
                peak_hours=[],
                peak_days=[],
                activity_trends={}
            )
        
        # 按小时统计
        hourly_dist = defaultdict(int)
        daily_dist = defaultdict(int)
        
        for session in sessions:
            if session.created_at:
                hour = session.created_at.hour
                day = session.created_at.strftime("%A")
                hourly_dist[hour] += 1
                daily_dist[day] += 1
        
        # 找出峰值时间
        peak_hours = sorted(hourly_dist.items(), key=lambda x: x[1], reverse=True)[:3]
        peak_days = sorted(daily_dist.items(), key=lambda x: x[1], reverse=True)[:3]
        
        # 活动趋势（按日期）
        activity_trends = defaultdict(int)
        for session in sessions:
            if session.created_at:
                date = session.created_at.strftime("%Y-%m-%d")
                activity_trends[date] += 1
        
        return TimePattern(
            hourly_distribution=dict(hourly_dist),
            daily_distribution=dict(daily_dist),
            peak_hours=[{"hour": h, "count": c} for h, c in peak_hours],
            peak_days=[{"day": d, "count": c} for d, c in peak_days],
            activity_trends=dict(activity_trends)
        )
    
    def _analyze_prompt_usage(self, sessions: List[SessionModel]) -> Dict[str, Any]:
        """分析提示词使用模式"""
        prompt_stats = defaultdict(int)
        category_stats = defaultdict(int)
        
        for session in sessions:
            if session.system_prompt_id:
                prompt = self.db.query(Prompt).filter(
                    Prompt.id == session.system_prompt_id
                ).first()
                
                if prompt:
                    prompt_stats[prompt.name] += 1
                    if prompt.category:
                        category_stats[prompt.category] += 1
        
        return {
            "most_used_prompts": dict(Counter(prompt_stats).most_common(10)),
            "category_distribution": dict(category_stats),
            "custom_prompt_usage": sum(1 for s in sessions if s.system_prompt and not s.system_prompt_id)
        }
    
    def _analyze_conversation_quality(self, sessions: List[SessionModel]) -> Dict[str, Any]:
        """分析对话质量"""
        total_conversations = 0
        successful_conversations = 0
        total_response_time = 0
        response_time_count = 0
        
        for session in sessions:
            conversations = self.db.query(Conversation).filter(
                Conversation.session_id == session.id
            ).all()
            
            for conv in conversations:
                total_conversations += 1
                if conv.status == "success":
                    successful_conversations += 1
                
                if conv.response_time:
                    total_response_time += conv.response_time
                    response_time_count += 1
        
        return {
            "total_conversations": total_conversations,
            "success_rate": successful_conversations / total_conversations if total_conversations > 0 else 0,
            "avg_response_time": total_response_time / response_time_count if response_time_count > 0 else 0,
            "error_rate": (total_conversations - successful_conversations) / total_conversations if total_conversations > 0 else 0
        }
    
    def _generate_insights(self, sessions: List[SessionModel], model_usage: List[ModelUsagePattern], time_patterns: TimePattern) -> List[str]:
        """生成洞察和建议"""
        insights = []
        
        if not sessions:
            insights.append("暂无足够数据进行分析")
            return insights
        
        # 会话活跃度洞察
        active_sessions = sum(1 for s in sessions if s.status == "active")
        if active_sessions / len(sessions) > 0.8:
            insights.append("用户会话活跃度很高，建议优化长时间会话的性能")
        
        # 模型使用洞察
        if model_usage:
            best_model = model_usage[0]
            if best_model.success_rate > 0.95:
                insights.append(f"{best_model.model_name} 表现优异，成功率达到 {best_model.success_rate:.1%}")
            
            slow_models = [m for m in model_usage if m.avg_response_time > 5.0]
            if slow_models:
                insights.append(f"以下模型响应较慢，建议优化: {', '.join(m.model_name for m in slow_models)}")
        
        # 时间模式洞察
        if time_patterns.peak_hours:
            peak_hour = time_patterns.peak_hours[0]["hour"]
            insights.append(f"用户最活跃的时间是 {peak_hour}:00，建议在此时段确保服务稳定性")
        
        # 会话长度洞察
        avg_duration = sum(s.duration_minutes for s in sessions) / len(sessions)
        if avg_duration > 60:
            insights.append("用户倾向于进行长时间对话，建议优化会话管理和内存使用")
        elif avg_duration < 5:
            insights.append("用户会话时间较短，可能需要改善用户体验以提高参与度")
        
        return insights


def get_user_behavior_analyzer(db: Session) -> UserBehaviorAnalyzer:
    """获取用户行为分析器实例"""
    return UserBehaviorAnalyzer(db)