"""模型服务

负责与各种LLM模型进行交互，包括OpenAI、Anthropic等提供商的模型调用。
提供统一的接口来处理不同模型的API差异。
"""

import asyncio
import json
import time
from typing import Dict, List, Any, Optional, AsyncGenerator, Union
from dataclasses import dataclass
from enum import Enum

import openai
import anthropic
import httpx
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from .base_service import BaseService, ServiceError
from database.models import Conversation
from api.config import get_settings


class ModelProvider(Enum):
    """模型提供商枚举"""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"
    LOCAL = "local"


class ModelStatus(Enum):
    """模型状态枚举"""
    AVAILABLE = "available"
    UNAVAILABLE = "unavailable"
    RATE_LIMITED = "rate_limited"
    ERROR = "error"


@dataclass
class ModelResponse:
    """模型响应数据类"""
    content: str
    model_name: str
    provider: ModelProvider
    input_tokens: int
    output_tokens: int
    total_tokens: int
    response_time: float
    cost: float
    metadata: Dict[str, Any]
    error: Optional[str] = None
    status: ModelStatus = ModelStatus.AVAILABLE


@dataclass
class ModelConfig:
    """模型配置数据类"""
    name: str
    provider: ModelProvider
    api_key: str
    base_url: Optional[str] = None
    max_tokens: int = 4096
    temperature: float = 0.7
    top_p: float = 1.0
    frequency_penalty: float = 0.0
    presence_penalty: float = 0.0
    timeout: int = 30
    max_retries: int = 3
    enabled: bool = True


class ModelService(BaseService):
    """模型服务类
    
    提供统一的模型调用接口，支持多种LLM提供商。
    """
    
    def __init__(self):
        super().__init__("ModelService")
        self.settings = get_settings()
        self.models: Dict[str, ModelConfig] = {}
        self.providers: Dict[ModelProvider, Any] = {}
        self._token_costs = {
            # OpenAI pricing (per 1K tokens)
            "gpt-3.5-turbo": {"input": 0.0015, "output": 0.002},
            "gpt-4": {"input": 0.03, "output": 0.06},
            "gpt-4-turbo": {"input": 0.01, "output": 0.03},
            # Anthropic pricing
            "claude-3-sonnet": {"input": 0.003, "output": 0.015},
            "claude-3-haiku": {"input": 0.00025, "output": 0.00125},
            "claude-instant-1": {"input": 0.0008, "output": 0.0024},
        }
    
    async def initialize(self) -> None:
        """初始化模型服务"""
        self.logger.info("Initializing ModelService")
        
        # 初始化模型配置
        await self._load_model_configs()
        
        # 初始化提供商客户端
        await self._initialize_providers()
        
        # 检查模型可用性
        await self._check_model_availability()
        
        self.logger.info(f"ModelService initialized with {len(self.models)} models")
    
    async def cleanup(self) -> None:
        """清理资源"""
        self.logger.info("Cleaning up ModelService")
        
        # 关闭HTTP客户端
        for provider_client in self.providers.values():
            if hasattr(provider_client, 'close'):
                await provider_client.close()
    
    async def _load_model_configs(self) -> None:
        """加载模型配置"""
        # 从设置中加载默认模型配置
        default_models = [
            ModelConfig(
                name="gpt-3.5-turbo",
                provider=ModelProvider.OPENAI,
                api_key=self.settings.OPENAI_API_KEY or "",
                max_tokens=4096,
                temperature=0.7
            ),
            ModelConfig(
                name="gpt-4",
                provider=ModelProvider.OPENAI,
                api_key=self.settings.OPENAI_API_KEY or "",
                max_tokens=8192,
                temperature=0.7
            ),
            ModelConfig(
                name="claude-3-sonnet",
                provider=ModelProvider.ANTHROPIC,
                api_key=self.settings.ANTHROPIC_API_KEY or "",
                max_tokens=4096,
                temperature=0.7
            ),
            ModelConfig(
                name="claude-instant-1",
                provider=ModelProvider.ANTHROPIC,
                api_key=self.settings.ANTHROPIC_API_KEY or "",
                max_tokens=4096,
                temperature=0.7
            )
        ]
        
        for config in default_models:
            if config.api_key:  # 只添加有API密钥的模型
                self.models[config.name] = config
    
    async def _initialize_providers(self) -> None:
        """初始化提供商客户端"""
        # 初始化OpenAI客户端
        if any(model.provider == ModelProvider.OPENAI for model in self.models.values()):
            openai_key = next(
                (model.api_key for model in self.models.values() 
                 if model.provider == ModelProvider.OPENAI), None
            )
            if openai_key:
                self.providers[ModelProvider.OPENAI] = openai.AsyncOpenAI(
                    api_key=openai_key,
                    timeout=30.0
                )
        
        # 初始化Anthropic客户端
        if any(model.provider == ModelProvider.ANTHROPIC for model in self.models.values()):
            anthropic_key = next(
                (model.api_key for model in self.models.values() 
                 if model.provider == ModelProvider.ANTHROPIC), None
            )
            if anthropic_key:
                self.providers[ModelProvider.ANTHROPIC] = anthropic.AsyncAnthropic(
                    api_key=anthropic_key,
                    timeout=30.0
                )
    
    async def _check_model_availability(self) -> None:
        """检查模型可用性"""
        for model_name, config in self.models.items():
            try:
                # 发送简单的测试请求
                await self._test_model(model_name)
                self.logger.info(f"Model {model_name} is available")
            except Exception as e:
                self.logger.warning(f"Model {model_name} is not available: {e}")
                config.enabled = False
    
    async def _test_model(self, model_name: str) -> bool:
        """测试模型是否可用"""
        config = self.models.get(model_name)
        if not config:
            raise ServiceError(f"Model {model_name} not found")
        
        test_messages = [{"role": "user", "content": "Hello"}]
        
        try:
            response = await self._call_model(
                model_name=model_name,
                messages=test_messages,
                max_tokens=10
            )
            return response.status == ModelStatus.AVAILABLE
        except Exception as e:
            self.logger.error(f"Model test failed for {model_name}: {e}")
            return False
    
    async def chat_completion(
        self,
        model_name: str,
        messages: List[Dict[str, str]],
        stream: bool = False,
        **kwargs
    ) -> Union[ModelResponse, AsyncGenerator[str, None]]:
        """聊天完成接口
        
        Args:
            model_name: 模型名称
            messages: 消息列表
            stream: 是否流式响应
            **kwargs: 其他模型参数
        
        Returns:
            ModelResponse或流式生成器
        """
        return await self.execute_with_monitoring(
            f"chat_completion_{model_name}",
            self._call_model,
            model_name=model_name,
            messages=messages,
            stream=stream,
            **kwargs
        )
    
    async def _call_model(
        self,
        model_name: str,
        messages: List[Dict[str, str]],
        stream: bool = False,
        **kwargs
    ) -> Union[ModelResponse, AsyncGenerator[str, None]]:
        """调用模型的内部方法"""
        config = self.models.get(model_name)
        if not config:
            raise ServiceError(
                f"Model {model_name} not found",
                error_code="MODEL_NOT_FOUND"
            )
        
        if not config.enabled:
            raise ServiceError(
                f"Model {model_name} is disabled",
                error_code="MODEL_DISABLED"
            )
        
        provider_client = self.providers.get(config.provider)
        if not provider_client:
            raise ServiceError(
                f"Provider {config.provider.value} not initialized",
                error_code="PROVIDER_NOT_AVAILABLE"
            )
        
        start_time = time.time()
        
        try:
            if config.provider == ModelProvider.OPENAI:
                return await self._call_openai(
                    client=provider_client,
                    config=config,
                    messages=messages,
                    stream=stream,
                    start_time=start_time,
                    **kwargs
                )
            elif config.provider == ModelProvider.ANTHROPIC:
                return await self._call_anthropic(
                    client=provider_client,
                    config=config,
                    messages=messages,
                    stream=stream,
                    start_time=start_time,
                    **kwargs
                )
            else:
                raise ServiceError(
                    f"Provider {config.provider.value} not implemented",
                    error_code="PROVIDER_NOT_IMPLEMENTED"
                )
        
        except Exception as e:
            response_time = time.time() - start_time
            self.logger.error(
                f"Model call failed: {model_name}, error: {e}, time: {response_time:.3f}s"
            )
            
            return ModelResponse(
                content="",
                model_name=model_name,
                provider=config.provider,
                input_tokens=0,
                output_tokens=0,
                total_tokens=0,
                response_time=response_time,
                cost=0.0,
                metadata={},
                error=str(e),
                status=ModelStatus.ERROR
            )
    
    async def _call_openai(
        self,
        client: openai.AsyncOpenAI,
        config: ModelConfig,
        messages: List[Dict[str, str]],
        stream: bool,
        start_time: float,
        **kwargs
    ) -> Union[ModelResponse, AsyncGenerator[str, None]]:
        """调用OpenAI模型"""
        # 合并参数
        params = {
            "model": config.name,
            "messages": messages,
            "max_tokens": kwargs.get("max_tokens", config.max_tokens),
            "temperature": kwargs.get("temperature", config.temperature),
            "top_p": kwargs.get("top_p", config.top_p),
            "frequency_penalty": kwargs.get("frequency_penalty", config.frequency_penalty),
            "presence_penalty": kwargs.get("presence_penalty", config.presence_penalty),
            "stream": stream
        }
        
        if stream:
            return self._openai_stream_response(client, params, config, start_time)
        else:
            response = await client.chat.completions.create(**params)
            response_time = time.time() - start_time
            
            # 提取响应数据
            content = response.choices[0].message.content or ""
            input_tokens = response.usage.prompt_tokens if response.usage else 0
            output_tokens = response.usage.completion_tokens if response.usage else 0
            total_tokens = response.usage.total_tokens if response.usage else 0
            
            # 计算成本
            cost = self._calculate_cost(config.name, input_tokens, output_tokens)
            
            return ModelResponse(
                content=content,
                model_name=config.name,
                provider=config.provider,
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                total_tokens=total_tokens,
                response_time=response_time,
                cost=cost,
                metadata={
                    "finish_reason": response.choices[0].finish_reason,
                    "response_id": response.id
                },
                status=ModelStatus.AVAILABLE
            )
    
    async def _openai_stream_response(
        self,
        client: openai.AsyncOpenAI,
        params: Dict[str, Any],
        config: ModelConfig,
        start_time: float
    ) -> AsyncGenerator[str, None]:
        """OpenAI流式响应生成器"""
        try:
            stream = await client.chat.completions.create(**params)
            
            async for chunk in stream:
                if chunk.choices and chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
        
        except Exception as e:
            self.logger.error(f"OpenAI streaming failed: {e}")
            yield f"[ERROR: {str(e)}]"
    
    async def _call_anthropic(
        self,
        client: anthropic.AsyncAnthropic,
        config: ModelConfig,
        messages: List[Dict[str, str]],
        stream: bool,
        start_time: float,
        **kwargs
    ) -> Union[ModelResponse, AsyncGenerator[str, None]]:
        """调用Anthropic模型"""
        # 转换消息格式（Anthropic格式略有不同）
        anthropic_messages = self._convert_to_anthropic_format(messages)
        
        params = {
            "model": config.name,
            "messages": anthropic_messages,
            "max_tokens": kwargs.get("max_tokens", config.max_tokens),
            "temperature": kwargs.get("temperature", config.temperature),
            "stream": stream
        }
        
        if stream:
            return self._anthropic_stream_response(client, params, config, start_time)
        else:
            response = await client.messages.create(**params)
            response_time = time.time() - start_time
            
            # 提取响应数据
            content = "".join([block.text for block in response.content if hasattr(block, 'text')])
            input_tokens = response.usage.input_tokens if response.usage else 0
            output_tokens = response.usage.output_tokens if response.usage else 0
            total_tokens = input_tokens + output_tokens
            
            # 计算成本
            cost = self._calculate_cost(config.name, input_tokens, output_tokens)
            
            return ModelResponse(
                content=content,
                model_name=config.name,
                provider=config.provider,
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                total_tokens=total_tokens,
                response_time=response_time,
                cost=cost,
                metadata={
                    "stop_reason": response.stop_reason,
                    "response_id": response.id
                },
                status=ModelStatus.AVAILABLE
            )
    
    async def _anthropic_stream_response(
        self,
        client: anthropic.AsyncAnthropic,
        params: Dict[str, Any],
        config: ModelConfig,
        start_time: float
    ) -> AsyncGenerator[str, None]:
        """Anthropic流式响应生成器"""
        try:
            stream = await client.messages.create(**params)
            
            async for chunk in stream:
                if chunk.type == "content_block_delta" and hasattr(chunk.delta, 'text'):
                    yield chunk.delta.text
        
        except Exception as e:
            self.logger.error(f"Anthropic streaming failed: {e}")
            yield f"[ERROR: {str(e)}]"
    
    def _convert_to_anthropic_format(self, messages: List[Dict[str, str]]) -> List[Dict[str, str]]:
        """转换消息格式为Anthropic格式"""
        # Anthropic的消息格式与OpenAI基本相同，但可能有细微差别
        return messages
    
    def _calculate_cost(self, model_name: str, input_tokens: int, output_tokens: int) -> float:
        """计算API调用成本"""
        if model_name not in self._token_costs:
            return 0.0
        
        costs = self._token_costs[model_name]
        input_cost = (input_tokens / 1000) * costs["input"]
        output_cost = (output_tokens / 1000) * costs["output"]
        
        return input_cost + output_cost
    
    async def get_available_models(self) -> List[Dict[str, Any]]:
        """获取可用模型列表"""
        models = []
        for name, config in self.models.items():
            if config.enabled:
                models.append({
                    "name": name,
                    "provider": config.provider.value,
                    "max_tokens": config.max_tokens,
                    "enabled": config.enabled
                })
        
        return models
    
    async def get_model_info(self, model_name: str) -> Dict[str, Any]:
        """获取模型详细信息"""
        config = self.models.get(model_name)
        if not config:
            raise ServiceError(
                f"Model {model_name} not found",
                error_code="MODEL_NOT_FOUND"
            )
        
        return {
            "name": config.name,
            "provider": config.provider.value,
            "max_tokens": config.max_tokens,
            "temperature": config.temperature,
            "enabled": config.enabled,
            "cost_per_1k_input": self._token_costs.get(model_name, {}).get("input", 0),
            "cost_per_1k_output": self._token_costs.get(model_name, {}).get("output", 0)
        }
    
    async def update_model_config(
        self, 
        model_name: str, 
        updates: Dict[str, Any]
    ) -> Dict[str, Any]:
        """更新模型配置"""
        config = self.models.get(model_name)
        if not config:
            raise ServiceError(
                f"Model {model_name} not found",
                error_code="MODEL_NOT_FOUND"
            )
        
        # 更新配置
        for key, value in updates.items():
            if hasattr(config, key):
                setattr(config, key, value)
        
        # 如果启用状态改变，重新检查可用性
        if "enabled" in updates and updates["enabled"]:
            config.enabled = await self._test_model(model_name)
        
        return await self.get_model_info(model_name)
    
    async def get_usage_statistics(self, days: int = 7) -> Dict[str, Any]:
        """获取模型使用统计"""
        async with self.get_db_session() as session:
            # 查询最近N天的对话记录
            from datetime import datetime, timedelta
            
            start_date = datetime.utcnow() - timedelta(days=days)
            
            query = select(Conversation).where(
                Conversation.created_at >= start_date
            )
            
            result = await session.execute(query)
            conversations = result.scalars().all()
            
            # 统计数据
            stats = {
                "total_conversations": len(conversations),
                "total_tokens": sum(c.total_tokens or 0 for c in conversations),
                "total_cost": sum(c.cost or 0 for c in conversations),
                "avg_response_time": sum(c.response_time or 0 for c in conversations) / len(conversations) if conversations else 0,
                "model_usage": {},
                "success_rate": len([c for c in conversations if c.status == "completed"]) / len(conversations) if conversations else 0
            }
            
            # 按模型统计
            for conv in conversations:
                model = conv.model_name
                if model not in stats["model_usage"]:
                    stats["model_usage"][model] = {
                        "count": 0,
                        "tokens": 0,
                        "cost": 0,
                        "avg_response_time": 0
                    }
                
                stats["model_usage"][model]["count"] += 1
                stats["model_usage"][model]["tokens"] += conv.total_tokens or 0
                stats["model_usage"][model]["cost"] += conv.cost or 0
                stats["model_usage"][model]["avg_response_time"] += conv.response_time or 0
            
            # 计算平均响应时间
            for model_stats in stats["model_usage"].values():
                if model_stats["count"] > 0:
                    model_stats["avg_response_time"] /= model_stats["count"]
            
            return stats
        return None