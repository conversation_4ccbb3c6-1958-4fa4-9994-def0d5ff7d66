"""缓存服务模块

提供统一的缓存管理功能，包括内存缓存、Redis缓存等，用于提高系统性能。
"""

import hashlib
import json
import time
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

from .base_service import BaseService


@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    value: Any
    created_at: float
    expires_at: Optional[float] = None
    access_count: int = 0
    last_accessed: float = 0
    tags: List[str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []
        if self.last_accessed == 0:
            self.last_accessed = self.created_at
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.expires_at is None:
            return False
        return time.time() > self.expires_at
    
    def touch(self) -> None:
        """更新访问时间和次数"""
        self.last_accessed = time.time()
        self.access_count += 1


class CacheService(BaseService):
    """缓存服务类
    
    提供多层缓存功能：
    - 内存缓存（L1）
    - Redis缓存（L2，可选）
    - 缓存策略管理
    - 缓存统计和监控
    """
    
    def __init__(self, max_memory_size: int = 1000, default_ttl: int = 3600):
        super().__init__("CacheService")
        self.max_memory_size = max_memory_size
        self.default_ttl = default_ttl
        
        # 内存缓存
        self._memory_cache: Dict[str, CacheEntry] = {}
        self._cache_stats = {
            "hits": 0,
            "misses": 0,
            "sets": 0,
            "deletes": 0,
            "evictions": 0,
            "memory_usage": 0
        }
        
        # Redis缓存（可选）
        self._redis_client = None
        self._redis_enabled = False
    
    async def initialize(self) -> None:
        """初始化缓存服务"""
        self.logger.info("Initializing CacheService")
        
        # 尝试初始化Redis连接
        await self._init_redis()
        
        # 启动清理任务
        await self._start_cleanup_task()
        
        self.logger.info(
            f"CacheService initialized - Memory cache: {self.max_memory_size} entries, "
            f"Redis: {'enabled' if self._redis_enabled else 'disabled'}"
        )
    
    async def cleanup(self) -> None:
        """清理资源"""
        self.logger.info("Cleaning up CacheService")
        
        # 清理内存缓存
        self._memory_cache.clear()
        
        # 关闭Redis连接
        if self._redis_client:
            await self._redis_client.close()
    
    async def get(self, key: str, default: Any = None) -> Any:
        """获取缓存值
        
        Args:
            key: 缓存键
            default: 默认值
        
        Returns:
            缓存值或默认值
        """
        return await self.execute_with_monitoring(
            "cache_get",
            self._get_impl,
            key,
            default
        )
    
    async def _get_impl(self, key: str, default: Any = None) -> Any:
        """获取缓存值的实现"""
        # 先检查内存缓存
        if key in self._memory_cache:
            entry = self._memory_cache[key]
            
            if entry.is_expired():
                # 过期，删除并继续查找
                del self._memory_cache[key]
                self._cache_stats["evictions"] += 1
            else:
                # 命中，更新访问信息
                entry.touch()
                self._cache_stats["hits"] += 1
                return entry.value
        
        # 检查Redis缓存
        if self._redis_enabled:
            try:
                redis_value = await self._redis_client.get(key)
                if redis_value is not None:
                    # Redis命中，反序列化并存入内存缓存
                    value = json.loads(redis_value)
                    await self._set_memory_cache(key, value, self.default_ttl)
                    self._cache_stats["hits"] += 1
                    return value
            except Exception as e:
                self.logger.warning(f"Redis get error for key {key}: {e}")
        
        # 缓存未命中
        self._cache_stats["misses"] += 1
        return default
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        tags: Optional[List[str]] = None
    ) -> bool:
        """设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 过期时间（秒）
            tags: 标签列表
        
        Returns:
            是否设置成功
        """
        return await self.execute_with_monitoring(
            "cache_set",
            self._set_impl,
            key,
            value,
            ttl,
            tags
        )
    
    async def _set_impl(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        tags: Optional[List[str]] = None
    ) -> bool:
        """设置缓存值的实现"""
        if ttl is None:
            ttl = self.default_ttl
        
        # 设置内存缓存
        await self._set_memory_cache(key, value, ttl, tags)
        
        # 设置Redis缓存
        if self._redis_enabled:
            try:
                serialized_value = json.dumps(value, default=str)
                await self._redis_client.setex(key, ttl, serialized_value)
            except Exception as e:
                self.logger.warning(f"Redis set error for key {key}: {e}")
        
        self._cache_stats["sets"] += 1
        return True
    
    async def delete(self, key: str) -> bool:
        """删除缓存值
        
        Args:
            key: 缓存键
        
        Returns:
            是否删除成功
        """
        return await self.execute_with_monitoring(
            "cache_delete",
            self._delete_impl,
            key
        )
    
    async def _delete_impl(self, key: str) -> bool:
        """删除缓存值的实现"""
        deleted = False
        
        # 删除内存缓存
        if key in self._memory_cache:
            del self._memory_cache[key]
            deleted = True
        
        # 删除Redis缓存
        if self._redis_enabled:
            try:
                redis_deleted = await self._redis_client.delete(key)
                deleted = deleted or redis_deleted > 0
            except Exception as e:
                self.logger.warning(f"Redis delete error for key {key}: {e}")
        
        if deleted:
            self._cache_stats["deletes"] += 1
        
        return deleted
    
    async def exists(self, key: str) -> bool:
        """检查缓存键是否存在
        
        Args:
            key: 缓存键
        
        Returns:
            是否存在
        """
        # 检查内存缓存
        if key in self._memory_cache:
            entry = self._memory_cache[key]
            if not entry.is_expired():
                return True
            else:
                # 过期，删除
                del self._memory_cache[key]
                self._cache_stats["evictions"] += 1
        
        # 检查Redis缓存
        if self._redis_enabled:
            try:
                return await self._redis_client.exists(key) > 0
            except Exception as e:
                self.logger.warning(f"Redis exists error for key {key}: {e}")
        
        return False
    
    async def clear(self, pattern: Optional[str] = None, tags: Optional[List[str]] = None) -> int:
        """清除缓存
        
        Args:
            pattern: 键模式（可选）
            tags: 标签过滤（可选）
        
        Returns:
            清除的条目数量
        """
        return await self.execute_with_monitoring(
            "cache_clear",
            self._clear_impl,
            pattern,
            tags
        )
    
    async def _clear_impl(self, pattern: Optional[str] = None, tags: Optional[List[str]] = None) -> int:
        """清除缓存的实现"""
        cleared_count = 0
        
        # 清除内存缓存
        if pattern is None and tags is None:
            # 清除所有
            cleared_count = len(self._memory_cache)
            self._memory_cache.clear()
        else:
            # 按条件清除
            keys_to_delete = []
            
            for key, entry in self._memory_cache.items():
                should_delete = True
                
                if pattern and not self._match_pattern(key, pattern):
                    should_delete = False
                
                if tags and not any(tag in entry.tags for tag in tags):
                    should_delete = False
                
                if should_delete:
                    keys_to_delete.append(key)
            
            for key in keys_to_delete:
                del self._memory_cache[key]
                cleared_count += 1
        
        # 清除Redis缓存
        if self._redis_enabled:
            try:
                if pattern:
                    # 使用模式删除
                    keys = await self._redis_client.keys(pattern)
                    if keys:
                        await self._redis_client.delete(*keys)
                        cleared_count += len(keys)
                elif tags is None:
                    # 清除所有
                    await self._redis_client.flushdb()
            except Exception as e:
                self.logger.warning(f"Redis clear error: {e}")
        
        return cleared_count
    
    async def get_or_set(
        self,
        key: str,
        factory_func,
        ttl: Optional[int] = None,
        tags: Optional[List[str]] = None
    ) -> Any:
        """获取缓存值，如果不存在则通过工厂函数创建
        
        Args:
            key: 缓存键
            factory_func: 工厂函数（可以是协程）
            ttl: 过期时间（秒）
            tags: 标签列表
        
        Returns:
            缓存值
        """
        # 先尝试获取
        value = await self.get(key)
        
        if value is not None:
            return value
        
        # 不存在，通过工厂函数创建
        if asyncio.iscoroutinefunction(factory_func):
            value = await factory_func()
        else:
            value = factory_func()
        
        # 设置缓存
        await self.set(key, value, ttl, tags)
        
        return value
    
    def generate_key(self, *args, **kwargs) -> str:
        """生成缓存键
        
        Args:
            *args: 位置参数
            **kwargs: 关键字参数
        
        Returns:
            生成的缓存键
        """
        # 创建键的字符串表示
        key_parts = []
        
        for arg in args:
            if isinstance(arg, (str, int, float, bool)):
                key_parts.append(str(arg))
            else:
                key_parts.append(json.dumps(arg, sort_keys=True, default=str))
        
        for k, v in sorted(kwargs.items()):
            if isinstance(v, (str, int, float, bool)):
                key_parts.append(f"{k}={v}")
            else:
                key_parts.append(f"{k}={json.dumps(v, sort_keys=True, default=str)}")
        
        key_string = ":".join(key_parts)
        
        # 生成哈希
        return hashlib.md5(key_string.encode()).hexdigest()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息
        
        Returns:
            统计信息字典
        """
        total_requests = self._cache_stats["hits"] + self._cache_stats["misses"]
        hit_rate = (self._cache_stats["hits"] / total_requests * 100) if total_requests > 0 else 0
        
        # 计算内存使用情况
        memory_entries = len(self._memory_cache)
        memory_usage_percent = (memory_entries / self.max_memory_size * 100) if self.max_memory_size > 0 else 0
        
        return {
            "hits": self._cache_stats["hits"],
            "misses": self._cache_stats["misses"],
            "sets": self._cache_stats["sets"],
            "deletes": self._cache_stats["deletes"],
            "evictions": self._cache_stats["evictions"],
            "hit_rate": round(hit_rate, 2),
            "total_requests": total_requests,
            "memory_entries": memory_entries,
            "memory_usage_percent": round(memory_usage_percent, 2),
            "redis_enabled": self._redis_enabled
        }
    
    def reset_stats(self) -> None:
        """重置统计信息"""
        self._cache_stats = {
            "hits": 0,
            "misses": 0,
            "sets": 0,
            "deletes": 0,
            "evictions": 0,
            "memory_usage": 0
        }
    
    async def _set_memory_cache(
        self,
        key: str,
        value: Any,
        ttl: int,
        tags: Optional[List[str]] = None
    ) -> None:
        """设置内存缓存"""
        now = time.time()
        expires_at = now + ttl if ttl > 0 else None
        
        entry = CacheEntry(
            key=key,
            value=value,
            created_at=now,
            expires_at=expires_at,
            tags=tags or []
        )
        
        # 检查是否需要清理空间
        if len(self._memory_cache) >= self.max_memory_size:
            await self._evict_entries()
        
        self._memory_cache[key] = entry
    
    async def _evict_entries(self, count: int = None) -> None:
        """清理缓存条目"""
        if count is None:
            count = max(1, self.max_memory_size // 10)  # 清理10%
        
        # 按LRU策略清理
        entries = list(self._memory_cache.items())
        entries.sort(key=lambda x: x[1].last_accessed)
        
        for i in range(min(count, len(entries))):
            key, _ = entries[i]
            del self._memory_cache[key]
            self._cache_stats["evictions"] += 1
    
    async def _init_redis(self) -> None:
        """初始化Redis连接"""
        try:
            import redis.asyncio as redis
            
            # 从配置获取Redis连接信息
            redis_url = "redis://localhost:6379/0"  # 默认配置
            
            self._redis_client = redis.from_url(redis_url)
            
            # 测试连接
            await self._redis_client.ping()
            self._redis_enabled = True
            
            self.logger.info("Redis cache enabled")
            
        except ImportError:
            self.logger.info("Redis not available, using memory cache only")
        except Exception as e:
            self.logger.warning(f"Failed to connect to Redis: {e}")
    
    async def _start_cleanup_task(self) -> None:
        """启动清理任务"""
        # 这里可以启动定期清理过期条目的任务
        pass
    
    def _match_pattern(self, key: str, pattern: str) -> bool:
        """匹配模式"""
        import fnmatch
        return fnmatch.fnmatch(key, pattern)


# 缓存装饰器
def cached(
    ttl: int = 3600,
    key_prefix: str = "",
    tags: Optional[List[str]] = None
):
    """缓存装饰器
    
    Args:
        ttl: 过期时间（秒）
        key_prefix: 键前缀
        tags: 标签列表
    
    Returns:
        装饰器函数
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 获取缓存服务实例
            cache_service = CacheService()
            
            # 生成缓存键
            key_parts = [key_prefix, func.__name__] if key_prefix else [func.__name__]
            key_parts.extend([str(arg) for arg in args])
            key_parts.extend([f"{k}={v}" for k, v in sorted(kwargs.items())])
            
            cache_key = ":".join(key_parts)
            
            # 尝试从缓存获取
            cached_result = await cache_service.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 执行函数
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)
            
            # 设置缓存
            await cache_service.set(cache_key, result, ttl, tags)
            
            return result
        
        return wrapper
    return decorator


# 导入asyncio（如果需要）
import asyncio