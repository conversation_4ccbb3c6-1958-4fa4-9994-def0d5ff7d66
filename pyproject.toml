[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "llm_comparison_tool"
version = "0.1.0"
description = "A tool to compare responses from multiple LLMs (OpenAI, Gemini, 豆包, 千问)."
authors = [{ name = "LLM Comparison Tool", email = "<EMAIL>" }]
requires-python = ">=3.9"
readme = "README.md"
license = { text = "MIT" }
keywords = ["llm", "ai", "comparison", "chatbot", "openai", "gemini"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    "requests>=2.31.0",
    "pyyaml>=6.0",
    "python-dotenv>=1.0.0",
    "colorama>=0.4.6",
    "tabulate>=0.9.0",
    "rich>=13.0.0",
    "aiohttp>=3.8.0",
    "asyncio>=3.4.3",
    "psutil>=5.9.0",
    "loguru>=0.7.0",
    # API dependencies
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "python-multipart>=0.0.6",
    "sqlalchemy>=2.0.23",
    "aiosqlite>=0.19.0",
    "greenlet>=2.0.0",
    "alembic>=1.12.1",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "passlib[bcrypt]>=1.7.4",
    "python-jose[cryptography]>=3.3.0",
    "cryptography>=41.0.8",
    "httpx>=0.25.2",
    "structlog>=23.2.0",
    "python-dateutil>=2.8.2",
    "orjson>=3.9.10",
    "aiofiles>=23.2.1",
    # LLM API dependencies
    "openai>=1.3.0",
    "google-generativeai>=0.7.0",
    "anthropic>=0.7.0",
    "pyjwt>=2.10.1",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
    "pre-commit>=3.0.0",
]
test = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
    "responses>=0.23.0",
    "httpx>=0.24.0",
]

[project.scripts]
compare = "main:main"
llm-compare = "main:main"

[project.urls]
Homepage = "https://github.com/example/llm_comparison_tool"
Repository = "https://github.com/example/llm_comparison_tool"
Issues = "https://github.com/example/llm_comparison_tool/issues"

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.uv]
# UV 特定配置
dev-dependencies = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
    "pre-commit>=3.0.0",
    "responses>=0.23.0",
    "httpx>=0.24.0",
    "isort>=5.12.0",
]

# UV 工作空间配置
[tool.uv.workspace]
members = ["."]  # 单一项目工作空间

# UV 源配置
[tool.uv.sources]
# 可以在这里配置特定包的源

# UV 环境配置已移至项目级别的 requires-python

# 开发脚本已移至 Makefile，避免重复配置
# 使用 make start, make test 等命令

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --cov=src --cov-report=term-missing"
testpaths = [
    "tests",
]
python_files = [
    "test_*.py",
    "*_test.py",
]
python_classes = [
    "Test*",
]
python_functions = [
    "test_*",
]
markers = [
    "slow: marks tests as slow (deselect with '-m 'not slow'')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
