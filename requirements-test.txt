# 测试依赖包

# 核心测试框架
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0
pytest-xdist>=3.3.0
pytest-timeout>=2.1.0
pytest-html>=3.2.0
pytest-json-report>=1.5.0
pytest-benchmark>=4.0.0
pytest-sugar>=0.9.7
pytest-clarity>=1.0.1
pytest-randomly>=3.8

# 模拟和测试工具
factory-boy>=3.3.0
faker>=19.0.0
responses>=0.23.0
httpx>=0.24.0
requests-mock>=1.11.0
freezegun>=1.2.0
time-machine>=2.10.0

# 数据库测试
pytest-postgresql>=5.0.0
pytest-redis>=3.0.0
sqlalchemy-utils>=0.41.0
alembic>=1.11.0

# 异步测试
aioresponses>=0.7.4
aiofiles>=23.0.0
aioredis>=2.0.0

# API测试
httpx>=0.24.0
starlette[testing]>=0.27.0
fastapi[testing]>=0.100.0

# 性能和负载测试
locust>=2.15.0
memory-profiler>=0.60.0
psutil>=5.9.0

# 代码质量和覆盖率
coverage[toml]>=7.2.0
pytest-cov>=4.1.0
coverage-badge>=1.1.0

# 静态分析和代码检查
flake8>=6.0.0
black>=23.0.0
isort>=5.12.0
mypy>=1.4.0
bandit>=1.7.0
safety>=2.3.0
pylint>=2.17.0

# 文档测试
pytest-doctestplus>=1.0.0
doctest>=2.7.0

# 环境和配置
python-dotenv>=1.0.0
pytest-env>=0.8.0

# 报告和可视化
allure-pytest>=2.13.0
pytest-html>=3.2.0
pytest-json-report>=1.5.0
pytest-metadata>=3.0.0

# 并发和并行测试
pytest-xdist>=3.3.0
pytest-forked>=1.6.0

# 调试工具
pytest-pdb>=0.2.0
pytest-pudb>=0.7.0
ipdb>=0.13.0

# 数据验证和序列化测试
pydantic>=2.0.0
marshmallow>=3.20.0
cerberus>=1.3.0

# 时间和日期测试
freezegun>=1.2.0
time-machine>=2.10.0
arrow>=1.2.0

# 文件和路径测试
pyfakefs>=5.2.0
tempfile>=3.11.0

# 网络和HTTP测试
responses>=0.23.0
httpretty>=1.1.0
requests-mock>=1.11.0

# 加密和安全测试
cryptography>=41.0.0
passlib>=1.7.0
pyjwt>=2.8.0

# 日志测试
loguru>=0.7.0
structlog>=23.0.0

# 缓存测试
redis>=4.6.0
memcached>=1.59.0

# 消息队列测试
celery>=5.3.0
kombu>=5.3.0

# 监控和指标测试
prometheus-client>=0.17.0
statsd>=4.0.0

# 配置管理测试
pydantic-settings>=2.0.0
hydra-core>=1.3.0

# 数据处理测试
pandas>=2.0.0
numpy>=1.24.0

# 机器学习测试（如果需要）
scikit-learn>=1.3.0
tensorflow>=2.13.0
torch>=2.0.0

# 图像处理测试（如果需要）
Pillow>=10.0.0
opencv-python>=4.8.0

# 自然语言处理测试（如果需要）
nltk>=3.8.0
spacy>=3.6.0
transformers>=4.30.0

# 开发工具
pre-commit>=3.3.0
tox>=4.6.0
nox>=2023.4.22

# 类型检查
mypy>=1.4.0
types-requests>=2.31.0
types-redis>=4.6.0
types-PyYAML>=6.0.0

# 文档生成
sphinx>=7.0.0
sphinx-rtd-theme>=1.3.0
mkdocs>=1.5.0
mkdocs-material>=9.1.0