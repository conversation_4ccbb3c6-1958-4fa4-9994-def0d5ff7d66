"""基础配置模块

提供所有配置类的基础定义，避免重复代码
"""

import os
import secrets
from pathlib import Path
from typing import List, Dict, Any, Optional

from pydantic import BaseModel, Field, field_validator


class DatabaseConfig(BaseModel):
    """数据库配置"""
    url: str = Field("sqlite:///./data/llm_comparison.db", description="数据库连接URL")
    pool_size: int = Field(5, description="连接池大小")
    max_overflow: int = Field(10, description="连接池最大溢出")
    pool_timeout: int = Field(30, description="连接池超时时间（秒）")
    echo: bool = Field(False, description="是否启用SQL语句日志")

    @field_validator("url", mode="before")
    @classmethod
    def validate_database_url(cls, v):
        """验证数据库URL"""
        if v.startswith("sqlite:///") and not v.startswith("sqlite:///./") and not v.startswith("sqlite:////"):
            return v.replace("sqlite:///", "sqlite:///./")
        return v


class ServerConfig(BaseModel):
    """服务器配置"""
    host: str = Field("0.0.0.0", description="服务器主机")
    port: int = Field(8000, description="服务器端口")
    workers: int = Field(1, description="工作进程数")
    reload: bool = Field(True, description="是否启用自动重载")
    timeout: int = Field(60, description="请求超时时间（秒）")


class SecurityConfig(BaseModel):
    """安全配置"""
    secret_key: str = Field(default="", description="密钥，用于加密和签名")
    api_key_header: str = Field("X-API-Key", description="API密钥请求头")
    allowed_origins: List[str] = Field(["*"], description="允许的跨域来源")
    allowed_hosts: List[str] = Field(["*"], description="允许的主机")
    rate_limit_enabled: bool = Field(True, description="是否启用速率限制")
    rate_limit_default: str = Field("100/minute", description="默认速率限制")

    @field_validator("secret_key", mode="before")
    @classmethod
    def generate_secret_key(cls, v: Optional[str]) -> str:
        """如果未提供密钥，则生成一个随机密钥"""
        if not v:
            return secrets.token_hex(32)
        return v


class SessionConfig(BaseModel):
    """会话配置"""
    default_timeout: int = Field(3600, description="默认会话超时时间（秒）")
    max_conversations_per_session: int = Field(100, description="每个会话最大对话数")
    max_history_length: int = Field(10, description="对话历史最大长度")


class StorageConfig(BaseModel):
    """文件存储配置"""
    storage_dir: str = Field("./data/storage", description="文件存储目录")
    max_upload_size: int = Field(10 * 1024 * 1024, description="最大上传文件大小（字节）")
    allowed_upload_types: List[str] = Field(
        ["text/plain", "application/json"], 
        description="允许的上传文件类型"
    )

    @field_validator("storage_dir", mode="before")
    @classmethod
    def validate_storage_dir(cls, v: str) -> str:
        """验证存储目录"""
        if not os.path.isabs(v):
            # 确保存储目录是相对于项目根目录的
            project_root = Path(__file__).parent.parent.parent
            return str(project_root / v)
        return v


class ModelConfig(BaseModel):
    """模型配置"""
    name: str = Field(..., description="模型名称")
    api_key: str = Field(..., description="API密钥")
    base_url: str = Field(..., description="API基础URL")
    enabled: bool = Field(True, description="是否启用")
    timeout: int = Field(30, description="请求超时时间（秒）")
    max_concurrent_requests: int = Field(5, description="最大并发请求数")
    available_models: Dict[str, Any] = Field(default_factory=dict, description="可用模型列表")


class CacheConfig(BaseModel):
    """缓存配置"""
    enabled: bool = Field(True, description="是否启用缓存")
    ttl: int = Field(3600, description="缓存过期时间（秒）")
    max_size: int = Field(1000, description="缓存最大条目数")


class MonitoringConfig(BaseModel):
    """监控配置"""
    enabled: bool = Field(True, description="是否启用性能监控")
    slow_request_threshold: float = Field(1.0, description="慢请求阈值（秒）")
    metrics_enabled: bool = Field(True, description="是否启用指标收集")


class EnvironmentConfig(BaseModel):
    """环境配置"""
    environment: str = Field("development", description="运行环境")
    debug: bool = Field(True, description="是否启用调试模式")
    log_level: str = Field("INFO", description="日志级别")
    structured_logs: bool = Field(False, description="是否使用结构化日志")


class BaseConfigMixin:
    """配置基础混入类，提供通用方法"""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        if hasattr(self, 'model_dump'):
            return self.model_dump()
        elif hasattr(self, 'dict'):
            return self.dict()
        else:
            return {}
    
    def update_config(self, **kwargs):
        """更新配置"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    @staticmethod
    def create_directories(*paths: str) -> None:
        """创建必要的目录"""
        for path in paths:
            Path(path).mkdir(parents=True, exist_ok=True)


# 环境特定配置工厂
class EnvironmentConfigFactory:
    """环境特定配置工厂"""
    
    @staticmethod
    def get_environment_overrides(environment: str) -> Dict[str, Any]:
        """获取环境特定的配置覆盖"""
        env = environment.lower()
        
        if env == "production":
            return {
                "debug": False,
                "server": {"reload": False},
                "database": {"echo": False},
                "security": {
                    "allowed_origins": ["https://your-production-domain.com"],
                    "allowed_hosts": ["your-production-domain.com"],
                    "rate_limit_default": "50/minute"
                }
            }
        elif env == "staging":
            return {
                "debug": False,
                "server": {"reload": False},
                "database": {"echo": False},
                "security": {
                    "allowed_origins": ["https://staging.your-domain.com"],
                    "allowed_hosts": ["staging.your-domain.com"],
                    "rate_limit_default": "80/minute"
                }
            }
        elif env == "testing":
            return {
                "debug": True,
                "server": {"reload": False},
                "database": {
                    "url": "sqlite:///./data/test.db",
                    "echo": True
                },
                "security": {"rate_limit_enabled": False}
            }
        else:  # development
            return {
                "debug": True,
                "server": {"reload": True},
                "database": {"echo": True},
                "security": {
                    "allowed_origins": ["*"],
                    "allowed_hosts": ["*"],
                    "rate_limit_enabled": False
                }
            }


# 配置验证器
class ConfigValidator:
    """配置验证器"""
    
    @staticmethod
    def validate_required_env_vars(required_vars: List[str]) -> Dict[str, str]:
        """验证必需的环境变量"""
        missing_vars = []
        env_values = {}
        
        for var in required_vars:
            value = os.getenv(var)
            if not value:
                missing_vars.append(var)
            else:
                env_values[var] = value
        
        if missing_vars:
            raise ValueError(f"缺少必需的环境变量: {', '.join(missing_vars)}")
        
        return env_values
    
    @staticmethod
    def validate_paths(*paths: str) -> None:
        """验证路径是否可访问"""
        for path in paths:
            path_obj = Path(path)
            if not path_obj.parent.exists():
                try:
                    path_obj.parent.mkdir(parents=True, exist_ok=True)
                except Exception as e:
                    raise ValueError(f"无法创建路径 {path}: {e}")
