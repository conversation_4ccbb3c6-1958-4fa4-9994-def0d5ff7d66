"""统一配置管理系统

整合API配置、应用配置、模型配置等，提供统一的配置访问接口。
消除重复代码，使用基础配置类。
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, List, Optional
from functools import lru_cache

from pydantic_settings import BaseSettings
from dotenv import load_dotenv
from utils.logger import logger

from .base_config import (
    DatabaseConfig, ServerConfig, SecurityConfig, SessionConfig,
    StorageConfig, ModelConfig, CacheConfig, MonitoringConfig,
    EnvironmentConfig, BaseConfigMixin, EnvironmentConfigFactory,
    ConfigValidator
)
from .logging_config import LoggingConfig

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent.parent
CONFIG_DIR = PROJECT_ROOT / "config"


class UnifiedConfig(BaseSettings, BaseConfigMixin):
    """统一配置管理器
    
    整合所有配置项，提供统一的配置访问接口。
    """

    # 环境配置
    environment: EnvironmentConfig = EnvironmentConfig()

    # 子配置
    database: DatabaseConfig = DatabaseConfig()
    server: ServerConfig = ServerConfig()
    security: SecurityConfig = SecurityConfig()
    logging: LoggingConfig = LoggingConfig()
    cache: CacheConfig = CacheConfig()
    monitoring: MonitoringConfig = MonitoringConfig()
    session: SessionConfig = SessionConfig()
    storage: StorageConfig = StorageConfig()

    # 模型配置
    models: Dict[str, ModelConfig] = {}
    active_models: List[str] = []

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True
        extra = "allow"
        env_prefix = "LLM_TOOL_"
        env_nested_delimiter = "__"

    def __init__(self, **kwargs):
        # 加载环境变量
        load_dotenv()

        # 加载YAML配置文件
        yaml_config = self._load_yaml_configs()

        # 合并配置
        merged_config = {**yaml_config, **kwargs}

        super().__init__(**merged_config)

        # 应用环境特定配置
        self._apply_environment_overrides()

        # 后处理
        self._post_init()

    def _load_yaml_configs(self) -> Dict[str, Any]:
        """加载YAML配置文件"""
        config = {}

        # 加载应用配置
        app_config = self._load_yaml_file("app.yaml")
        if app_config:
            config.update(app_config.get("app", {}))

        # 加载模型配置
        models_config = self._load_yaml_file("models_optimized.yaml")
        if models_config:
            models_data = models_config.get("models", {})
            config["models"] = {
                name: ModelConfig(**model_data)
                for name, model_data in models_data.items()
            }

        # 加载活跃模型配置
        active_models_config = self._load_yaml_file("active_models.yaml")
        if active_models_config:
            config["active_models"] = [
                model["name"] for model in active_models_config.get("active_models", [])
                if model.get("enabled", False)
            ]

            # 合并设置
            settings = active_models_config.get("settings", {})
            config.update(settings)

            # 合并对话设置
            conversation = active_models_config.get("conversation", {})
            if conversation:
                config["session"] = SessionConfig(**conversation)

        return config

    def _load_yaml_file(self, filename: str) -> Optional[Dict[str, Any]]:
        """加载单个YAML文件"""
        file_path = CONFIG_DIR / filename

        if not file_path.exists():
            logger.warning(f"配置文件不存在: {file_path}")
            return None

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"加载配置文件 {filename} 失败: {e}")
            return None

    def _apply_environment_overrides(self):
        """应用环境特定配置覆盖"""
        env_overrides = EnvironmentConfigFactory.get_environment_overrides(
            self.environment.environment
        )
        
        for key, value in env_overrides.items():
            if hasattr(self, key):
                if isinstance(value, dict) and hasattr(getattr(self, key), 'update_config'):
                    getattr(self, key).update_config(**value)
                else:
                    setattr(self, key, value)

    def _post_init(self):
        """初始化后处理"""
        # 确保配置对象正确初始化
        if isinstance(self.database, dict):
            self.database = DatabaseConfig(**self.database)
        if isinstance(self.storage, dict):
            self.storage = StorageConfig(**self.storage)
        if isinstance(self.logging, dict):
            self.logging = LoggingConfig(**self.logging)
        if isinstance(self.environment, dict):
            self.environment = EnvironmentConfig(**self.environment)

        # 创建必要的目录
        try:
            db_path = self.database.url.replace("sqlite:///", "")
            if db_path.startswith("./"):
                db_dir = Path(db_path).parent
            else:
                db_dir = Path("./data")

            self.create_directories(
                self.storage.storage_dir,
                self.logging.log_dir,
                str(db_dir)
            )
        except Exception as e:
            logger.warning(f"创建目录时出错: {e}")

        logger.info(f"配置加载完成，环境: {self.environment.environment}")

    def get_model_config(self, model_name: str) -> Optional[ModelConfig]:
        """获取模型配置
        
        Args:
            model_name: 模型名称
            
        Returns:
            Optional[ModelConfig]: 模型配置
        """
        return self.models.get(model_name)

    def get_active_models(self) -> List[str]:
        """获取活跃模型列表
        
        Returns:
            List[str]: 活跃模型名称列表
        """
        return [name for name in self.active_models if name in self.models]

    def is_model_enabled(self, model_name: str) -> bool:
        """检查模型是否启用
        
        Args:
            model_name: 模型名称
            
        Returns:
            bool: 是否启用
        """
        model_config = self.get_model_config(model_name)
        return (model_config is not None and 
                model_config.enabled and 
                model_name in self.active_models)

    def enable_model(self, model_name: str) -> bool:
        """启用模型
        
        Args:
            model_name: 模型名称
            
        Returns:
            bool: 是否成功
        """
        if model_name in self.models and model_name not in self.active_models:
            self.active_models.append(model_name)
            self.models[model_name].enabled = True
            return True
        return False

    def disable_model(self, model_name: str) -> bool:
        """禁用模型
        
        Args:
            model_name: 模型名称
            
        Returns:
            bool: 是否成功
        """
        if model_name in self.active_models:
            self.active_models.remove(model_name)
            if model_name in self.models:
                self.models[model_name].enabled = False
            return True
        return False

    def validate_configuration(self) -> List[str]:
        """验证配置
        
        Returns:
            List[str]: 验证错误列表
        """
        errors = []
        
        # 验证必需的环境变量
        required_vars = []
        for model_name, model_config in self.models.items():
            if model_config.enabled:
                env_var = f"{model_name.upper()}_API_KEY"
                if not os.getenv(env_var):
                    errors.append(f"缺少环境变量: {env_var}")
        
        # 验证路径
        try:
            ConfigValidator.validate_paths(
                self.storage.storage_dir,
                self.logging.log_dir
            )
        except ValueError as e:
            errors.append(str(e))
        
        return errors


@lru_cache(maxsize=1)
def get_unified_config() -> UnifiedConfig:
    """获取统一配置实例
    
    Returns:
        UnifiedConfig: 配置实例
    """
    return UnifiedConfig()


# 便捷函数
def get_database_config() -> DatabaseConfig:
    """获取数据库配置"""
    return get_unified_config().database


def get_server_config() -> ServerConfig:
    """获取服务器配置"""
    return get_unified_config().server


def get_security_config() -> SecurityConfig:
    """获取安全配置"""
    return get_unified_config().security


def get_logging_config() -> LoggingConfig:
    """获取日志配置"""
    return get_unified_config().logging


def get_model_config(model_name: str) -> Optional[ModelConfig]:
    """获取模型配置"""
    return get_unified_config().get_model_config(model_name)


def get_active_models() -> List[str]:
    """获取活跃模型列表"""
    return get_unified_config().get_active_models()


# 向后兼容的全局实例
config = get_unified_config()
