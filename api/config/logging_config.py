"""统一日志配置管理"""

import os
import re
import sys
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
from utils.logger import logger
from pydantic import BaseModel, Field


class LoggingConfig(BaseModel):
    """日志配置类"""

    # 基础配置
    level: str = Field(default="INFO", description="日志级别")
    format: str = Field(
        default="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} - {message}",
        description="日志格式"
    )

    # 文件配置
    log_dir: str = Field(default="logs", description="日志目录")
    file_rotation: str = Field(default="10 MB", description="文件轮转大小")
    file_retention: str = Field(default="30 days", description="文件保留时间")
    file_compression: str = Field(default="zip", description="文件压缩格式")

    # 控制台配置
    console_enabled: bool = Field(default=True, description="是否启用控制台输出")
    console_colorize: bool = Field(default=True, description="控制台是否彩色输出")

    # 特殊日志配置
    access_log_enabled: bool = Field(default=True, description="是否启用访问日志")
    error_log_enabled: bool = Field(default=True, description="是否启用错误日志")
    performance_log_enabled: bool = Field(default=True, description="是否启用性能日志")

    # 第三方库日志级别
    third_party_levels: Dict[str, str] = Field(
        default_factory=lambda: {
            "uvicorn": "INFO",
            "fastapi": "INFO",
            "sqlalchemy": "WARNING",
            "httpx": "WARNING",
            "openai": "WARNING",
            "anthropic": "WARNING"
        },
        description="第三方库日志级别"
    )

    # 敏感信息过滤
    sensitive_patterns: List[str] = Field(
        default_factory=lambda: [
            r"api[_-]?key",
            r"secret",
            r"password",
            r"token",
            r"authorization"
        ],
        description="敏感信息过滤模式"
    )


class LoggerManager:
    """日志管理器"""

    def __init__(self, config: LoggingConfig):
        self.config = config
        self.log_dir = Path(config.log_dir)
        self._setup_directories()
        self._configured = False

    def _setup_directories(self) -> None:
        """创建日志目录"""
        self.log_dir.mkdir(parents=True, exist_ok=True)

        # 创建子目录
        (self.log_dir / "app").mkdir(exist_ok=True)
        (self.log_dir / "access").mkdir(exist_ok=True)
        (self.log_dir / "error").mkdir(exist_ok=True)
        (self.log_dir / "performance").mkdir(exist_ok=True)

    def configure(self) -> None:
        """配置日志系统"""
        if self._configured:
            return

        # 移除默认处理器
        logger.remove()

        # 配置控制台输出
        if self.config.console_enabled:
            logger.add(
                sys.stderr,
                format=self.config.format,
                level=self.config.level,
                colorize=self.config.console_colorize,
                filter=self._sensitive_filter
            )

        # 配置主应用日志
        logger.add(
            self.log_dir / "app" / "app_{time:YYYY-MM-DD}.log",
            format=self.config.format,
            level=self.config.level,
            rotation=self.config.file_rotation,
            retention=self.config.file_retention,
            compression=self.config.file_compression,
            filter=self._sensitive_filter,
            enqueue=True
        )

        # 配置错误日志
        if self.config.error_log_enabled:
            logger.add(
                self.log_dir / "error" / "error_{time:YYYY-MM-DD}.log",
                format=self.config.format,
                level="ERROR",
                rotation=self.config.file_rotation,
                retention=self.config.file_retention,
                compression=self.config.file_compression,
                filter=self._sensitive_filter,
                enqueue=True
            )

        # 配置第三方库日志级别
        self._configure_third_party_loggers()

        self._configured = True
        logger.info("日志系统配置完成")

    def _sensitive_filter(self, record: Dict[str, Any]) -> bool:
        """敏感信息过滤器"""
        message = record["message"]
        for pattern in self.config.sensitive_patterns:
            # 简单的敏感信息脱敏
            message = re.sub(
                f"({pattern})\\s*[:=]\\s*['\"]?([^\\s'\"]+)['\"]?",
                r"\1: ***",
                message,
                flags=re.IGNORECASE
            )

        record["message"] = message
        return True

    def _configure_third_party_loggers(self) -> None:
        """配置第三方库日志级别"""
        for logger_name, level in self.config.third_party_levels.items():
            logging.getLogger(logger_name).setLevel(getattr(logging, level))

    def get_logger(self, name: str) -> logger:
        """获取指定名称的日志器"""
        return logger.bind(name=name)

    def _create_specialized_logger(
        self,
        logger_type: str,
        log_subdir: str,
        log_format: str,
        filter_func: callable
    ) -> logger:
        """创建专用日志器的通用方法"""
        specialized_logger = logger.bind(logger_type=logger_type)
        specialized_logger.add(
            self.log_dir / log_subdir / f"{logger_type}_{{time:YYYY-MM-DD}}.log",
            format=log_format,
            level="INFO",
            rotation=self.config.file_rotation,
            retention=self.config.file_retention,
            compression=self.config.file_compression,
            filter=filter_func,
            enqueue=True
        )
        return specialized_logger

    def create_access_logger(self) -> Optional[logger]:
        """创建访问日志器"""
        if not self.config.access_log_enabled:
            return None

        access_format = (
            "{time:YYYY-MM-DD HH:mm:ss.SSS} | {extra[method]} {extra[path]} | "
            "{extra[status_code]} | {extra[duration]}ms | {extra[client_ip]} | "
            "{extra[user_agent]}"
        )

        return self._create_specialized_logger(
            "access",
            "access",
            access_format,
            lambda record: record["extra"].get("logger_type") == "access"
        )

    def create_performance_logger(self) -> Optional[logger]:
        """创建性能日志器"""
        if not self.config.performance_log_enabled:
            return None

        perf_format = (
            "{time:YYYY-MM-DD HH:mm:ss.SSS} | {extra[operation]} | "
            "{extra[duration]}ms | {extra[details]}"
        )

        return self._create_specialized_logger(
            "performance",
            "performance",
            perf_format,
            lambda record: record["extra"].get("logger_type") == "performance"
        )


def create_logging_config(
        level: Optional[str] = None,
        log_dir: Optional[str] = None,
        **kwargs
) -> LoggingConfig:
    """创建日志配置"""
    config_data = {
        "level": level or os.getenv("LOG_LEVEL", "INFO"),
        "log_dir": log_dir or os.getenv("LOG_DIR", "logs"),
        **kwargs
    }
    return LoggingConfig(**config_data)


# 全局日志管理器实例
_logger_manager: Optional[LoggerManager] = None


def get_logger_manager() -> LoggerManager:
    """获取全局日志管理器"""
    global _logger_manager
    if _logger_manager is None:
        config = create_logging_config()
        _logger_manager = LoggerManager(config)
        _logger_manager.configure()
    return _logger_manager


def setup_logging(
        level: Optional[str] = None,
        log_dir: Optional[str] = None,
        **kwargs
) -> LoggerManager:
    """设置日志系统"""
    global _logger_manager
    config = create_logging_config(level=level, log_dir=log_dir, **kwargs)
    _logger_manager = LoggerManager(config)
    _logger_manager.configure()
    return _logger_manager


# 便捷函数
def get_app_logger(name: str = "app") -> logger:
    """获取应用日志器"""
    return get_logger_manager().get_logger(name)


def get_access_logger() -> Optional[logger]:
    """获取访问日志器"""
    return get_logger_manager().create_access_logger()


def get_performance_logger() -> Optional[logger]:
    """获取性能日志器"""
    return get_logger_manager().create_performance_logger()


# 静态工具方法
class LoggingUtils:
    """日志工具类"""

    @staticmethod
    def validate_log_level(level: str) -> bool:
        """验证日志级别是否有效"""
        valid_levels = {"DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"}
        return level.upper() in valid_levels

    @staticmethod
    def sanitize_log_message(message: str, patterns: List[str]) -> str:
        """清理日志消息中的敏感信息"""
        for pattern in patterns:
            message = re.sub(
                f"({pattern})\\s*[:=]\\s*['\"]?([^\\s'\"]+)['\"]?",
                r"\1: ***",
                message,
                flags=re.IGNORECASE
            )
        return message
