# -*- coding: utf-8 -*-
"""
模型管理API路由
"""

from typing import List, Optional, Dict, Any

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from api.dependencies.auth import get_optional_api_key, require_model_permission
from api.dependencies.database import get_db
from api.models.schemas import (
    ModelInfo, ModelListResponse, ModelConfigUpdate, BaseResponse
)
from database.models import APIKey
from src.core.model_manager import ModelManager
from src.utils.config_optimized import OptimizedConfigManager
from utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/models", tags=["models"])

# 初始化核心组件
config_manager = OptimizedConfigManager()
model_manager = ModelManager(config_manager)


@router.get("/", response_model=ModelListResponse)
async def list_models(
        include_disabled: bool = Query(False, description="是否包含禁用的模型"),
        provider_filter: Optional[str] = Query(None, description="按提供商过滤"),
        db: Session = Depends(get_db),
        api_key: Optional[APIKey] = Depends(get_optional_api_key)
):
    """
    获取模型列表
    
    Args:
        include_disabled: 是否包含禁用的模型
        provider_filter: 提供商过滤
        db: 数据库会话
        api_key: API密钥（可选）
        
    Returns:
        ModelListResponse: 模型列表响应
    """
    try:
        # 获取所有模型配置
        all_models = model_manager.get_all_models()
        active_models = model_manager.get_active_models()

        models_info = []

        for model_name, model_instance in all_models.items():
            # 获取模型配置
            model_config = config_manager.get_model_config(model_name)
            if not model_config:
                continue

            # 检查是否启用
            is_enabled = model_name in active_models

            # 如果不包含禁用模型且当前模型被禁用，跳过
            if not include_disabled and not is_enabled:
                continue

            # 获取提供商信息
            provider = _get_model_provider(model_name)

            # 按提供商过滤
            if provider_filter and provider.lower() != provider_filter.lower():
                continue

            # 检查模型可用性
            is_available = await _check_model_availability(model_instance)

            # 构建模型信息
            model_info = ModelInfo(
                name=model_name,
                display_name=model_config.get("display_name", model_name),
                description=model_config.get("description", ""),
                provider=provider,
                version=model_config.get("version"),
                is_enabled=is_enabled,
                is_available=is_available,
                config=_sanitize_model_config(model_config),
                capabilities=_get_model_capabilities(model_name),
                limits=_get_model_limits(model_config)
            )

            models_info.append(model_info)

        # 统计信息
        total = len(models_info)
        enabled = sum(1 for m in models_info if m.is_enabled)
        available = sum(1 for m in models_info if m.is_available)

        logger.info(f"Listed {total} models (enabled: {enabled}, available: {available})")

        return ModelListResponse(
            success=True,
            message="Models retrieved successfully",
            models=models_info,
            total=total,
            enabled=enabled,
            available=available
        )

    except Exception as e:
        logger.error(f"Error listing models: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve models"
        )


@router.get("/{model_name}", response_model=ModelInfo)
async def get_model(
        model_name: str,
        db: Session = Depends(get_db),
        api_key: Optional[APIKey] = Depends(get_optional_api_key)
):
    """
    获取指定模型信息
    
    Args:
        model_name: 模型名称
        db: 数据库会话
        api_key: API密钥（可选）
        
    Returns:
        ModelInfo: 模型信息
        
    Raises:
        HTTPException: 模型不存在时抛出404错误
    """
    try:
        # 检查模型是否存在
        all_models = model_manager.get_all_models()
        if model_name not in all_models:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Model not found"
            )

        model_instance = all_models[model_name]
        model_config = config_manager.get_model_config(model_name)
        active_models = model_manager.get_active_models()

        # 检查模型可用性
        is_available = await _check_model_availability(model_instance)

        # 构建模型信息
        model_info = ModelInfo(
            name=model_name,
            display_name=model_config.get("display_name", model_name),
            description=model_config.get("description", ""),
            provider=_get_model_provider(model_name),
            version=model_config.get("version"),
            is_enabled=model_name in active_models,
            is_available=is_available,
            config=_sanitize_model_config(model_config),
            capabilities=_get_model_capabilities(model_name),
            limits=_get_model_limits(model_config)
        )

        logger.info(f"Retrieved model info: {model_name}")

        return model_info

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving model {model_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve model"
        )


@router.put("/{model_name}/config", response_model=BaseResponse)
async def update_model_config(
        model_name: str,
        config_update: ModelConfigUpdate,
        db: Session = Depends(get_db),
        api_key: APIKey = Depends(require_model_permission)
):
    """
    更新模型配置
    
    Args:
        model_name: 模型名称
        config_update: 配置更新数据
        db: 数据库会话
        api_key: API密钥
        
    Returns:
        BaseResponse: 更新结果
        
    Raises:
        HTTPException: 模型不存在时抛出404错误
    """
    try:
        # 检查模型是否存在
        all_models = model_manager.get_all_models()
        if model_name not in all_models:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Model not found"
            )

        # 更新启用状态
        if config_update.is_enabled is not None:
            if config_update.is_enabled:
                model_manager.enable_model(model_name)
                logger.info(f"Model enabled: {model_name} by API key: {api_key.name}")
            else:
                model_manager.disable_model(model_name)
                logger.info(f"Model disabled: {model_name} by API key: {api_key.name}")

        # 更新配置参数
        if config_update.config:
            current_config = config_manager.get_model_config(model_name) or {}
            updated_config = {**current_config, **config_update.config}

            # 验证配置参数
            _validate_model_config(model_name, updated_config)

            # 保存配置
            config_manager.update_model_config(model_name, updated_config)
            logger.info(f"Model config updated: {model_name} by API key: {api_key.name}")

        return BaseResponse(
            success=True,
            message="Model configuration updated successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating model config {model_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update model configuration"
        )


@router.post("/{model_name}/enable", response_model=BaseResponse)
async def enable_model(
        model_name: str,
        db: Session = Depends(get_db),
        api_key: APIKey = Depends(require_model_permission)
):
    """
    启用模型
    
    Args:
        model_name: 模型名称
        db: 数据库会话
        api_key: API密钥
        
    Returns:
        BaseResponse: 启用结果
    """
    try:
        # 检查模型是否存在
        all_models = model_manager.get_all_models()
        if model_name not in all_models:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Model not found"
            )

        # 启用模型
        model_manager.enable_model(model_name)

        logger.info(f"Model enabled: {model_name} by API key: {api_key.name}")

        return BaseResponse(
            success=True,
            message=f"Model {model_name} enabled successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error enabling model {model_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to enable model"
        )


@router.post("/{model_name}/disable", response_model=BaseResponse)
async def disable_model(
        model_name: str,
        db: Session = Depends(get_db),
        api_key: APIKey = Depends(require_model_permission)
):
    """
    禁用模型
    
    Args:
        model_name: 模型名称
        db: 数据库会话
        api_key: API密钥
        
    Returns:
        BaseResponse: 禁用结果
    """
    try:
        # 检查模型是否存在
        all_models = model_manager.get_all_models()
        if model_name not in all_models:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Model not found"
            )

        # 禁用模型
        model_manager.disable_model(model_name)

        logger.info(f"Model disabled: {model_name} by API key: {api_key.name}")

        return BaseResponse(
            success=True,
            message=f"Model {model_name} disabled successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error disabling model {model_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to disable model"
        )


@router.post("/{model_name}/test", response_model=BaseResponse)
async def test_model(
        model_name: str,
        test_message: str = Query("Hello, how are you?", description="测试消息"),
        db: Session = Depends(get_db),
        api_key: APIKey = Depends(require_model_permission)
):
    """
    测试模型连接
    
    Args:
        model_name: 模型名称
        test_message: 测试消息
        db: 数据库会话
        api_key: API密钥
        
    Returns:
        BaseResponse: 测试结果
    """
    try:
        # 检查模型是否存在
        all_models = model_manager.get_all_models()
        if model_name not in all_models:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Model not found"
            )

        model_instance = all_models[model_name]

        # 执行测试
        try:
            messages = [{"role": "user", "content": test_message}]
            response = await model_instance.chat(messages)

            if response:
                logger.info(f"Model test successful: {model_name} by API key: {api_key.name}")
                return BaseResponse(
                    success=True,
                    message=f"Model {model_name} test successful"
                )
            else:
                raise Exception("Empty response from model")

        except Exception as test_error:
            logger.warning(f"Model test failed: {model_name} - {test_error}")
            return BaseResponse(
                success=False,
                message=f"Model {model_name} test failed: {str(test_error)}"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error testing model {model_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to test model"
        )


# 辅助函数

def _get_model_provider(model_name: str) -> str:
    """
    获取模型提供商
    
    Args:
        model_name: 模型名称
        
    Returns:
        str: 提供商名称
    """
    provider_mapping = {
        "openai": "OpenAI",
        "gemini": "Google",
        "doubao": "ByteDance",
        "qianwen": "Alibaba",
        "claude": "Anthropic"
    }

    for key, provider in provider_mapping.items():
        if key in model_name.lower():
            return provider

    return "Unknown"


async def _check_model_availability(model_instance) -> bool:
    """
    检查模型可用性
    
    Args:
        model_instance: 模型实例
        
    Returns:
        bool: 是否可用
    """
    try:
        # 简单的健康检查
        test_messages = [{"role": "user", "content": "test"}]
        response = await model_instance.chat(test_messages)
        return bool(response)
    except Exception:
        return False


def _sanitize_model_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    清理模型配置，移除敏感信息
    
    Args:
        config: 原始配置
        
    Returns:
        Dict[str, Any]: 清理后的配置
    """
    sensitive_keys = ["api_key", "secret", "token", "password"]

    sanitized = {}
    for key, value in config.items():
        if any(sensitive in key.lower() for sensitive in sensitive_keys):
            sanitized[key] = "***"
        else:
            sanitized[key] = value

    return sanitized


def _get_model_capabilities(model_name: str) -> List[str]:
    """
    获取模型能力列表
    
    Args:
        model_name: 模型名称
        
    Returns:
        List[str]: 能力列表
    """
    # 基础能力
    capabilities = ["text_generation", "conversation"]

    # 根据模型名称添加特定能力
    if "gpt-4" in model_name.lower():
        capabilities.extend(["advanced_reasoning", "code_generation"])

    if "vision" in model_name.lower() or "v" in model_name.lower():
        capabilities.append("image_understanding")

    if "claude" in model_name.lower():
        capabilities.extend(["long_context", "analysis"])

    return capabilities


def _get_model_limits(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    获取模型限制信息
    
    Args:
        config: 模型配置
        
    Returns:
        Dict[str, Any]: 限制信息
    """
    return {
        "max_tokens": config.get("max_tokens", 4096),
        "timeout": config.get("timeout", 30),
        "rate_limit": config.get("rate_limit", "60/min"),
        "context_length": config.get("context_length", 4096)
    }


def _validate_model_config(model_name: str, config: Dict[str, Any]):
    """
    验证模型配置
    
    Args:
        model_name: 模型名称
        config: 配置参数
        
    Raises:
        ValueError: 配置无效时抛出
    """
    # 验证必需的配置项
    required_fields = ["max_tokens", "timeout"]

    for field in required_fields:
        if field not in config:
            raise ValueError(f"Missing required config field: {field}")

    # 验证数值范围
    if config.get("max_tokens", 0) <= 0:
        raise ValueError("max_tokens must be positive")

    if config.get("timeout", 0) <= 0:
        raise ValueError("timeout must be positive")

    if "temperature" in config:
        temp = config["temperature"]
        if not (0 <= temp <= 2):
            raise ValueError("temperature must be between 0 and 2")
