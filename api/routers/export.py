#!/usr/bin/env python3
"""
数据导出API路由
提供会话、对话和统计数据的导出功能
"""

import csv
import io
import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any

from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks
from fastapi.responses import FileResponse
from sqlalchemy import desc
from sqlalchemy.orm import Session

from api.dependencies.auth import require_api_key
from api.dependencies.database import get_database
from api.models.schemas import (
    ExportRequest, ExportResponse, BaseResponse, ResponseFormat
)
from database.models import Session as SessionModel, Conversation, Prompt, APIKey
from utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter(prefix="/export", tags=["数据导出"])

# 导出文件存储目录
EXPORT_DIR = Path("data/exports")
EXPORT_DIR.mkdir(parents=True, exist_ok=True)

# 文件过期时间（小时）
FILE_EXPIRY_HOURS = 24


def require_export_permission(api_key: APIKey = Depends(require_api_key)) -> APIKey:
    """检查导出权限"""
    if not api_key.permissions.get("export", False):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Insufficient permissions for export operations"
        )
    return api_key


def cleanup_expired_files():
    """清理过期的导出文件"""
    try:
        cutoff_time = datetime.now() - timedelta(hours=FILE_EXPIRY_HOURS)

        for file_path in EXPORT_DIR.glob("*"):
            if file_path.is_file():
                file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                if file_time < cutoff_time:
                    file_path.unlink()
                    logger.info(f"Deleted expired export file: {file_path.name}")
    except Exception as e:
        logger.error(f"Error cleaning up expired files: {e}")


def export_to_json(data: List[Dict[str, Any]], include_metadata: bool = True) -> str:
    """导出为JSON格式"""
    export_data = {
        "data": data,
        "total_count": len(data),
        "exported_at": datetime.now().isoformat()
    }

    if include_metadata:
        export_data["metadata"] = {
            "format": "json",
            "version": "1.0",
            "source": "LLM Comparison Tool"
        }

    return json.dumps(export_data, indent=2, ensure_ascii=False, default=str)


def export_to_csv(data: List[Dict[str, Any]], include_metadata: bool = True) -> str:
    """导出为CSV格式"""
    if not data:
        return "No data to export"

    output = io.StringIO()

    # 写入元数据（如果需要）
    if include_metadata:
        output.write(f"# Exported from LLM Comparison Tool\n")
        output.write(f"# Export Date: {datetime.now().isoformat()}\n")
        output.write(f"# Total Records: {len(data)}\n")
        output.write("\n")

    # 写入CSV数据
    if data:
        fieldnames = data[0].keys()
        writer = csv.DictWriter(output, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(data)

    return output.getvalue()


def export_to_txt(data: List[Dict[str, Any]], include_metadata: bool = True) -> str:
    """导出为TXT格式"""
    lines = []

    if include_metadata:
        lines.extend([
            "LLM Comparison Tool Export",
            "=" * 50,
            f"Export Date: {datetime.now().isoformat()}",
            f"Total Records: {len(data)}",
            "=" * 50,
            ""
        ])

    for i, item in enumerate(data, 1):
        lines.append(f"Record {i}:")
        lines.append("-" * 20)

        for key, value in item.items():
            lines.append(f"{key}: {value}")

        lines.append("")

    return "\n".join(lines)


@router.post("/sessions", response_model=ExportResponse)
async def export_sessions(
        request: ExportRequest,
        background_tasks: BackgroundTasks,
        db: Session = Depends(get_database),
        api_key: APIKey = Depends(require_export_permission)
):
    """
    导出会话数据
    
    Args:
        request: 导出请求参数
        background_tasks: 后台任务
        db: 数据库会话
        api_key: API密钥
        
    Returns:
        ExportResponse: 导出响应，包含下载链接
    """
    try:
        # 构建查询
        query = db.query(SessionModel)

        # 应用过滤条件
        if request.session_id:
            query = query.filter(SessionModel.id == request.session_id)

        if request.start_date:
            query = query.filter(SessionModel.created_at >= request.start_date)

        if request.end_date:
            query = query.filter(SessionModel.created_at <= request.end_date)

        # 获取数据
        sessions = query.order_by(desc(SessionModel.created_at)).all()

        # 转换为字典格式
        session_data = []
        for session in sessions:
            session_dict = {
                "id": session.id,
                "title": session.title,
                "status": session.status.value,
                "created_at": session.created_at.isoformat(),
                "updated_at": session.updated_at.isoformat(),
                "last_activity": session.last_activity.isoformat() if session.last_activity else None,
                "conversation_count": len(session.conversations),
                "total_tokens": sum(conv.input_tokens + conv.output_tokens
                                    for conv in session.conversations
                                    if conv.input_tokens and conv.output_tokens)
            }
            session_data.append(session_dict)

        # 生成文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"sessions_export_{timestamp}.{request.format.value}"
        file_path = EXPORT_DIR / filename

        # 根据格式导出
        if request.format == ResponseFormat.JSON:
            content = export_to_json(session_data, request.include_metadata)
        elif request.format == ResponseFormat.CSV:
            content = export_to_csv(session_data, request.include_metadata)
        elif request.format == ResponseFormat.TXT:
            content = export_to_txt(session_data, request.include_metadata)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported export format: {request.format}"
            )

        # 写入文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)

        # 添加清理任务
        background_tasks.add_task(cleanup_expired_files)

        logger.info(f"Sessions exported by API key {api_key.name}: {len(session_data)} records")

        return ExportResponse(
            success=True,
            message="Sessions exported successfully",
            download_url=f"/export/download/{filename}",
            file_size=file_path.stat().st_size,
            record_count=len(session_data),
            expires_at=datetime.now() + timedelta(hours=FILE_EXPIRY_HOURS)
        )

    except Exception as e:
        logger.error(f"Error exporting sessions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to export sessions"
        )


@router.post("/conversations", response_model=ExportResponse)
async def export_conversations(
        request: ExportRequest,
        background_tasks: BackgroundTasks,
        db: Session = Depends(get_database),
        api_key: APIKey = Depends(require_export_permission)
):
    """
    导出对话数据
    
    Args:
        request: 导出请求参数
        background_tasks: 后台任务
        db: 数据库会话
        api_key: API密钥
        
    Returns:
        ExportResponse: 导出响应，包含下载链接
    """
    try:
        # 构建查询
        query = db.query(Conversation)

        # 应用过滤条件
        if request.session_id:
            query = query.filter(Conversation.session_id == request.session_id)

        if request.model_name:
            query = query.filter(Conversation.model_name == request.model_name)

        if request.start_date:
            query = query.filter(Conversation.timestamp >= request.start_date)

        if request.end_date:
            query = query.filter(Conversation.timestamp <= request.end_date)

        # 获取数据
        conversations = query.order_by(desc(Conversation.timestamp)).all()

        # 转换为字典格式
        conversation_data = []
        for conv in conversations:
            conv_dict = {
                "id": conv.id,
                "session_id": conv.session_id,
                "model_name": conv.model_name,
                "prompt": conv.prompt,
                "response": conv.response,
                "status": conv.status.value,
                "timestamp": conv.timestamp.isoformat(),
                "response_time": conv.response_time,
                "input_tokens": conv.input_tokens,
                "output_tokens": conv.output_tokens,
                "error_message": conv.error_message
            }
            conversation_data.append(conv_dict)

        # 生成文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"conversations_export_{timestamp}.{request.format.value}"
        file_path = EXPORT_DIR / filename

        # 根据格式导出
        if request.format == ResponseFormat.JSON:
            content = export_to_json(conversation_data, request.include_metadata)
        elif request.format == ResponseFormat.CSV:
            content = export_to_csv(conversation_data, request.include_metadata)
        elif request.format == ResponseFormat.TXT:
            content = export_to_txt(conversation_data, request.include_metadata)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported export format: {request.format}"
            )

        # 写入文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)

        # 添加清理任务
        background_tasks.add_task(cleanup_expired_files)

        logger.info(f"Conversations exported by API key {api_key.name}: {len(conversation_data)} records")

        return ExportResponse(
            success=True,
            message="Conversations exported successfully",
            download_url=f"/export/download/{filename}",
            file_size=file_path.stat().st_size,
            record_count=len(conversation_data),
            expires_at=datetime.now() + timedelta(hours=FILE_EXPIRY_HOURS)
        )

    except Exception as e:
        logger.error(f"Error exporting conversations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to export conversations"
        )


@router.post("/prompts", response_model=ExportResponse)
async def export_prompts(
        format: ResponseFormat = Query(ResponseFormat.JSON, description="导出格式"),
        include_metadata: bool = Query(True, description="是否包含元数据"),
        background_tasks: BackgroundTasks = BackgroundTasks(),
        db: Session = Depends(get_database),
        api_key: APIKey = Depends(require_export_permission)
):
    """
    导出提示词模板数据
    
    Args:
        format: 导出格式
        include_metadata: 是否包含元数据
        background_tasks: 后台任务
        db: 数据库会话
        api_key: API密钥
        
    Returns:
        ExportResponse: 导出响应，包含下载链接
    """
    try:
        # 获取所有提示词
        prompts = db.query(Prompt).order_by(Prompt.name).all()

        # 转换为字典格式
        prompt_data = []
        for prompt in prompts:
            prompt_dict = {
                "id": prompt.id,
                "name": prompt.name,
                "content": prompt.content,
                "description": prompt.description,
                "category": prompt.category,
                "is_active": prompt.is_active,
                "is_system": prompt.is_system,
                "created_at": prompt.created_at.isoformat(),
                "updated_at": prompt.updated_at.isoformat()
            }
            prompt_data.append(prompt_dict)

        # 生成文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"prompts_export_{timestamp}.{format.value}"
        file_path = EXPORT_DIR / filename

        # 根据格式导出
        if format == ResponseFormat.JSON:
            content = export_to_json(prompt_data, include_metadata)
        elif format == ResponseFormat.CSV:
            content = export_to_csv(prompt_data, include_metadata)
        elif format == ResponseFormat.TXT:
            content = export_to_txt(prompt_data, include_metadata)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported export format: {format}"
            )

        # 写入文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)

        # 添加清理任务
        background_tasks.add_task(cleanup_expired_files)

        logger.info(f"Prompts exported by API key {api_key.name}: {len(prompt_data)} records")

        return ExportResponse(
            success=True,
            message="Prompts exported successfully",
            download_url=f"/export/download/{filename}",
            file_size=file_path.stat().st_size,
            record_count=len(prompt_data),
            expires_at=datetime.now() + timedelta(hours=FILE_EXPIRY_HOURS)
        )

    except Exception as e:
        logger.error(f"Error exporting prompts: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to export prompts"
        )


@router.get("/download/{filename}")
async def download_export_file(
        filename: str,
        api_key: APIKey = Depends(require_export_permission)
):
    """
    下载导出文件
    
    Args:
        filename: 文件名
        api_key: API密钥
        
    Returns:
        FileResponse: 文件下载响应
    """
    try:
        file_path = EXPORT_DIR / filename

        if not file_path.exists():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Export file not found or expired"
            )

        # 检查文件是否过期
        file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
        if datetime.now() - file_time > timedelta(hours=FILE_EXPIRY_HOURS):
            file_path.unlink()  # 删除过期文件
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Export file has expired"
            )

        logger.info(f"Export file downloaded by API key {api_key.name}: {filename}")

        return FileResponse(
            path=file_path,
            filename=filename,
            media_type='application/octet-stream'
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error downloading export file: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to download export file"
        )


@router.get("/files", response_model=BaseResponse)
async def list_export_files(
        api_key: APIKey = Depends(require_export_permission)
):
    """
    列出可用的导出文件
    
    Args:
        api_key: API密钥
        
    Returns:
        BaseResponse: 文件列表
    """
    try:
        files = []
        cutoff_time = datetime.now() - timedelta(hours=FILE_EXPIRY_HOURS)

        for file_path in EXPORT_DIR.glob("*"):
            if file_path.is_file():
                file_time = datetime.fromtimestamp(file_path.stat().st_mtime)

                # 跳过过期文件
                if file_time < cutoff_time:
                    continue

                files.append({
                    "filename": file_path.name,
                    "size": file_path.stat().st_size,
                    "created_at": file_time.isoformat(),
                    "expires_at": (file_time + timedelta(hours=FILE_EXPIRY_HOURS)).isoformat(),
                    "download_url": f"/export/download/{file_path.name}"
                })

        # 按创建时间排序
        files.sort(key=lambda x: x["created_at"], reverse=True)

        logger.info(f"Export files listed by API key {api_key.name}: {len(files)} files")

        return BaseResponse(
            success=True,
            message="Export files retrieved successfully",
            data={
                "files": files,
                "total_count": len(files)
            }
        )

    except Exception as e:
        logger.error(f"Error listing export files: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list export files"
        )


@router.delete("/files/{filename}", response_model=BaseResponse)
async def delete_export_file(
        filename: str,
        api_key: APIKey = Depends(require_export_permission)
):
    """
    删除导出文件
    
    Args:
        filename: 文件名
        api_key: API密钥
        
    Returns:
        BaseResponse: 删除结果
    """
    try:
        file_path = EXPORT_DIR / filename

        if not file_path.exists():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Export file not found"
            )

        file_path.unlink()

        logger.info(f"Export file deleted by API key {api_key.name}: {filename}")

        return BaseResponse(
            success=True,
            message="Export file deleted successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting export file: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete export file"
        )


@router.post("/cleanup", response_model=BaseResponse)
async def cleanup_export_files(
        api_key: APIKey = Depends(require_export_permission)
):
    """
    清理过期的导出文件
    
    Args:
        api_key: API密钥
        
    Returns:
        BaseResponse: 清理结果
    """
    try:
        deleted_count = 0
        cutoff_time = datetime.now() - timedelta(hours=FILE_EXPIRY_HOURS)

        for file_path in EXPORT_DIR.glob("*"):
            if file_path.is_file():
                file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                if file_time < cutoff_time:
                    file_path.unlink()
                    deleted_count += 1

        logger.info(f"Export files cleanup by API key {api_key.name}: {deleted_count} files deleted")

        return BaseResponse(
            success=True,
            message=f"Cleanup completed: {deleted_count} expired files deleted",
            data={"deleted_count": deleted_count}
        )

    except Exception as e:
        logger.error(f"Error cleaning up export files: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to cleanup export files"
        )
