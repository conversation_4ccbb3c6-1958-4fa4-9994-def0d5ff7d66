# -*- coding: utf-8 -*-
"""
提示词管理API路由
"""

import uuid
from datetime import datetime
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy import desc, or_
from sqlalchemy.orm import Session

from api.dependencies.auth import require_prompt_permission, get_optional_api_key
from api.dependencies.database import get_db
from api.models.schemas import (
    PromptCreate, PromptUpdate, PromptResponse, PromptListResponse,
    PromptRenderRequest, PromptRenderResponse, PaginationParams, BaseResponse
)
from database.models import Prompt, APIKey
from utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/prompts", tags=["prompts"])


@router.post("/", response_model=PromptResponse, status_code=status.HTTP_201_CREATED)
async def create_prompt(
        prompt_data: PromptCreate,
        db: Session = Depends(get_db),
        api_key: APIKey = Depends(require_prompt_permission)
):
    """
    创建新提示词
    
    Args:
        prompt_data: 提示词创建数据
        db: 数据库会话
        api_key: API密钥
        
    Returns:
        PromptResponse: 创建的提示词信息
        
    Raises:
        HTTPException: 创建失败时抛出
    """
    try:
        # 检查名称是否已存在
        existing_prompt = db.query(Prompt).filter(
            Prompt.name == prompt_data.name
        ).first()

        if existing_prompt:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Prompt with this name already exists"
            )

        # 生成提示词ID
        prompt_id = str(uuid.uuid4())

        # 提取变量（从内容中查找 {variable} 格式）
        variables = _extract_variables_from_content(prompt_data.content)
        if prompt_data.variables:
            # 合并用户指定的变量
            variables = list(set(variables + prompt_data.variables))

        # 创建提示词对象
        prompt = Prompt(
            id=prompt_id,
            name=prompt_data.name,
            description=prompt_data.description,
            content=prompt_data.content,
            variables=variables,
            category=prompt_data.category,
            tags=prompt_data.tags or [],
            version=prompt_data.version,
            parent_id=prompt_data.parent_id,
            is_active=True,
            is_system=False
        )

        # 保存到数据库
        db.add(prompt)
        db.commit()
        db.refresh(prompt)

        logger.info(f"Prompt created: {prompt_id} ({prompt_data.name}) by API key: {api_key.name}")

        return PromptResponse.from_orm(prompt)

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating prompt: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create prompt"
        )


@router.get("/", response_model=PromptListResponse)
async def list_prompts(
        pagination: PaginationParams = Depends(),
        category_filter: Optional[str] = Query(None, description="按分类过滤"),
        tag_filter: Optional[str] = Query(None, description="按标签过滤"),
        search: Optional[str] = Query(None, description="搜索关键词"),
        include_inactive: bool = Query(False, description="是否包含非活跃提示词"),
        include_system: bool = Query(True, description="是否包含系统提示词"),
        db: Session = Depends(get_db),
        api_key: Optional[APIKey] = Depends(get_optional_api_key)
):
    """
    获取提示词列表
    
    Args:
        pagination: 分页参数
        category_filter: 分类过滤
        tag_filter: 标签过滤
        search: 搜索关键词
        include_inactive: 是否包含非活跃提示词
        include_system: 是否包含系统提示词
        db: 数据库会话
        api_key: API密钥（可选）
        
    Returns:
        PromptListResponse: 提示词列表
    """
    try:
        # 构建查询
        query = db.query(Prompt)

        # 应用过滤条件
        if not include_inactive:
            query = query.filter(Prompt.is_active == True)

        if not include_system:
            query = query.filter(Prompt.is_system == False)

        if category_filter:
            query = query.filter(Prompt.category == category_filter)

        if tag_filter:
            query = query.filter(Prompt.tags.contains([tag_filter]))

        if search:
            search_term = f"%{search}%"
            query = query.filter(
                or_(
                    Prompt.name.ilike(search_term),
                    Prompt.description.ilike(search_term),
                    Prompt.content.ilike(search_term)
                )
            )

        # 获取总数
        total = query.count()

        # 应用分页和排序
        prompts = query.order_by(desc(Prompt.updated_at)).offset(
            pagination.offset
        ).limit(pagination.size).all()

        # 计算总页数
        pages = (total + pagination.size - 1) // pagination.size

        logger.info(f"Listed {len(prompts)} prompts (page {pagination.page})")

        return PromptListResponse(
            success=True,
            message="Prompts retrieved successfully",
            total=total,
            page=pagination.page,
            size=pagination.size,
            pages=pages,
            data=[PromptResponse.from_orm(prompt) for prompt in prompts]
        )

    except Exception as e:
        logger.error(f"Error listing prompts: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve prompts"
        )


@router.get("/{prompt_id}", response_model=PromptResponse)
async def get_prompt(
        prompt_id: str,
        db: Session = Depends(get_db),
        api_key: Optional[APIKey] = Depends(get_optional_api_key)
):
    """
    获取指定提示词信息
    
    Args:
        prompt_id: 提示词ID
        db: 数据库会话
        api_key: API密钥（可选）
        
    Returns:
        PromptResponse: 提示词信息
        
    Raises:
        HTTPException: 提示词不存在时抛出404错误
    """
    try:
        prompt = db.query(Prompt).filter(
            Prompt.id == prompt_id
        ).first()

        if not prompt:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Prompt not found"
            )

        logger.info(f"Retrieved prompt: {prompt_id}")

        return PromptResponse.from_orm(prompt)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving prompt {prompt_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve prompt"
        )


@router.put("/{prompt_id}", response_model=PromptResponse)
async def update_prompt(
        prompt_id: str,
        prompt_data: PromptUpdate,
        db: Session = Depends(get_db),
        api_key: APIKey = Depends(require_prompt_permission)
):
    """
    更新提示词信息
    
    Args:
        prompt_id: 提示词ID
        prompt_data: 更新数据
        db: 数据库会话
        api_key: API密钥
        
    Returns:
        PromptResponse: 更新后的提示词信息
        
    Raises:
        HTTPException: 提示词不存在时抛出404错误
    """
    try:
        prompt = db.query(Prompt).filter(
            Prompt.id == prompt_id
        ).first()

        if not prompt:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Prompt not found"
            )

        # 检查名称冲突（如果更新了名称）
        if prompt_data.name and prompt_data.name != prompt.name:
            existing_prompt = db.query(Prompt).filter(
                Prompt.name == prompt_data.name,
                Prompt.id != prompt_id
            ).first()

            if existing_prompt:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Prompt with this name already exists"
                )

        # 更新字段
        update_data = prompt_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(prompt, field, value)

        # 如果更新了内容，重新提取变量
        if prompt_data.content:
            variables = _extract_variables_from_content(prompt_data.content)
            if prompt_data.variables:
                variables = list(set(variables + prompt_data.variables))
            prompt.variables = variables

        # 更新时间戳
        prompt.updated_at = datetime.utcnow()

        db.commit()
        db.refresh(prompt)

        logger.info(f"Prompt updated: {prompt_id} by API key: {api_key.name}")

        return PromptResponse.from_orm(prompt)

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error updating prompt {prompt_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update prompt"
        )


@router.delete("/{prompt_id}", response_model=BaseResponse)
async def delete_prompt(
        prompt_id: str,
        db: Session = Depends(get_db),
        api_key: APIKey = Depends(require_prompt_permission)
):
    """
    删除提示词
    
    Args:
        prompt_id: 提示词ID
        db: 数据库会话
        api_key: API密钥
        
    Returns:
        BaseResponse: 删除结果
        
    Raises:
        HTTPException: 提示词不存在时抛出404错误
    """
    try:
        prompt = db.query(Prompt).filter(
            Prompt.id == prompt_id
        ).first()

        if not prompt:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Prompt not found"
            )

        # 检查是否为系统提示词
        if prompt.is_system:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete system prompt"
            )

        # 删除提示词
        db.delete(prompt)
        db.commit()

        logger.info(f"Prompt deleted: {prompt_id} by API key: {api_key.name}")

        return BaseResponse(
            success=True,
            message="Prompt deleted successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error deleting prompt {prompt_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete prompt"
        )


@router.post("/{prompt_id}/render", response_model=PromptRenderResponse)
async def render_prompt(
        prompt_id: str,
        render_request: PromptRenderRequest,
        db: Session = Depends(get_db),
        api_key: Optional[APIKey] = Depends(get_optional_api_key)
):
    """
    渲染提示词模板
    
    Args:
        prompt_id: 提示词ID
        render_request: 渲染请求
        db: 数据库会话
        api_key: API密钥（可选）
        
    Returns:
        PromptRenderResponse: 渲染结果
        
    Raises:
        HTTPException: 提示词不存在时抛出404错误
    """
    try:
        prompt = db.query(Prompt).filter(
            Prompt.id == prompt_id
        ).first()

        if not prompt:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Prompt not found"
            )

        # 渲染提示词
        rendered_content = prompt.render(render_request.variables)

        # 找出实际使用的变量
        variables_used = []
        for var in prompt.variables:
            if var in render_request.variables:
                variables_used.append(var)

        # 更新使用统计
        prompt.usage_count += 1
        prompt.last_used = datetime.utcnow()
        db.commit()

        logger.info(f"Prompt rendered: {prompt_id}")

        return PromptRenderResponse(
            success=True,
            message="Prompt rendered successfully",
            rendered_content=rendered_content,
            variables_used=variables_used
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error rendering prompt {prompt_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to render prompt"
        )


@router.post("/{prompt_id}/activate", response_model=PromptResponse)
async def activate_prompt(
        prompt_id: str,
        db: Session = Depends(get_db),
        api_key: APIKey = Depends(require_prompt_permission)
):
    """
    激活提示词
    
    Args:
        prompt_id: 提示词ID
        db: 数据库会话
        api_key: API密钥
        
    Returns:
        PromptResponse: 激活后的提示词信息
    """
    try:
        prompt = db.query(Prompt).filter(
            Prompt.id == prompt_id
        ).first()

        if not prompt:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Prompt not found"
            )

        prompt.is_active = True
        prompt.updated_at = datetime.utcnow()

        db.commit()
        db.refresh(prompt)

        logger.info(f"Prompt activated: {prompt_id} by API key: {api_key.name}")

        return PromptResponse.from_orm(prompt)

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error activating prompt {prompt_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to activate prompt"
        )


@router.post("/{prompt_id}/deactivate", response_model=PromptResponse)
async def deactivate_prompt(
        prompt_id: str,
        db: Session = Depends(get_db),
        api_key: APIKey = Depends(require_prompt_permission)
):
    """
    停用提示词
    
    Args:
        prompt_id: 提示词ID
        db: 数据库会话
        api_key: API密钥
        
    Returns:
        PromptResponse: 停用后的提示词信息
    """
    try:
        prompt = db.query(Prompt).filter(
            Prompt.id == prompt_id
        ).first()

        if not prompt:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Prompt not found"
            )

        prompt.is_active = False
        prompt.updated_at = datetime.utcnow()

        db.commit()
        db.refresh(prompt)

        logger.info(f"Prompt deactivated: {prompt_id} by API key: {api_key.name}")

        return PromptResponse.from_orm(prompt)

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error deactivating prompt {prompt_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to deactivate prompt"
        )


@router.get("/categories/list", response_model=List[str])
async def list_categories(
        db: Session = Depends(get_db),
        api_key: Optional[APIKey] = Depends(get_optional_api_key)
):
    """
    获取所有提示词分类
    
    Args:
        db: 数据库会话
        api_key: API密钥（可选）
        
    Returns:
        List[str]: 分类列表
    """
    try:
        categories = db.query(Prompt.category).filter(
            Prompt.category.isnot(None),
            Prompt.is_active == True
        ).distinct().all()

        category_list = [cat[0] for cat in categories if cat[0]]

        logger.info(f"Listed {len(category_list)} categories")

        return category_list

    except Exception as e:
        logger.error(f"Error listing categories: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve categories"
        )


@router.get("/tags/list", response_model=List[str])
async def list_tags(
        db: Session = Depends(get_db),
        api_key: Optional[APIKey] = Depends(get_optional_api_key)
):
    """
    获取所有提示词标签
    
    Args:
        db: 数据库会话
        api_key: API密钥（可选）
        
    Returns:
        List[str]: 标签列表
    """
    try:
        prompts = db.query(Prompt.tags).filter(
            Prompt.is_active == True
        ).all()

        # 收集所有标签
        all_tags = set()
        for prompt_tags in prompts:
            if prompt_tags[0]:  # tags字段不为空
                all_tags.update(prompt_tags[0])

        tag_list = sorted(list(all_tags))

        logger.info(f"Listed {len(tag_list)} tags")

        return tag_list

    except Exception as e:
        logger.error(f"Error listing tags: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve tags"
        )


# 辅助函数

def _extract_variables_from_content(content: str) -> List[str]:
    """
    从提示词内容中提取变量
    
    Args:
        content: 提示词内容
        
    Returns:
        List[str]: 变量列表
    """
    import re

    # 查找 {variable} 格式的变量
    pattern = r'\{([^}]+)\}'
    matches = re.findall(pattern, content)

    # 去重并排序
    variables = sorted(list(set(matches)))

    return variables
