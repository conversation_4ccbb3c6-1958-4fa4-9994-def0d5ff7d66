# -*- coding: utf-8 -*-
"""
统计监控API路由
"""

import time
from datetime import datetime, timedelta
from typing import Optional

import psutil
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy import func, desc, and_
from sqlalchemy.orm import Session

from api.dependencies.auth import require_stats_permission, get_optional_api_key
from api.dependencies.database import get_db
from api.middleware.performance import PerformanceMonitor
from api.models.schemas import (
    SystemStatsResponse, UsageStatsResponse, ModelStatsResponse,
    PerformanceStatsResponse, HealthCheckResponse, BaseResponse,
    TrendAnalysisResponse, ComparisonAnalysisResponse, PredictiveAnalysisResponse,
    UserBehaviorAnalysisResponse, CostAnalysisResponse, SessionCostResponse,
    PricingUpdateRequest, PricingInfoResponse
)
from database.models import Session as DBSession, Conversation, Prompt, APIKey
from services.cost_service import get_cost_calculator
from services.user_behavior_service import get_user_behavior_analyzer
from utils.database_optimizer import get_database_optimizer
from utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/stats", tags=["statistics"])

# 全局性能监控实例
performance_monitor = PerformanceMonitor()


@router.get("/system", response_model=SystemStatsResponse)
async def get_system_stats(
        db: Session = Depends(get_db),
        api_key: APIKey = Depends(require_stats_permission)
):
    """
    获取系统统计信息
    
    Args:
        db: 数据库会话
        api_key: API密钥
        
    Returns:
        SystemStatsResponse: 系统统计信息
    """
    try:
        # 获取系统资源使用情况
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')

        # 获取数据库统计
        total_sessions = db.query(DBSession).count()
        active_sessions = db.query(DBSession).filter(
            DBSession.status == 'active'
        ).count()
        total_conversations = db.query(Conversation).count()
        total_prompts = db.query(Prompt).count()
        active_prompts = db.query(Prompt).filter(
            Prompt.is_active == True
        ).count()

        # 获取API密钥统计
        total_api_keys = db.query(APIKey).count()
        active_api_keys = db.query(APIKey).filter(
            APIKey.is_active == True
        ).count()

        # 获取今日统计
        today = datetime.utcnow().date()
        today_conversations = db.query(Conversation).filter(
            func.date(Conversation.created_at) == today
        ).count()

        today_sessions = db.query(DBSession).filter(
            func.date(DBSession.created_at) == today
        ).count()

        logger.info(f"System stats retrieved by API key: {api_key.name}")

        return SystemStatsResponse(
            success=True,
            message="System statistics retrieved successfully",
            cpu_usage=cpu_percent,
            memory_usage={
                "total": memory.total,
                "available": memory.available,
                "percent": memory.percent,
                "used": memory.used,
                "free": memory.free
            },
            disk_usage={
                "total": disk.total,
                "used": disk.used,
                "free": disk.free,
                "percent": (disk.used / disk.total) * 100
            },
            database_stats={
                "total_sessions": total_sessions,
                "active_sessions": active_sessions,
                "total_conversations": total_conversations,
                "total_prompts": total_prompts,
                "active_prompts": active_prompts,
                "total_api_keys": total_api_keys,
                "active_api_keys": active_api_keys
            },
            daily_stats={
                "today_conversations": today_conversations,
                "today_sessions": today_sessions
            },
            timestamp=datetime.utcnow()
        )

    except Exception as e:
        logger.error(f"Error retrieving system stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve system statistics"
        )


@router.get("/usage", response_model=UsageStatsResponse)
async def get_usage_stats(
        days: int = Query(7, ge=1, le=90, description="统计天数"),
        db: Session = Depends(get_db),
        api_key: APIKey = Depends(require_stats_permission)
):
    """
    获取使用统计信息
    
    Args:
        days: 统计天数
        db: 数据库会话
        api_key: API密钥
        
    Returns:
        UsageStatsResponse: 使用统计信息
    """
    try:
        # 计算时间范围
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)

        # 按日期统计对话数量
        daily_conversations = db.query(
            func.date(Conversation.created_at).label('date'),
            func.count(Conversation.id).label('count')
        ).filter(
            Conversation.created_at >= start_date
        ).group_by(
            func.date(Conversation.created_at)
        ).order_by('date').all()

        # 按日期统计会话数量
        daily_sessions = db.query(
            func.date(DBSession.created_at).label('date'),
            func.count(DBSession.id).label('count')
        ).filter(
            DBSession.created_at >= start_date
        ).group_by(
            func.date(DBSession.created_at)
        ).order_by('date').all()

        # 按模型统计使用情况
        model_usage = db.query(
            Conversation.model_name,
            func.count(Conversation.id).label('count'),
            func.avg(Conversation.response_time).label('avg_response_time'),
            func.sum(Conversation.input_tokens).label('total_input_tokens'),
            func.sum(Conversation.output_tokens).label('total_output_tokens')
        ).filter(
            Conversation.created_at >= start_date
        ).group_by(
            Conversation.model_name
        ).order_by(desc('count')).all()

        # 按状态统计对话
        status_distribution = db.query(
            Conversation.status,
            func.count(Conversation.id).label('count')
        ).filter(
            Conversation.created_at >= start_date
        ).group_by(
            Conversation.status
        ).all()

        # 按API密钥统计使用情况（如果有关联）
        api_key_usage = db.query(
            APIKey.name,
            func.count(DBSession.id).label('session_count')
        ).join(
            DBSession, APIKey.id == DBSession.api_key_id, isouter=True
        ).filter(
            DBSession.created_at >= start_date
        ).group_by(
            APIKey.name
        ).order_by(desc('session_count')).all()

        # 计算总计
        total_conversations = sum(item.count for item in daily_conversations)
        total_sessions = sum(item.count for item in daily_sessions)

        logger.info(f"Usage stats retrieved for {days} days by API key: {api_key.name}")

        return UsageStatsResponse(
            success=True,
            message="Usage statistics retrieved successfully",
            period_days=days,
            start_date=start_date.date(),
            end_date=end_date.date(),
            total_conversations=total_conversations,
            total_sessions=total_sessions,
            daily_conversations=[
                {"date": item.date, "count": item.count}
                for item in daily_conversations
            ],
            daily_sessions=[
                {"date": item.date, "count": item.count}
                for item in daily_sessions
            ],
            model_usage=[
                {
                    "model_name": item.model_name,
                    "conversation_count": item.count,
                    "avg_response_time": float(item.avg_response_time or 0),
                    "total_input_tokens": int(item.total_input_tokens or 0),
                    "total_output_tokens": int(item.total_output_tokens or 0)
                }
                for item in model_usage
            ],
            status_distribution=[
                {"status": item.status, "count": item.count}
                for item in status_distribution
            ],
            api_key_usage=[
                {"api_key_name": item.name, "session_count": item.session_count}
                for item in api_key_usage if item.name
            ]
        )

    except Exception as e:
        logger.error(f"Error retrieving usage stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve usage statistics"
        )


@router.get("/models", response_model=ModelStatsResponse)
async def get_model_stats(
        model_name: Optional[str] = Query(None, description="指定模型名称"),
        days: int = Query(7, ge=1, le=90, description="统计天数"),
        db: Session = Depends(get_db),
        api_key: APIKey = Depends(require_stats_permission)
):
    """
    获取模型统计信息
    
    Args:
        model_name: 指定模型名称
        days: 统计天数
        db: 数据库会话
        api_key: API密钥
        
    Returns:
        ModelStatsResponse: 模型统计信息
    """
    try:
        # 计算时间范围
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)

        # 构建查询
        query = db.query(Conversation).filter(
            Conversation.created_at >= start_date
        )

        if model_name:
            query = query.filter(Conversation.model_name == model_name)

        # 获取基础统计
        conversations = query.all()

        if not conversations:
            return ModelStatsResponse(
                success=True,
                message="No data found for the specified criteria",
                model_name=model_name,
                period_days=days,
                total_conversations=0,
                success_rate=0.0,
                avg_response_time=0.0,
                total_tokens=0,
                avg_tokens_per_conversation=0.0,
                error_distribution=[],
                daily_usage=[],
                performance_trends=[]
            )

        # 计算统计数据
        total_conversations = len(conversations)
        successful_conversations = len([c for c in conversations if c.status == 'completed'])
        success_rate = (successful_conversations / total_conversations) * 100

        response_times = [c.response_time for c in conversations if c.response_time]
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0

        total_input_tokens = sum(c.input_tokens for c in conversations if c.input_tokens)
        total_output_tokens = sum(c.output_tokens for c in conversations if c.output_tokens)
        total_tokens = total_input_tokens + total_output_tokens
        avg_tokens_per_conversation = total_tokens / total_conversations if total_conversations > 0 else 0

        # 错误分布
        error_conversations = [c for c in conversations if c.status == 'error']
        error_types = {}
        for conv in error_conversations:
            error_type = conv.error_message.split(':')[0] if conv.error_message else 'Unknown'
            error_types[error_type] = error_types.get(error_type, 0) + 1

        error_distribution = [
            {"error_type": error_type, "count": count}
            for error_type, count in error_types.items()
        ]

        # 按日期统计使用情况
        daily_usage_dict = {}
        for conv in conversations:
            date = conv.created_at.date()
            if date not in daily_usage_dict:
                daily_usage_dict[date] = {
                    "date": date,
                    "conversation_count": 0,
                    "avg_response_time": 0,
                    "total_tokens": 0,
                    "response_times": []
                }

            daily_usage_dict[date]["conversation_count"] += 1
            daily_usage_dict[date]["total_tokens"] += (conv.input_tokens or 0) + (conv.output_tokens or 0)
            if conv.response_time:
                daily_usage_dict[date]["response_times"].append(conv.response_time)

        # 计算每日平均响应时间
        daily_usage = []
        for date, data in sorted(daily_usage_dict.items()):
            avg_rt = sum(data["response_times"]) / len(data["response_times"]) if data["response_times"] else 0
            daily_usage.append({
                "date": date,
                "conversation_count": data["conversation_count"],
                "avg_response_time": avg_rt,
                "total_tokens": data["total_tokens"]
            })

        # 性能趋势（按小时统计最近24小时）
        recent_24h = end_date - timedelta(hours=24)
        recent_conversations = [c for c in conversations if c.created_at >= recent_24h]

        hourly_performance = {}
        for conv in recent_conversations:
            hour = conv.created_at.replace(minute=0, second=0, microsecond=0)
            if hour not in hourly_performance:
                hourly_performance[hour] = {
                    "timestamp": hour,
                    "conversation_count": 0,
                    "avg_response_time": 0,
                    "success_rate": 0,
                    "response_times": [],
                    "successful_count": 0
                }

            hourly_performance[hour]["conversation_count"] += 1
            if conv.response_time:
                hourly_performance[hour]["response_times"].append(conv.response_time)
            if conv.status == 'completed':
                hourly_performance[hour]["successful_count"] += 1

        performance_trends = []
        for hour, data in sorted(hourly_performance.items()):
            avg_rt = sum(data["response_times"]) / len(data["response_times"]) if data["response_times"] else 0
            success_rate_hourly = (data["successful_count"] / data["conversation_count"]) * 100 if data[
                                                                                                       "conversation_count"] > 0 else 0

            performance_trends.append({
                "timestamp": hour,
                "conversation_count": data["conversation_count"],
                "avg_response_time": avg_rt,
                "success_rate": success_rate_hourly
            })

        logger.info(f"Model stats retrieved for {model_name or 'all models'} by API key: {api_key.name}")

        return ModelStatsResponse(
            success=True,
            message="Model statistics retrieved successfully",
            model_name=model_name,
            period_days=days,
            total_conversations=total_conversations,
            success_rate=success_rate,
            avg_response_time=avg_response_time,
            total_tokens=total_tokens,
            avg_tokens_per_conversation=avg_tokens_per_conversation,
            error_distribution=error_distribution,
            daily_usage=daily_usage,
            performance_trends=performance_trends
        )

    except Exception as e:
        logger.error(f"Error retrieving model stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve model statistics"
        )


@router.get("/performance", response_model=PerformanceStatsResponse)
async def get_performance_stats(
        api_key: APIKey = Depends(require_stats_permission)
):
    """
    获取性能统计信息
    
    Args:
        api_key: API密钥
        
    Returns:
        PerformanceStatsResponse: 性能统计信息
    """
    try:
        # 获取性能监控数据
        stats = performance_monitor.get_stats()

        logger.info(f"Performance stats retrieved by API key: {api_key.name}")

        return PerformanceStatsResponse(
            success=True,
            message="Performance statistics retrieved successfully",
            **stats
        )

    except Exception as e:
        logger.error(f"Error retrieving performance stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve performance statistics"
        )


@router.get("/health", response_model=HealthCheckResponse)
async def health_check(
        db: Session = Depends(get_db),
        api_key: Optional[APIKey] = Depends(get_optional_api_key)
):
    """
    健康检查
    
    Args:
        db: 数据库会话
        api_key: API密钥（可选）
        
    Returns:
        HealthCheckResponse: 健康检查结果
    """
    try:
        # 检查数据库连接
        db_healthy = True
        db_error = None
        try:
            db.execute("SELECT 1")
        except Exception as e:
            db_healthy = False
            db_error = str(e)

        # 检查系统资源
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')

        # 判断系统健康状态
        system_healthy = (
                cpu_percent < 90 and
                memory.percent < 90 and
                (disk.used / disk.total) < 0.9
        )

        # 检查API服务状态
        api_healthy = True  # 如果能到达这里，API服务就是健康的

        # 总体健康状态
        overall_healthy = db_healthy and system_healthy and api_healthy

        # 获取运行时间
        uptime = time.time() - performance_monitor.start_time

        health_status = {
            "overall_healthy": overall_healthy,
            "database": {
                "healthy": db_healthy,
                "error": db_error
            },
            "system": {
                "healthy": system_healthy,
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "disk_percent": (disk.used / disk.total) * 100
            },
            "api": {
                "healthy": api_healthy,
                "uptime_seconds": uptime
            },
            "timestamp": datetime.utcnow()
        }

        status_code = status.HTTP_200_OK if overall_healthy else status.HTTP_503_SERVICE_UNAVAILABLE

        logger.info(f"Health check performed - Status: {'healthy' if overall_healthy else 'unhealthy'}")

        return HealthCheckResponse(
            success=overall_healthy,
            message="Health check completed",
            **health_status
        )

    except Exception as e:
        logger.error(f"Error during health check: {e}")
        return HealthCheckResponse(
            success=False,
            message="Health check failed",
            overall_healthy=False,
            database={"healthy": False, "error": "Health check failed"},
            system={"healthy": False, "cpu_percent": 0, "memory_percent": 0, "disk_percent": 0},
            api={"healthy": False, "uptime_seconds": 0},
            timestamp=datetime.utcnow()
        )


@router.post("/reset", response_model=BaseResponse)
async def reset_stats(
        api_key: APIKey = Depends(require_stats_permission)
):
    """
    重置性能统计数据
    
    Args:
        api_key: API密钥
        
    Returns:
        BaseResponse: 重置结果
    """
    try:
        # 重置性能监控数据
        performance_monitor.reset()

        logger.info(f"Performance stats reset by API key: {api_key.name}")

        return BaseResponse(
            success=True,
            message="Performance statistics reset successfully"
        )

    except Exception as e:
        logger.error(f"Error resetting stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to reset statistics"
        )


# ============ 高级统计分析功能 ============

@router.get("/trends", response_model=TrendAnalysisResponse)
async def get_trend_analysis(
        days: int = Query(30, ge=7, le=365, description="分析天数"),
        db: Session = Depends(get_db),
        api_key: APIKey = Depends(require_stats_permission)
):
    """
    获取趋势分析
    
    Args:
        days: 分析天数
        db: 数据库会话
        api_key: API密钥
        
    Returns:
        TrendAnalysisResponse: 趋势分析结果
    """
    try:
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)

        # 对话趋势分析
        daily_conversations = db.query(
            func.date(Conversation.created_at).label('date'),
            func.count(Conversation.id).label('count'),
            func.avg(Conversation.response_time).label('avg_response_time'),
            func.sum(Conversation.input_tokens + Conversation.output_tokens).label('total_tokens')
        ).filter(
            Conversation.created_at >= start_date
        ).group_by(
            func.date(Conversation.created_at)
        ).order_by('date').all()

        conversation_trend = [
            {
                "date": item.date,
                "count": item.count,
                "avg_response_time": float(item.avg_response_time or 0),
                "total_tokens": int(item.total_tokens or 0)
            }
            for item in daily_conversations
        ]

        # 模型性能趋势
        model_performance = db.query(
            Conversation.model_name,
            func.date(Conversation.created_at).label('date'),
            func.avg(Conversation.response_time).label('avg_response_time'),
            func.count(Conversation.id).label('count'),
            func.sum(func.case([(Conversation.status == 'completed', 1)], else_=0)).label('success_count')
        ).filter(
            Conversation.created_at >= start_date
        ).group_by(
            Conversation.model_name,
            func.date(Conversation.created_at)
        ).order_by('date').all()

        model_performance_trend = [
            {
                "model_name": item.model_name,
                "date": item.date,
                "avg_response_time": float(item.avg_response_time or 0),
                "success_rate": (item.success_count / item.count * 100) if item.count > 0 else 0
            }
            for item in model_performance
        ]

        # Token使用趋势
        token_usage = db.query(
            func.date(Conversation.created_at).label('date'),
            func.sum(Conversation.input_tokens).label('input_tokens'),
            func.sum(Conversation.output_tokens).label('output_tokens')
        ).filter(
            Conversation.created_at >= start_date
        ).group_by(
            func.date(Conversation.created_at)
        ).order_by('date').all()

        token_usage_trend = [
            {
                "date": item.date,
                "input_tokens": int(item.input_tokens or 0),
                "output_tokens": int(item.output_tokens or 0),
                "total_tokens": int((item.input_tokens or 0) + (item.output_tokens or 0))
            }
            for item in token_usage
        ]

        # 错误率趋势
        error_rate = db.query(
            func.date(Conversation.created_at).label('date'),
            func.count(Conversation.id).label('total_count'),
            func.sum(func.case([(Conversation.status == 'error', 1)], else_=0)).label('error_count')
        ).filter(
            Conversation.created_at >= start_date
        ).group_by(
            func.date(Conversation.created_at)
        ).order_by('date').all()

        error_rate_trend = [
            {
                "date": item.date,
                "error_rate": (item.error_count / item.total_count * 100) if item.total_count > 0 else 0,
                "total_requests": item.total_count,
                "error_count": item.error_count
            }
            for item in error_rate
        ]

        # 高峰时段分析
        hourly_usage = db.query(
            func.extract('hour', Conversation.created_at).label('hour'),
            func.count(Conversation.id).label('count')
        ).filter(
            Conversation.created_at >= start_date
        ).group_by(
            func.extract('hour', Conversation.created_at)
        ).order_by('count').all()

        peak_hours = [
            {"hour": int(item.hour), "request_count": item.count}
            for item in hourly_usage
        ]

        # 计算增长率
        if len(conversation_trend) >= 2:
            recent_avg = sum(item["count"] for item in conversation_trend[-7:]) / 7
            previous_avg = sum(item["count"] for item in conversation_trend[-14:-7]) / 7 if len(
                conversation_trend) >= 14 else recent_avg
            conversation_growth = ((recent_avg - previous_avg) / previous_avg * 100) if previous_avg > 0 else 0
        else:
            conversation_growth = 0

        growth_rate = {
            "conversation_growth_rate": conversation_growth,
            "token_growth_rate": 0,  # 可以类似计算
            "user_growth_rate": 0  # 可以类似计算
        }

        logger.info(f"Trend analysis retrieved for {days} days by API key: {api_key.name}")

        return TrendAnalysisResponse(
            success=True,
            message="Trend analysis completed successfully",
            period_days=days,
            conversation_trend=conversation_trend,
            model_performance_trend=model_performance_trend,
            token_usage_trend=token_usage_trend,
            error_rate_trend=error_rate_trend,
            peak_hours=peak_hours,
            growth_rate=growth_rate
        )

    except Exception as e:
        logger.error(f"Error in trend analysis: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to perform trend analysis"
        )


@router.get("/comparison", response_model=ComparisonAnalysisResponse)
async def get_comparison_analysis(
        models: str = Query(..., description="对比模型列表，逗号分隔"),
        days: int = Query(30, ge=7, le=365, description="对比天数"),
        db: Session = Depends(get_db),
        api_key: APIKey = Depends(require_stats_permission)
):
    """
    获取模型对比分析
    
    Args:
        models: 对比模型列表，逗号分隔
        days: 对比天数
        db: 数据库会话
        api_key: API密钥
        
    Returns:
        ComparisonAnalysisResponse: 对比分析结果
    """
    try:
        model_list = [model.strip() for model in models.split(',')]
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)

        # 性能对比
        performance_data = db.query(
            Conversation.model_name,
            func.count(Conversation.id).label('total_requests'),
            func.avg(Conversation.response_time).label('avg_response_time'),
            func.sum(func.case([(Conversation.status == 'completed', 1)], else_=0)).label('success_count'),
            func.sum(Conversation.input_tokens).label('total_input_tokens'),
            func.sum(Conversation.output_tokens).label('total_output_tokens')
        ).filter(
            and_(
                Conversation.created_at >= start_date,
                Conversation.model_name.in_(model_list)
            )
        ).group_by(
            Conversation.model_name
        ).all()

        performance_comparison = [
            {
                "model_name": item.model_name,
                "total_requests": item.total_requests,
                "avg_response_time": float(item.avg_response_time or 0),
                "success_rate": (item.success_count / item.total_requests * 100) if item.total_requests > 0 else 0,
                "total_tokens": int((item.total_input_tokens or 0) + (item.total_output_tokens or 0)),
                "avg_tokens_per_request": int(((item.total_input_tokens or 0) + (
                        item.total_output_tokens or 0)) / item.total_requests) if item.total_requests > 0 else 0
            }
            for item in performance_data
        ]

        # 使用量对比
        daily_usage = db.query(
            Conversation.model_name,
            func.date(Conversation.created_at).label('date'),
            func.count(Conversation.id).label('daily_count')
        ).filter(
            and_(
                Conversation.created_at >= start_date,
                Conversation.model_name.in_(model_list)
            )
        ).group_by(
            Conversation.model_name,
            func.date(Conversation.created_at)
        ).order_by('date').all()

        usage_comparison = [
            {
                "model_name": item.model_name,
                "date": item.date,
                "daily_count": item.daily_count
            }
            for item in daily_usage
        ]

        # 成本对比（基于token使用量的估算）
        cost_comparison = [
            {
                "model_name": item["model_name"],
                "estimated_cost": item["total_tokens"] * 0.001,  # 假设每1000 token成本0.001
                "cost_per_request": (item["total_tokens"] * 0.001) / item["total_requests"] if item[
                                                                                                   "total_requests"] > 0 else 0
            }
            for item in performance_comparison
        ]

        # 质量指标
        quality_metrics = [
            {
                "model_name": item["model_name"],
                "reliability_score": item["success_rate"],
                "efficiency_score": max(0, 100 - (item["avg_response_time"] / 10)),  # 响应时间越短分数越高
                "cost_effectiveness": (item["total_requests"] / (item["total_tokens"] * 0.001)) if item[
                                                                                                       "total_tokens"] > 0 else 0
            }
            for item in performance_comparison
        ]

        # 优化建议
        recommendations = []
        if performance_comparison:
            best_performance = max(performance_comparison, key=lambda x: x["success_rate"])
            fastest_model = min(performance_comparison, key=lambda x: x["avg_response_time"])
            most_efficient = min(cost_comparison, key=lambda x: x["cost_per_request"])

            recommendations.extend([
                f"最高成功率模型: {best_performance['model_name']} ({best_performance['success_rate']:.1f}%)",
                f"最快响应模型: {fastest_model['model_name']} ({fastest_model['avg_response_time']:.2f}s)",
                f"最具成本效益模型: {most_efficient['model_name']}"
            ])

        logger.info(f"Comparison analysis retrieved for models {model_list} by API key: {api_key.name}")

        return ComparisonAnalysisResponse(
            success=True,
            message="Comparison analysis completed successfully",
            models=model_list,
            period_days=days,
            performance_comparison=performance_comparison,
            usage_comparison=usage_comparison,
            cost_comparison=cost_comparison,
            quality_metrics=quality_metrics,
            recommendations=recommendations
        )

    except Exception as e:
        logger.error(f"Error in comparison analysis: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to perform comparison analysis"
        )


@router.get("/prediction", response_model=PredictiveAnalysisResponse)
async def get_predictive_analysis(
        prediction_days: int = Query(30, ge=7, le=90, description="预测天数"),
        historical_days: int = Query(60, ge=30, le=365, description="历史数据天数"),
        db: Session = Depends(get_db),
        api_key: APIKey = Depends(require_stats_permission)
):
    """
    获取预测分析
    
    Args:
        prediction_days: 预测天数
        historical_days: 历史数据天数
        db: 数据库会话
        api_key: API密钥
        
    Returns:
        PredictiveAnalysisResponse: 预测分析结果
    """
    try:
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=historical_days)

        # 获取历史数据
        historical_data = db.query(
            func.date(Conversation.created_at).label('date'),
            func.count(Conversation.id).label('conversation_count'),
            func.sum(Conversation.input_tokens + Conversation.output_tokens).label('total_tokens')
        ).filter(
            Conversation.created_at >= start_date
        ).group_by(
            func.date(Conversation.created_at)
        ).order_by('date').all()

        if len(historical_data) < 7:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Insufficient historical data for prediction"
            )

        # 简单的线性趋势预测
        conversation_counts = [item.conversation_count for item in historical_data]
        token_counts = [int(item.total_tokens or 0) for item in historical_data]

        # 计算平均增长率
        recent_period = conversation_counts[-7:]  # 最近7天
        previous_period = conversation_counts[-14:-7] if len(conversation_counts) >= 14 else recent_period

        avg_recent = sum(recent_period) / len(recent_period)
        avg_previous = sum(previous_period) / len(previous_period)
        growth_rate = (avg_recent - avg_previous) / avg_previous if avg_previous > 0 else 0

        # 使用量预测
        usage_forecast = []
        current_avg = avg_recent
        for i in range(prediction_days):
            predicted_count = max(0, int(current_avg * (1 + growth_rate * (i + 1) / 30)))
            future_date = end_date + timedelta(days=i + 1)
            usage_forecast.append({
                "date": future_date.date(),
                "predicted_conversations": predicted_count,
                "confidence_level": max(0.5, 0.9 - (i * 0.01))  # 置信度随时间递减
            })

        # 成本预测
        avg_tokens_per_conversation = sum(token_counts) / sum(conversation_counts) if sum(
            conversation_counts) > 0 else 1000
        cost_forecast = [
            {
                "date": item["date"],
                "predicted_cost": item["predicted_conversations"] * avg_tokens_per_conversation * 0.001,
                "predicted_tokens": item["predicted_conversations"] * avg_tokens_per_conversation
            }
            for item in usage_forecast
        ]

        # 容量建议
        max_predicted_daily = max(item["predicted_conversations"] for item in usage_forecast)
        current_max = max(conversation_counts[-7:]) if conversation_counts else 0

        capacity_recommendations = [
            {
                "metric": "daily_conversations",
                "current_peak": current_max,
                "predicted_peak": max_predicted_daily,
                "recommended_capacity": int(max_predicted_daily * 1.2),  # 20%缓冲
                "scaling_factor": max_predicted_daily / current_max if current_max > 0 else 1
            }
        ]

        # 风险评估
        risk_level = "low"
        if growth_rate > 0.5:  # 增长率超过50%
            risk_level = "high"
        elif growth_rate > 0.2:  # 增长率超过20%
            risk_level = "medium"

        risk_assessment = {
            "overall_risk_level": risk_level,
            "growth_rate": growth_rate * 100,
            "capacity_risk": "high" if max_predicted_daily > current_max * 2 else "low",
            "cost_risk": "high" if sum(item["predicted_cost"] for item in cost_forecast) > 1000 else "low",
            "recommendations": [
                "监控系统容量" if risk_level != "low" else "保持当前配置",
                "考虑扩容" if max_predicted_daily > current_max * 1.5 else "当前容量充足"
            ]
        }

        # 整体置信度
        data_quality = min(1.0, len(historical_data) / 30)  # 数据质量基于历史数据量
        trend_stability = 1.0 - abs(growth_rate)  # 趋势稳定性
        confidence_level = (data_quality + trend_stability) / 2

        logger.info(f"Predictive analysis completed for {prediction_days} days by API key: {api_key.name}")

        return PredictiveAnalysisResponse(
            success=True,
            message="Predictive analysis completed successfully",
            prediction_days=prediction_days,
            usage_forecast=usage_forecast,
            cost_forecast=cost_forecast,
            capacity_recommendations=capacity_recommendations,
            risk_assessment=risk_assessment,
            confidence_level=confidence_level
        )

    except Exception as e:
        logger.error(f"Error in predictive analysis: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to perform predictive analysis"
        )


@router.get("/database/slow-queries", response_model=BaseResponse)
async def get_slow_queries(
        limit: int = Query(10, ge=1, le=100, description="返回记录数量"),
        db: Session = Depends(get_db),
        api_key: APIKey = Depends(require_stats_permission)
):
    """
    获取慢查询分析
    
    Args:
        limit: 返回记录数量
        db: 数据库会话
        api_key: API密钥
        
    Returns:
        BaseResponse: 慢查询分析结果
    """
    try:
        optimizer = get_database_optimizer()
        slow_queries = optimizer.analyze_slow_queries(db, limit=limit)

        logger.info(f"Slow queries analysis retrieved by API key: {api_key.name}")

        return BaseResponse(
            success=True,
            message="Slow queries analysis completed successfully",
            data={
                "slow_queries": slow_queries,
                "total_count": len(slow_queries),
                "analysis_timestamp": datetime.utcnow()
            }
        )

    except Exception as e:
        logger.error(f"Error analyzing slow queries: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to analyze slow queries"
        )


@router.get("/database/index-recommendations", response_model=BaseResponse)
async def get_index_recommendations(
        table_name: Optional[str] = Query(None, description="指定表名"),
        db: Session = Depends(get_db),
        api_key: APIKey = Depends(require_stats_permission)
):
    """
    获取索引建议
    
    Args:
        table_name: 指定表名
        db: 数据库会话
        api_key: API密钥
        
    Returns:
        BaseResponse: 索引建议结果
    """
    try:
        optimizer = get_database_optimizer()
        recommendations = optimizer.get_index_recommendations(db, table_name=table_name)

        logger.info(f"Index recommendations retrieved for table {table_name or 'all'} by API key: {api_key.name}")

        return BaseResponse(
            success=True,
            message="Index recommendations generated successfully",
            data={
                "recommendations": recommendations,
                "table_name": table_name,
                "total_recommendations": len(recommendations),
                "analysis_timestamp": datetime.utcnow()
            }
        )

    except Exception as e:
        logger.error(f"Error generating index recommendations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate index recommendations"
        )


@router.get("/database/optimization-report", response_model=BaseResponse)
async def get_optimization_report(
        db: Session = Depends(get_db),
        api_key: APIKey = Depends(require_stats_permission)
):
    """
    获取数据库优化报告
    
    Args:
        db: 数据库会话
        api_key: API密钥
        
    Returns:
        BaseResponse: 数据库优化报告
    """
    try:
        optimizer = get_database_optimizer()
        report = optimizer.generate_optimization_report(db)

        logger.info(f"Database optimization report generated by API key: {api_key.name}")

        return BaseResponse(
            success=True,
            message="Database optimization report generated successfully",
            data={
                "report": report,
                "generated_at": datetime.utcnow()
            }
        )

    except Exception as e:
        logger.error(f"Error generating optimization report: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate optimization report"
        )


@router.get("/user-behavior", response_model=UserBehaviorAnalysisResponse)
async def get_user_behavior_analysis(
        days: int = Query(30, ge=1, le=365, description="分析天数"),
        session_id: Optional[str] = Query(None, description="特定会话ID"),
        db: Session = Depends(get_db),
        api_key: APIKey = Depends(require_stats_permission)
):
    """
    获取用户行为分析
    
    Args:
        days: 分析天数（1-365天）
        session_id: 可选的特定会话ID
        db: 数据库会话
        api_key: API密钥
        
    Returns:
        UserBehaviorAnalysisResponse: 用户行为分析结果
    """
    try:
        analyzer = get_user_behavior_analyzer(db)
        analysis = analyzer.analyze_user_behavior(days=days, session_id=session_id)

        return UserBehaviorAnalysisResponse(
            success=True,
            message="User behavior analysis completed successfully",
            analysis=analysis
        )

    except Exception as e:
        logger.error(f"Error analyzing user behavior: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to analyze user behavior"
        )


@router.get("/cost-analysis", response_model=CostAnalysisResponse)
async def get_cost_analysis(
        days: int = Query(30, ge=1, le=365, description="分析天数"),
        session_id: Optional[str] = Query(None, description="特定会话ID"),
        model_name: Optional[str] = Query(None, description="特定模型名称"),
        db: Session = Depends(get_db),
        api_key: APIKey = Depends(require_stats_permission)
):
    """
    获取成本分析
    
    Args:
        days: 分析天数（1-365天）
        session_id: 可选的特定会话ID
        model_name: 可选的特定模型名称
        db: 数据库会话
        api_key: API密钥
        
    Returns:
        CostAnalysisResponse: 成本分析结果
    """
    try:
        calculator = get_cost_calculator(db)
        analysis = calculator.analyze_costs(
            days=days,
            session_id=session_id,
            model_name=model_name
        )

        return CostAnalysisResponse(
            success=True,
            message="Cost analysis completed successfully",
            analysis=analysis
        )

    except Exception as e:
        logger.error(f"Error analyzing costs: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to analyze costs"
        )


@router.get("/session-cost/{session_id}", response_model=SessionCostResponse)
async def get_session_cost(
        session_id: str,
        db: Session = Depends(get_db),
        api_key: APIKey = Depends(require_stats_permission)
):
    """
    获取特定会话的成本
    
    Args:
        session_id: 会话ID
        db: 数据库会话
        api_key: API密钥
        
    Returns:
        SessionCostResponse: 会话成本信息
    """
    try:
        calculator = get_cost_calculator(db)
        session_cost = calculator.get_session_cost(session_id)

        return SessionCostResponse(
            success=True,
            message="Session cost retrieved successfully",
            session_cost=session_cost
        )

    except Exception as e:
        logger.error(f"Error getting session cost: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get session cost"
        )


@router.get("/pricing", response_model=PricingInfoResponse)
async def get_pricing_info(
        api_key: APIKey = Depends(require_stats_permission)
):
    """
    获取模型定价信息
    
    Args:
        api_key: API密钥
        
    Returns:
        PricingInfoResponse: 定价信息
    """
    try:
        # 创建一个临时的计算器实例来获取定价信息
        from services.cost_service import CostCalculator
        pricing = CostCalculator.MODEL_PRICING

        return PricingInfoResponse(
            success=True,
            message="Pricing information retrieved successfully",
            pricing=pricing
        )

    except Exception as e:
        logger.error(f"Error getting pricing info: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get pricing information"
        )


@router.put("/pricing", response_model=BaseResponse)
async def update_pricing(
        request: PricingUpdateRequest,
        db: Session = Depends(get_db),
        api_key: APIKey = Depends(require_stats_permission)
):
    """
    更新模型定价
    
    Args:
        request: 定价更新请求
        db: 数据库会话
        api_key: API密钥
        
    Returns:
        BaseResponse: 更新结果
    """
    try:
        calculator = get_cost_calculator(db)
        calculator.update_pricing(
            model_name=request.model_name,
            input_price=request.input_price,
            output_price=request.output_price
        )

        return BaseResponse(
            success=True,
            message=f"Pricing updated successfully for {request.model_name}"
        )

    except Exception as e:
        logger.error(f"Error updating pricing: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update pricing"
        )
