# -*- coding: utf-8 -*-
"""
对话API路由
"""

import asyncio
from datetime import datetime
from typing import List, Optional, Dict, Any

from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks
from sqlalchemy import desc
from sqlalchemy.orm import Session

from api.dependencies.auth import require_write_permission, get_optional_api_key
from api.dependencies.container import (
    get_comparison_engine, get_conversation_service, get_performance_monitor
)
from api.dependencies.database import get_db
from api.models.schemas import (
    ChatResponse, ModelResponse, ConversationResponse,
    ConversationListResponse, PaginationParams, BaseResponse,
    ConversationStatus
)
from database.models import APIKey
from database.models import Session as SessionModel, Conversation, Prompt
from services.conversation_service import ConversationService
from src.core.comparison_engine import ComparisonEngine
from utils.logger import get_logger
from utils.performance_monitor import PerformanceMonitor

logger = get_logger(__name__)
router = APIRouter(prefix="/conversations", tags=["conversations"])


@router.post("/{session_id}/chat", response_model=ChatResponse)
async def chat(
        session_id: int,
        request: ConversationRequest,
        background_tasks: BackgroundTasks,
        comparison_engine: ComparisonEngine = Depends(get_comparison_engine),
        conversation_service: ConversationService = Depends(get_conversation_service),
        performance_monitor: PerformanceMonitor = Depends(get_performance_monitor),
        db: Session = Depends(get_db),
        api_key: APIKey = Depends(require_write_permission)
):
    """
    发送聊天消息并获取多个模型的响应
    
    Args:
        session_id: 会话ID
        chat_request: 聊天请求
        background_tasks: 后台任务
        db: 数据库会话
        api_key: API密钥
        
    Returns:
        ChatResponse: 聊天响应
        
    Raises:
        HTTPException: 会话不存在或处理失败时抛出
    """
    start_time = datetime.utcnow()

    try:
        # 检查会话是否存在且活跃
        session = db.query(SessionModel).filter(
            SessionModel.id == session_id,
            SessionModel.status == "active"
        ).first()

        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Active session not found"
            )

        # 确定使用的模型列表
        models_to_use = request.models or session.models
        if not models_to_use:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No models specified"
            )

        # 获取系统提示词
        system_prompt = request.system_prompt or session.system_prompt
        if session.system_prompt_id and not system_prompt:
            prompt_obj = db.query(Prompt).filter(
                Prompt.id == session.system_prompt_id,
                Prompt.is_active == True
            ).first()
            if prompt_obj:
                system_prompt = prompt_obj.render(request.variables or {})

        # 记录性能开始
        performance_monitor.start_request(f"chat_{session_id}")

        # 执行模型比较
        responses = await _execute_model_comparison(
            session_id=session_id,
            user_message=request.message,
            models=models_to_use,
            system_prompt=system_prompt,
            model_config=request.model_params or {},
            db=db
        )

        # 计算总响应时间
        total_response_time = (datetime.utcnow() - start_time).total_seconds()

        # 统计成功和失败的模型数量
        successful_models = sum(1 for r in responses if r.status == ConversationStatus.SUCCESS)
        failed_models = len(responses) - successful_models

        # 更新会话统计
        background_tasks.add_task(
            _update_session_stats,
            session_id=session_id,
            message_count=len(responses),
            token_count=sum(r.token_count or 0 for r in responses),
            db_session=db
        )

        # 记录性能结束
        performance_monitor.end_request(f"chat_{session_id}", total_response_time)

        logger.info(
            f"Chat completed for session {session_id}: "
            f"{successful_models} successful, {failed_models} failed"
        )

        return ChatResponse(
            success=True,
            message="Chat completed successfully",
            session_id=session_id,
            user_message=request.message,
            responses=responses,
            timestamp=start_time,
            total_response_time=total_response_time,
            successful_models=successful_models,
            failed_models=failed_models
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in chat for session {session_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process chat request"
        )


async def _execute_model_comparison(
        session_id: str,
        user_message: str,
        models: List[str],
        system_prompt: Optional[str],
        model_config: Dict[str, Any],
        db: Session
) -> List[ModelResponse]:
    """
    执行模型比较
    
    Args:
        session_id: 会话ID
        user_message: 用户消息
        models: 模型列表
        system_prompt: 系统提示词
        model_config: 模型配置
        db: 数据库会话
        
    Returns:
        List[ModelResponse]: 模型响应列表
    """
    responses = []

    # 为每个模型创建异步任务
    tasks = []
    for model_name in models:
        task = _get_model_response(
            session_id=session_id,
            model_name=model_name,
            user_message=user_message,
            system_prompt=system_prompt,
            model_config=model_config,
            db=db
        )
        tasks.append(task)

    # 并发执行所有模型请求
    try:
        responses = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理异常结果
        processed_responses = []
        for i, response in enumerate(responses):
            if isinstance(response, Exception):
                logger.error(f"Model {models[i]} failed: {response}")
                processed_responses.append(ModelResponse(
                    model_name=models[i],
                    response="",
                    status=ConversationStatus.ERROR,
                    error_message=str(response)
                ))
            else:
                processed_responses.append(response)

        return processed_responses

    except Exception as e:
        logger.error(f"Error in model comparison: {e}")
        # 返回错误响应
        return [
            ModelResponse(
                model_name=model_name,
                response="",
                status=ConversationStatus.ERROR,
                error_message="Model comparison failed"
            )
            for model_name in models
        ]


async def _get_model_response(
        session_id: str,
        model_name: str,
        user_message: str,
        system_prompt: Optional[str],
        model_config: Dict[str, Any],
        db: Session
) -> ModelResponse:
    """
    获取单个模型的响应
    
    Args:
        session_id: 会话ID
        model_name: 模型名称
        user_message: 用户消息
        system_prompt: 系统提示词
        model_config: 模型配置
        db: 数据库会话
        
    Returns:
        ModelResponse: 模型响应
    """
    start_time = datetime.utcnow()

    try:
        # 获取模型实例
        model = model_manager.get_model(model_name)
        if not model:
            raise ValueError(f"Model {model_name} not available")

        # 获取对话历史
        conversation_history = conversation_manager.get_conversation_history(
            model_name, session_id
        )

        # 构建消息列表
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})

        # 添加历史对话
        for conv in conversation_history:
            messages.append({"role": "user", "content": conv["user_message"]})
            messages.append({"role": "assistant", "content": conv["model_response"]})

        # 添加当前用户消息
        messages.append({"role": "user", "content": user_message})

        # 调用模型
        response = await model.chat(messages, **model_config)

        # 计算响应时间
        response_time = (datetime.utcnow() - start_time).total_seconds()

        # 估算token数量（简单实现）
        token_count = len(user_message.split()) + len(response.split())

        # 保存对话记录到数据库
        conversation = Conversation(
            session_id=session_id,
            user_message=user_message,
            model_name=model_name,
            model_response=response,
            timestamp=start_time,
            response_time=response_time,
            token_count=token_count,
            status=ConversationStatus.SUCCESS,
            system_prompt_used=system_prompt,
            model_config=model_config
        )

        db.add(conversation)
        db.commit()

        # 更新对话管理器的历史记录
        conversation_manager.add_conversation(
            model_name=model_name,
            session_id=session_id,
            user_message=user_message,
            model_response=response
        )

        return ModelResponse(
            model_name=model_name,
            response=response,
            status=ConversationStatus.SUCCESS,
            response_time=response_time,
            token_count=token_count,
            model_config=model_config
        )

    except Exception as e:
        response_time = (datetime.utcnow() - start_time).total_seconds()
        error_message = str(e)

        # 保存错误记录到数据库
        conversation = Conversation(
            session_id=session_id,
            user_message=user_message,
            model_name=model_name,
            model_response="",
            timestamp=start_time,
            response_time=response_time,
            status=ConversationStatus.ERROR,
            error_message=error_message,
            system_prompt_used=system_prompt,
            model_config=model_config
        )

        db.add(conversation)
        db.commit()

        logger.error(f"Model {model_name} error: {error_message}")

        return ModelResponse(
            model_name=model_name,
            response="",
            status=ConversationStatus.ERROR,
            response_time=response_time,
            error_message=error_message
        )


def _update_session_stats(
        session_id: str,
        message_count: int,
        token_count: int,
        db_session: Session
):
    """
    更新会话统计信息（后台任务）
    
    Args:
        session_id: 会话ID
        message_count: 消息数量
        token_count: token数量
        db_session: 数据库会话
    """
    try:
        session = db_session.query(SessionModel).filter(
            SessionModel.id == session_id
        ).first()

        if session:
            session.total_messages += message_count
            session.total_tokens += token_count
            session.last_activity = datetime.utcnow()
            session.updated_at = datetime.utcnow()

            db_session.commit()

    except Exception as e:
        logger.error(f"Error updating session stats: {e}")
        db_session.rollback()


@router.get("/", response_model=ConversationListResponse)
async def list_conversations(
        pagination: PaginationParams = Depends(),
        session_id: Optional[str] = Query(None, description="会话ID过滤"),
        model_name: Optional[str] = Query(None, description="模型名称过滤"),
        status_filter: Optional[ConversationStatus] = Query(None, description="状态过滤"),
        start_date: Optional[datetime] = Query(None, description="开始日期"),
        end_date: Optional[datetime] = Query(None, description="结束日期"),
        db: Session = Depends(get_db),
        api_key: Optional[APIKey] = Depends(get_optional_api_key)
):
    """
    获取对话记录列表
    
    Args:
        pagination: 分页参数
        session_id: 会话ID过滤
        model_name: 模型名称过滤
        status_filter: 状态过滤
        start_date: 开始日期
        end_date: 结束日期
        db: 数据库会话
        api_key: API密钥（可选）
        
    Returns:
        ConversationListResponse: 对话记录列表
    """
    try:
        # 构建查询
        query = db.query(Conversation)

        # 应用过滤条件
        if session_id:
            query = query.filter(Conversation.session_id == session_id)

        if model_name:
            query = query.filter(Conversation.model_name == model_name)

        if status_filter:
            query = query.filter(Conversation.status == status_filter)

        if start_date:
            query = query.filter(Conversation.timestamp >= start_date)

        if end_date:
            query = query.filter(Conversation.timestamp <= end_date)

        # 获取总数
        total = query.count()

        # 应用分页和排序
        conversations = query.order_by(desc(Conversation.timestamp)).offset(
            pagination.offset
        ).limit(pagination.size).all()

        # 计算总页数
        pages = (total + pagination.size - 1) // pagination.size

        logger.info(f"Listed {len(conversations)} conversations")

        return ConversationListResponse(
            success=True,
            message="Conversations retrieved successfully",
            total=total,
            page=pagination.page,
            size=pagination.size,
            pages=pages,
            data=[ConversationResponse.from_orm(conv) for conv in conversations]
        )

    except Exception as e:
        logger.error(f"Error listing conversations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve conversations"
        )


@router.get("/{conversation_id}", response_model=ConversationResponse)
async def get_conversation(
        conversation_id: int,
        db: Session = Depends(get_db),
        api_key: Optional[APIKey] = Depends(get_optional_api_key)
):
    """
    获取指定对话记录
    
    Args:
        conversation_id: 对话ID
        db: 数据库会话
        api_key: API密钥（可选）
        
    Returns:
        ConversationResponse: 对话记录
        
    Raises:
        HTTPException: 对话不存在时抛出404错误
    """
    try:
        conversation = db.query(Conversation).filter(
            Conversation.id == conversation_id
        ).first()

        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Conversation not found"
            )

        logger.info(f"Retrieved conversation: {conversation_id}")

        return ConversationResponse.from_orm(conversation)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving conversation {conversation_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve conversation"
        )


@router.delete("/{conversation_id}", response_model=BaseResponse)
async def delete_conversation(
        conversation_id: int,
        db: Session = Depends(get_db),
        api_key: APIKey = Depends(require_write_permission)
):
    """
    删除指定对话记录
    
    Args:
        conversation_id: 对话ID
        db: 数据库会话
        api_key: API密钥
        
    Returns:
        BaseResponse: 删除结果
        
    Raises:
        HTTPException: 对话不存在时抛出404错误
    """
    try:
        conversation = db.query(Conversation).filter(
            Conversation.id == conversation_id
        ).first()

        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Conversation not found"
            )

        # 删除对话记录
        db.delete(conversation)
        db.commit()

        logger.info(f"Conversation deleted: {conversation_id} by API key: {api_key.name}")

        return BaseResponse(
            success=True,
            message="Conversation deleted successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error deleting conversation {conversation_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete conversation"
        )
