# -*- coding: utf-8 -*-
"""
会话管理API路由
"""

import uuid
from datetime import datetime
from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy import desc
from sqlalchemy.orm import Session

from api.dependencies.auth import require_session_permission, get_optional_api_key
from api.dependencies.container import get_session_service
from api.dependencies.database import get_db
from api.models.schemas import (
    SessionCreate, SessionUpdate, SessionResponse, SessionListResponse,
    PaginationParams, BaseResponse, SessionStatus
)
from database.models import APIKey
from database.models import Session as SessionModel, Conversation
from services.session_service import SessionService
from utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/sessions", tags=["sessions"])


@router.post("/", response_model=SessionResponse, status_code=status.HTTP_201_CREATED)
async def create_session(
        session_data: SessionCreate,
        db: Session = Depends(get_db),
        api_key: APIKey = Depends(require_session_permission),
        session_service: SessionService = Depends(get_session_service)
):
    """
    创建新会话
    
    Args:
        session_data: 会话创建数据
        db: 数据库会话
        api_key: API密钥
        
    Returns:
        SessionResponse: 创建的会话信息
        
    Raises:
        HTTPException: 创建失败时抛出
    """
    try:
        # 生成会话ID
        session_id = str(uuid.uuid4())

        # 创建会话对象
        session = SessionModel(
            id=session_id,
            models=session_data.models,
            system_prompt=session_data.system_prompt,
            system_prompt_id=session_data.system_prompt_id,
            metadata=session_data.metadata or {},
            status=SessionStatus.ACTIVE
        )

        # 保存到数据库
        db.add(session)
        db.commit()
        db.refresh(session)

        logger.info(f"Session created: {session_id} by API key: {api_key.name}")

        return SessionResponse.from_orm(session)

    except Exception as e:
        db.rollback()
        logger.error(f"Error creating session: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create session"
        )


@router.get("/", response_model=SessionListResponse)
async def list_sessions(
        pagination: PaginationParams = Depends(),
        status_filter: Optional[SessionStatus] = Query(None, description="按状态过滤"),
        model_filter: Optional[str] = Query(None, description="按模型过滤"),
        start_date: Optional[datetime] = Query(None, description="开始日期"),
        end_date: Optional[datetime] = Query(None, description="结束日期"),
        db: Session = Depends(get_db),
        api_key: Optional[APIKey] = Depends(get_optional_api_key)
):
    """
    获取会话列表
    
    Args:
        pagination: 分页参数
        status_filter: 状态过滤
        model_filter: 模型过滤
        start_date: 开始日期
        end_date: 结束日期
        db: 数据库会话
        api_key: API密钥（可选）
        
    Returns:
        SessionListResponse: 会话列表
    """
    try:
        # 构建查询
        query = db.query(SessionModel)

        # 应用过滤条件
        if status_filter:
            query = query.filter(SessionModel.status == status_filter)

        if model_filter:
            query = query.filter(SessionModel.models.contains([model_filter]))

        if start_date:
            query = query.filter(SessionModel.created_at >= start_date)

        if end_date:
            query = query.filter(SessionModel.created_at <= end_date)

        # 获取总数
        total = query.count()

        # 应用分页和排序
        sessions = query.order_by(desc(SessionModel.created_at)).offset(
            pagination.offset
        ).limit(pagination.size).all()

        # 计算总页数
        pages = (total + pagination.size - 1) // pagination.size

        logger.info(f"Listed {len(sessions)} sessions (page {pagination.page})")

        return SessionListResponse(
            success=True,
            message="Sessions retrieved successfully",
            total=total,
            page=pagination.page,
            size=pagination.size,
            pages=pages,
            data=[SessionResponse.from_orm(session) for session in sessions]
        )

    except Exception as e:
        logger.error(f"Error listing sessions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve sessions"
        )


@router.get("/{session_id}", response_model=SessionResponse)
async def get_session(
        session_id: str,
        db: Session = Depends(get_db),
        api_key: Optional[APIKey] = Depends(get_optional_api_key)
):
    """
    获取指定会话信息
    
    Args:
        session_id: 会话ID
        db: 数据库会话
        api_key: API密钥（可选）
        
    Returns:
        SessionResponse: 会话信息
        
    Raises:
        HTTPException: 会话不存在时抛出404错误
    """
    try:
        session = db.query(SessionModel).filter(
            SessionModel.id == session_id
        ).first()

        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Session not found"
            )

        logger.info(f"Retrieved session: {session_id}")

        return SessionResponse.from_orm(session)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving session {session_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve session"
        )


@router.put("/{session_id}", response_model=SessionResponse)
async def update_session(
        session_id: str,
        session_data: SessionUpdate,
        db: Session = Depends(get_db),
        api_key: APIKey = Depends(require_session_permission)
):
    """
    更新会话信息
    
    Args:
        session_id: 会话ID
        session_data: 更新数据
        db: 数据库会话
        api_key: API密钥
        
    Returns:
        SessionResponse: 更新后的会话信息
        
    Raises:
        HTTPException: 会话不存在时抛出404错误
    """
    try:
        session = db.query(SessionModel).filter(
            SessionModel.id == session_id
        ).first()

        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Session not found"
            )

        # 更新字段
        update_data = session_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(session, field, value)

        # 更新时间戳
        session.updated_at = datetime.utcnow()

        db.commit()
        db.refresh(session)

        logger.info(f"Session updated: {session_id} by API key: {api_key.name}")

        return SessionResponse.from_orm(session)

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error updating session {session_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update session"
        )


@router.delete("/{session_id}", response_model=BaseResponse)
async def delete_session(
        session_id: str,
        db: Session = Depends(get_db),
        api_key: APIKey = Depends(require_session_permission)
):
    """
    删除会话
    
    Args:
        session_id: 会话ID
        db: 数据库会话
        api_key: API密钥
        
    Returns:
        BaseResponse: 删除结果
        
    Raises:
        HTTPException: 会话不存在时抛出404错误
    """
    try:
        session = db.query(SessionModel).filter(
            SessionModel.id == session_id
        ).first()

        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Session not found"
            )

        # 删除会话（级联删除相关对话）
        db.delete(session)
        db.commit()

        logger.info(f"Session deleted: {session_id} by API key: {api_key.name}")

        return BaseResponse(
            success=True,
            message="Session deleted successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error deleting session {session_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete session"
        )


@router.post("/{session_id}/activate", response_model=SessionResponse)
async def activate_session(
        session_id: str,
        db: Session = Depends(get_db),
        api_key: APIKey = Depends(require_session_permission)
):
    """
    激活会话
    
    Args:
        session_id: 会话ID
        db: 数据库会话
        api_key: API密钥
        
    Returns:
        SessionResponse: 激活后的会话信息
    """
    try:
        session = db.query(SessionModel).filter(
            SessionModel.id == session_id
        ).first()

        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Session not found"
            )

        session.status = SessionStatus.ACTIVE
        session.last_activity = datetime.utcnow()
        session.updated_at = datetime.utcnow()

        db.commit()
        db.refresh(session)

        logger.info(f"Session activated: {session_id} by API key: {api_key.name}")

        return SessionResponse.from_orm(session)

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error activating session {session_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to activate session"
        )


@router.post("/{session_id}/deactivate", response_model=SessionResponse)
async def deactivate_session(
        session_id: str,
        db: Session = Depends(get_db),
        api_key: APIKey = Depends(require_session_permission)
):
    """
    停用会话
    
    Args:
        session_id: 会话ID
        db: 数据库会话
        api_key: API密钥
        
    Returns:
        SessionResponse: 停用后的会话信息
    """
    try:
        session = db.query(SessionModel).filter(
            SessionModel.id == session_id
        ).first()

        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Session not found"
            )

        session.status = SessionStatus.INACTIVE
        session.updated_at = datetime.utcnow()

        db.commit()
        db.refresh(session)

        logger.info(f"Session deactivated: {session_id} by API key: {api_key.name}")

        return SessionResponse.from_orm(session)

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error deactivating session {session_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to deactivate session"
        )


@router.get("/{session_id}/conversations", response_model=dict)
async def get_session_conversations(
        session_id: str,
        pagination: PaginationParams = Depends(),
        model_filter: Optional[str] = Query(None, description="按模型过滤"),
        db: Session = Depends(get_db),
        api_key: Optional[APIKey] = Depends(get_optional_api_key)
):
    """
    获取会话的对话记录
    
    Args:
        session_id: 会话ID
        pagination: 分页参数
        model_filter: 模型过滤
        db: 数据库会话
        api_key: API密钥（可选）
        
    Returns:
        dict: 对话记录列表
    """
    try:
        # 检查会话是否存在
        session = db.query(SessionModel).filter(
            SessionModel.id == session_id
        ).first()

        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Session not found"
            )

        # 构建查询
        query = db.query(Conversation).filter(
            Conversation.session_id == session_id
        )

        if model_filter:
            query = query.filter(Conversation.model_name == model_filter)

        # 获取总数
        total = query.count()

        # 应用分页和排序
        conversations = query.order_by(desc(Conversation.timestamp)).offset(
            pagination.offset
        ).limit(pagination.size).all()

        # 计算总页数
        pages = (total + pagination.size - 1) // pagination.size

        logger.info(f"Retrieved {len(conversations)} conversations for session {session_id}")

        return {
            "success": True,
            "message": "Conversations retrieved successfully",
            "total": total,
            "page": pagination.page,
            "size": pagination.size,
            "pages": pages,
            "data": [conv.to_dict() for conv in conversations]
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving conversations for session {session_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve conversations"
        )


@router.delete("/{session_id}/conversations", response_model=BaseResponse)
async def clear_session_conversations(
        session_id: str,
        model_name: Optional[str] = Query(None, description="指定模型名称（为空则清除所有）"),
        db: Session = Depends(get_db),
        api_key: APIKey = Depends(require_session_permission)
):
    """
    清除会话的对话记录
    
    Args:
        session_id: 会话ID
        model_name: 模型名称（可选）
        db: 数据库会话
        api_key: API密钥
        
    Returns:
        BaseResponse: 清除结果
    """
    try:
        # 检查会话是否存在
        session = db.query(SessionModel).filter(
            SessionModel.id == session_id
        ).first()

        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Session not found"
            )

        # 构建删除查询
        query = db.query(Conversation).filter(
            Conversation.session_id == session_id
        )

        if model_name:
            query = query.filter(Conversation.model_name == model_name)

        # 执行删除
        deleted_count = query.delete()

        # 更新会话统计
        if not model_name:
            session.total_messages = 0
            session.total_tokens = 0
        else:
            # 重新计算统计信息
            remaining_conversations = db.query(Conversation).filter(
                Conversation.session_id == session_id
            ).all()
            session.total_messages = len(remaining_conversations)
            session.total_tokens = sum(
                conv.token_count or 0 for conv in remaining_conversations
            )

        session.updated_at = datetime.utcnow()

        db.commit()

        logger.info(
            f"Cleared {deleted_count} conversations from session {session_id} "
            f"(model: {model_name or 'all'}) by API key: {api_key.name}"
        )

        return BaseResponse(
            success=True,
            message=f"Cleared {deleted_count} conversations successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error clearing conversations for session {session_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to clear conversations"
        )
