"""WebSocket路由

提供WebSocket连接端点和相关的实时通信API。
"""

from datetime import datetime
from typing import Dict, Any, Optional

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException, Query
from fastapi.responses import JSONResponse

from api.dependencies import get_db_session, verify_api_key
from api.models.schemas import MessageRequest
from api.websocket_manager import websocket_manager, Message
from services.conversation_service import ConversationService
from services.session_service import SessionService
from utils.logger import get_logger

router = APIRouter(prefix="/ws", tags=["WebSocket"])
logger = get_logger("WebSocketRouter")

# 服务实例
conversation_service = ConversationService()
session_service = SessionService()


@router.websocket("/connect")
async def websocket_endpoint(
        websocket: WebSocket,
        session_id: Optional[str] = Query(None),
        user_id: Optional[str] = Query(None),
        api_key: Optional[str] = Query(None)
):
    """WebSocket连接端点
    
    Args:
        websocket: WebSocket连接
        session_id: 会话ID（可选）
        user_id: 用户ID（可选）
        api_key: API密钥（可选）
    """
    connection_id = None

    try:
        # 验证API密钥（如果提供）
        if api_key:
            # 这里可以添加API密钥验证逻辑
            pass

        # 验证会话存在（如果提供）
        if session_id:
            try:
                await session_service.get_session(session_id)
            except Exception:
                await websocket.close(code=4004, reason="Session not found")
                return

        # 建立连接
        connection_id = await websocket_manager.connect(
            websocket=websocket,
            session_id=session_id,
            user_id=user_id,
            metadata={
                "api_key": api_key,
                "user_agent": websocket.headers.get("user-agent"),
                "origin": websocket.headers.get("origin")
            }
        )

        # 注册消息处理器
        websocket_manager.register_message_handler(
            "send_message",
            lambda conn_id, msg_data: handle_send_message(conn_id, msg_data)
        )

        websocket_manager.register_message_handler(
            "get_history",
            lambda conn_id, msg_data: handle_get_history(conn_id, msg_data)
        )

        # 如果有会话ID，自动订阅会话频道
        if session_id:
            await websocket_manager.subscribe_to_channel(
                connection_id,
                f"session:{session_id}"
            )

        # 监听消息
        while True:
            try:
                # 接收消息
                raw_message = await websocket.receive_text()

                # 处理消息
                await websocket_manager.handle_message(connection_id, raw_message)

            except WebSocketDisconnect:
                logger.info(f"WebSocket disconnected: {connection_id}")
                break
            except Exception as e:
                logger.error(f"Error handling WebSocket message: {e}")
                await websocket_manager.send_to_connection(
                    connection_id,
                    Message(
                        type="error",
                        data={"error": "Message processing error"}
                    )
                )

    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected during setup: {connection_id}")
    except Exception as e:
        logger.error(f"WebSocket connection error: {e}")
        try:
            await websocket.close(code=1011, reason="Internal server error")
        except:
            pass

    finally:
        # 清理连接
        if connection_id:
            await websocket_manager.disconnect(connection_id)


async def handle_send_message(connection_id: str, message_data: Dict[str, Any]) -> None:
    """处理发送消息请求
    
    Args:
        connection_id: 连接ID
        message_data: 消息数据
    """
    try:
        # 获取连接信息
        connection_info = websocket_manager.get_connection_info(connection_id)
        if not connection_info or not connection_info.session_id:
            await websocket_manager.send_to_connection(
                connection_id,
                Message(
                    type="error",
                    data={"error": "No session associated with connection"}
                )
            )
            return

        # 解析消息请求
        message_content = message_data.get("message")
        if not message_content:
            await websocket_manager.send_to_connection(
                connection_id,
                Message(
                    type="error",
                    data={"error": "Missing message content"}
                )
            )
            return

        # 创建消息请求
        message_request = MessageRequest(message=message_content)

        # 发送开始消息
        await websocket_manager.send_to_connection(
            connection_id,
            Message(
                type="message_start",
                data={
                    "session_id": connection_info.session_id,
                    "message": message_content,
                    "timestamp": datetime.utcnow().isoformat()
                }
            )
        )

        # 广播到会话频道（通知其他连接）
        await websocket_manager.send_to_channel(
            f"session:{connection_info.session_id}",
            Message(
                type="user_message",
                data={
                    "session_id": connection_info.session_id,
                    "message": message_content,
                    "user_id": connection_info.user_id,
                    "timestamp": datetime.utcnow().isoformat()
                }
            ),
            exclude_connection=connection_id
        )

        # 处理流式响应
        async for chunk in conversation_service.send_message_stream(
                connection_info.session_id,
                message_request
        ):
            # 发送流式数据到当前连接
            await websocket_manager.send_to_connection(
                connection_id,
                Message(
                    type="stream_chunk",
                    data=chunk
                )
            )

            # 如果是模型响应，也广播到会话频道
            if chunk.get("type") in ["chunk", "complete"]:
                await websocket_manager.send_to_channel(
                    f"session:{connection_info.session_id}",
                    Message(
                        type="model_response_chunk",
                        data=chunk
                    ),
                    exclude_connection=connection_id
                )

        # 发送完成消息
        await websocket_manager.send_to_connection(
            connection_id,
            Message(
                type="message_complete",
                data={
                    "session_id": connection_info.session_id,
                    "timestamp": datetime.utcnow().isoformat()
                }
            )
        )

    except Exception as e:
        logger.error(f"Error handling send_message: {e}")
        await websocket_manager.send_to_connection(
            connection_id,
            Message(
                type="error",
                data={"error": f"Failed to send message: {str(e)}"}
            )
        )


async def handle_get_history(connection_id: str, message_data: Dict[str, Any]) -> None:
    """处理获取历史记录请求
    
    Args:
        connection_id: 连接ID
        message_data: 消息数据
    """
    try:
        # 获取连接信息
        connection_info = websocket_manager.get_connection_info(connection_id)
        if not connection_info or not connection_info.session_id:
            await websocket_manager.send_to_connection(
                connection_id,
                Message(
                    type="error",
                    data={"error": "No session associated with connection"}
                )
            )
            return

        # 解析请求参数
        page = message_data.get("page", 1)
        size = message_data.get("size", 20)
        model_filter = message_data.get("model_filter")

        # 获取对话历史
        history = await conversation_service.list_conversations(
            session_id=connection_info.session_id,
            model_filter=model_filter,
            page=page,
            size=size
        )

        # 发送历史记录
        await websocket_manager.send_to_connection(
            connection_id,
            Message(
                type="history_response",
                data=history
            )
        )

    except Exception as e:
        logger.error(f"Error handling get_history: {e}")
        await websocket_manager.send_to_connection(
            connection_id,
            Message(
                type="error",
                data={"error": f"Failed to get history: {str(e)}"}
            )
        )


@router.get("/status")
async def get_websocket_status(
        api_key_info: dict = Depends(verify_api_key)
) -> JSONResponse:
    """获取WebSocket服务状态
    
    Returns:
        WebSocket服务状态信息
    """
    try:
        stats = websocket_manager.get_stats()

        return JSONResponse({
            "status": "running" if websocket_manager._running else "stopped",
            "stats": stats,
            "timestamp": datetime.utcnow().isoformat()
        })

    except Exception as e:
        logger.error(f"Error getting WebSocket status: {e}")
        raise HTTPException(status_code=500, detail="Failed to get WebSocket status")


@router.post("/broadcast")
async def broadcast_message(
        message_type: str,
        data: Dict[str, Any],
        channel: Optional[str] = None,
        session_id: Optional[str] = None,
        user_id: Optional[str] = None,
        api_key_info: dict = Depends(verify_api_key)
) -> JSONResponse:
    """广播消息
    
    Args:
        message_type: 消息类型
        data: 消息数据
        channel: 频道名称（可选）
        session_id: 会话ID（可选）
        user_id: 用户ID（可选）
    
    Returns:
        广播结果
    """
    try:
        message = Message(
            type=message_type,
            data=data
        )

        sent_count = 0

        if channel:
            # 发送到频道
            sent_count = await websocket_manager.send_to_channel(channel, message)
        elif session_id:
            # 发送到会话
            sent_count = await websocket_manager.send_to_session(session_id, message)
        elif user_id:
            # 发送到用户
            sent_count = await websocket_manager.send_to_user(user_id, message)
        else:
            # 广播到所有连接
            sent_count = await websocket_manager.broadcast(message)

        return JSONResponse({
            "success": True,
            "sent_count": sent_count,
            "message_type": message_type,
            "timestamp": datetime.utcnow().isoformat()
        })

    except Exception as e:
        logger.error(f"Error broadcasting message: {e}")
        raise HTTPException(status_code=500, detail="Failed to broadcast message")


@router.get("/connections")
async def get_connections(
        session_id: Optional[str] = Query(None),
        user_id: Optional[str] = Query(None),
        api_key_info: dict = Depends(verify_api_key)
) -> JSONResponse:
    """获取连接信息
    
    Args:
        session_id: 会话ID过滤（可选）
        user_id: 用户ID过滤（可选）
    
    Returns:
        连接信息列表
    """
    try:
        connections = []

        if session_id:
            # 获取会话的连接
            connection_ids = websocket_manager.get_session_connections(session_id)
            for conn_id in connection_ids:
                conn_info = websocket_manager.get_connection_info(conn_id)
                if conn_info:
                    connections.append({
                        "connection_id": conn_id,
                        "session_id": conn_info.session_id,
                        "user_id": conn_info.user_id,
                        "connected_at": conn_info.connected_at,
                        "last_ping": conn_info.last_ping,
                        "subscriptions": list(conn_info.subscriptions)
                    })
        elif user_id:
            # 获取用户的连接
            connection_ids = websocket_manager.get_user_connections(user_id)
            for conn_id in connection_ids:
                conn_info = websocket_manager.get_connection_info(conn_id)
                if conn_info:
                    connections.append({
                        "connection_id": conn_id,
                        "session_id": conn_info.session_id,
                        "user_id": conn_info.user_id,
                        "connected_at": conn_info.connected_at,
                        "last_ping": conn_info.last_ping,
                        "subscriptions": list(conn_info.subscriptions)
                    })
        else:
            # 获取所有连接
            for conn_id, conn_info in websocket_manager._connections.items():
                connections.append({
                    "connection_id": conn_id,
                    "session_id": conn_info.session_id,
                    "user_id": conn_info.user_id,
                    "connected_at": conn_info.connected_at,
                    "last_ping": conn_info.last_ping,
                    "subscriptions": list(conn_info.subscriptions)
                })

        return JSONResponse({
            "connections": connections,
            "total": len(connections),
            "timestamp": datetime.utcnow().isoformat()
        })

    except Exception as e:
        logger.error(f"Error getting connections: {e}")
        raise HTTPException(status_code=500, detail="Failed to get connections")


@router.delete("/connections/{connection_id}")
async def disconnect_connection(
        connection_id: str,
        api_key_info: dict = Depends(verify_api_key)
) -> JSONResponse:
    """断开指定连接
    
    Args:
        connection_id: 连接ID
    
    Returns:
        断开结果
    """
    try:
        # 检查连接是否存在
        connection_info = websocket_manager.get_connection_info(connection_id)
        if not connection_info:
            raise HTTPException(status_code=404, detail="Connection not found")

        # 断开连接
        await websocket_manager.disconnect(connection_id, code=1000)

        return JSONResponse({
            "success": True,
            "connection_id": connection_id,
            "timestamp": datetime.utcnow().isoformat()
        })

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error disconnecting connection: {e}")
        raise HTTPException(status_code=500, detail="Failed to disconnect connection")


# 启动和停止WebSocket管理器的生命周期事件
@router.on_event("startup")
async def startup_websocket_manager():
    """启动WebSocket管理器"""
    await websocket_manager.start()
    await conversation_service.initialize()
    await session_service.initialize()
    logger.info("WebSocket manager and services started")


@router.on_event("shutdown")
async def shutdown_websocket_manager():
    """停止WebSocket管理器"""
    await websocket_manager.stop()
    await conversation_service.cleanup()
    await session_service.cleanup()
    logger.info("WebSocket manager and services stopped")
