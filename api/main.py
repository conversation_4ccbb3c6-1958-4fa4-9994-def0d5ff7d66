# -*- coding: utf-8 -*-
"""
LLM对比工具 API服务主应用
"""

import sys
from contextlib import asynccontextmanager
from pathlib import Path

from fastapi import FastAPI, Request, HTTPException
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from utils.logger import logger
from sqlalchemy.exc import SQLAlchemyError

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

from api.middleware.logging import LoggingMiddleware
from api.middleware.performance import PerformanceMiddleware
from api.routers import sessions, conversations, models, prompts, stats, export
from database.connection import init_database, close_database
from api.dependencies.container import init_container, cleanup_container, get_api_settings
from api.exceptions.handlers import (
    APIError, api_error_handler, http_exception_handler,
    validation_exception_handler, database_exception_handler,
    service_exception_handler, general_exception_handler
)
from api.middleware.request_middleware import (
    RequestTrackingMiddleware, AccessLoggingMiddleware,
    PerformanceMonitoringMiddleware, SecurityHeadersMiddleware,
    RateLimitMiddleware
)
from services.base_service import ServiceError


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化
    logger.info("正在启动LLM对比工具API服务...")

    # 初始化依赖注入容器
    await init_container()
    logger.info("依赖注入容器已初始化")

    # 初始化日志
    logger.info("日志系统已初始化")

    # 初始化数据库
    await init_database()

    logger.info("API服务启动完成")

    yield

    # 关闭时清理
    logger.info("正在关闭API服务...")
    await close_database()
    await cleanup_container()
    logger.info("API服务已关闭")


def create_app() -> FastAPI:
    """创建FastAPI应用实例
    
    使用依赖注入容器中的配置来创建和配置应用。
    
    Returns:
        FastAPI: 配置好的应用实例
    """
    # 创建FastAPI应用实例
    app = FastAPI(
        title="LLM对比工具 API",
        description="用于对比多个大语言模型响应的API服务",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc",
        openapi_url="/openapi.json",
        lifespan=lifespan
    )

    # 在应用启动后配置中间件（因为需要依赖注入容器已初始化）
    @app.middleware("http")
    async def configure_middleware_with_settings(request: Request, call_next):
        """动态配置中间件"""
        # 这里可以根据配置动态调整中间件行为
        response = await call_next(request)
        return response

    return app


# 创建应用实例
app = create_app()


@app.on_event("startup")
async def configure_app_with_settings():
    """应用启动后配置
    
    在依赖注入容器初始化后配置CORS和其他中间件。
    """
    try:
        # 获取配置
        settings = get_api_settings()

        # 请求追踪中间件（最先添加）
        app.add_middleware(RequestTrackingMiddleware)
        logger.info("请求追踪中间件已配置")

        # 安全头中间件
        app.add_middleware(SecurityHeadersMiddleware)
        logger.info("安全头中间件已配置")

        # 速率限制中间件
        if hasattr(settings, 'rate_limit_enabled') and settings.rate_limit_enabled:
            requests_per_minute = getattr(settings, 'rate_limit_requests_per_minute', 60)
            app.add_middleware(RateLimitMiddleware, requests_per_minute=requests_per_minute)
            logger.info(f"速率限制中间件已配置: {requests_per_minute} 请求/分钟")

        # 性能监控中间件
        from api.dependencies.container import get_performance_monitor
        try:
            perf_monitor = get_performance_monitor()
            app.add_middleware(PerformanceMonitoringMiddleware, performance_monitor=perf_monitor)
            logger.info("性能监控中间件已配置")
        except Exception as e:
            logger.warning(f"性能监控中间件配置失败: {e}")
            app.add_middleware(PerformanceMonitoringMiddleware)

        # 访问日志中间件
        app.add_middleware(AccessLoggingMiddleware)
        logger.info("访问日志中间件已配置")

        # 配置CORS
        app.add_middleware(
            CORSMiddleware,
            allow_origins=settings.ALLOWED_ORIGINS,
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

        # 配置可信主机
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=settings.ALLOWED_HOSTS
        )

        # 添加自定义中间件
        app.add_middleware(LoggingMiddleware)
        app.add_middleware(PerformanceMiddleware)

        logger.info("应用中间件配置完成")

    except Exception as e:
        logger.error(f"配置应用中间件时出错: {e}")
        # 使用默认配置
        app.add_middleware(RequestTrackingMiddleware)
        app.add_middleware(SecurityHeadersMiddleware)
        app.add_middleware(AccessLoggingMiddleware)
        app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=["*"]
        )
        app.add_middleware(LoggingMiddleware)
        app.add_middleware(PerformanceMiddleware)


def register_exception_handlers(app: FastAPI):
    """注册异常处理器"""
    app.add_exception_handler(APIError, api_error_handler)
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(SQLAlchemyError, database_exception_handler)
    app.add_exception_handler(ServiceError, service_exception_handler)
    app.add_exception_handler(Exception, general_exception_handler)

    logger.info("异常处理器注册完成")


# 注册异常处理器
register_exception_handlers(app)


# 根路径
@app.get("/")
async def root():
    """API根路径"""
    return {
        "message": "LLM对比工具 API服务",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/api/v1/health"
    }


# 健康检查
@app.get("/api/v1/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": "llm-comparison-api",
        "version": "1.0.0"
    }


# 注册路由
app.include_router(sessions.router, prefix="/api/v1", tags=["会话管理"])
app.include_router(conversations.router, prefix="/api/v1", tags=["对话"])
app.include_router(models.router, prefix="/api/v1", tags=["模型管理"])
app.include_router(prompts.router, prefix="/api/v1", tags=["提示词管理"])
app.include_router(stats.router, prefix="/api/v1", tags=["统计监控"])
app.include_router(export.router, prefix="/api/v1", tags=["数据导出"])

if __name__ == "__main__":
    import uvicorn

    # 开发环境运行
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
