"""请求中间件系统"""

import time
import uuid
from typing import Callable, Optional
from contextvars import ContextV<PERSON>

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from utils.logger import logger

from api.config.logging_config import get_logger_manager
from src.utils.performance_monitor import PerformanceMonitor

# 请求上下文变量
request_id_var: ContextVar[str] = ContextVar('request_id', default='')
user_id_var: ContextVar[Optional[str]] = ContextVar('user_id', default=None)


class RequestTrackingMiddleware(BaseHTTPMiddleware):
    """请求追踪中间件"""

    def __init__(self, app, header_name: str = "X-Request-ID"):
        super().__init__(app)
        self.header_name = header_name

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 生成或获取请求ID
        request_id = request.headers.get(self.header_name) or str(uuid.uuid4())
        request_id_var.set(request_id)

        # 从请求中提取用户ID（如果有的话）
        user_id = self._extract_user_id(request)
        if user_id:
            user_id_var.set(user_id)

        # 处理请求
        response = await call_next(request)

        # 在响应头中添加请求ID
        response.headers[self.header_name] = request_id

        return response

    def _extract_user_id(self, request: Request) -> Optional[str]:
        """从请求中提取用户ID"""
        # 从Authorization头中提取
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            # 这里可以解析JWT token获取用户ID
            # 暂时返回None，后续可以根据实际认证方案实现
            pass

        # 从查询参数中提取
        return request.query_params.get("user_id")


class AccessLoggingMiddleware(BaseHTTPMiddleware):
    """访问日志中间件"""

    def __init__(self, app):
        super().__init__(app)
        self.access_logger = None
        self._setup_logger()

    def _setup_logger(self):
        """设置访问日志器"""
        try:
            logger_manager = get_logger_manager()
            self.access_logger = logger_manager.create_access_logger()
        except Exception as e:
            logger.warning(f"无法创建访问日志器: {e}")

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()

        # 获取客户端信息
        client_ip = self._get_client_ip(request)
        user_agent = request.headers.get("User-Agent", "")

        # 处理请求
        response = await call_next(request)

        # 计算处理时间
        duration = int((time.time() - start_time) * 1000)

        # 记录访问日志
        if self.access_logger:
            self.access_logger.info(
                "Request processed",
                method=request.method,
                path=str(request.url.path),
                query=str(request.url.query) if request.url.query else "",
                status_code=response.status_code,
                duration=duration,
                client_ip=client_ip,
                user_agent=user_agent,
                request_id=request_id_var.get(),
                user_id=user_id_var.get()
            )

        return response

    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        # 检查代理头
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()

        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip

        # 返回直接连接的IP
        return request.client.host if request.client else "unknown"


class PerformanceMonitoringMiddleware(BaseHTTPMiddleware):
    """性能监控中间件"""

    def __init__(self, app, performance_monitor: Optional[PerformanceMonitor] = None):
        super().__init__(app)
        self.performance_monitor = performance_monitor
        self.perf_logger = None
        self._setup_logger()

    def _setup_logger(self):
        """设置性能日志器"""
        try:
            logger_manager = get_logger_manager()
            self.perf_logger = logger_manager.create_performance_logger()
        except Exception as e:
            logger.warning(f"无法创建性能日志器: {e}")

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()

        # 记录请求开始
        operation = f"{request.method} {request.url.path}"

        try:
            # 处理请求
            response = await call_next(request)

            # 计算处理时间
            duration = int((time.time() - start_time) * 1000)

            # 记录性能指标
            if self.performance_monitor:
                self.performance_monitor.record_request(
                    endpoint=request.url.path,
                    method=request.method,
                    duration=duration,
                    status_code=response.status_code
                )

            # 记录性能日志
            if self.perf_logger:
                self.perf_logger.info(
                    "Performance metrics",
                    operation=operation,
                    duration=duration,
                    status_code=response.status_code,
                    request_id=request_id_var.get(),
                    details={
                        "method": request.method,
                        "path": request.url.path,
                        "query_params": dict(request.query_params),
                        "content_length": response.headers.get("content-length", 0)
                    }
                )

            # 对于慢请求发出警告
            if duration > 5000:  # 5秒
                logger.warning(
                    f"慢请求检测: {operation} 耗时 {duration}ms",
                    request_id=request_id_var.get()
                )

            return response

        except Exception as e:
            # 记录错误性能指标
            duration = int((time.time() - start_time) * 1000)

            if self.performance_monitor:
                self.performance_monitor.record_request(
                    endpoint=request.url.path,
                    method=request.method,
                    duration=duration,
                    status_code=500,
                    error=str(e)
                )

            logger.error(
                f"请求处理异常: {operation} 耗时 {duration}ms, 错误: {e}",
                request_id=request_id_var.get()
            )

            raise


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """安全头中间件"""

    def __init__(self, app):
        super().__init__(app)
        self.security_headers = {
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Referrer-Policy": "strict-origin-when-cross-origin",
            "Content-Security-Policy": "default-src 'self'"
        }

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)

        # 添加安全头
        for header, value in self.security_headers.items():
            response.headers[header] = value

        return response


class RateLimitMiddleware(BaseHTTPMiddleware):
    """简单的速率限制中间件"""

    def __init__(self, app, requests_per_minute: int = 60):
        super().__init__(app)
        self.requests_per_minute = requests_per_minute
        self.request_counts = {}  # 简单的内存存储，生产环境应使用Redis
        self.window_size = 60  # 1分钟窗口

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        client_ip = self._get_client_ip(request)
        current_time = int(time.time())
        window_start = current_time - (current_time % self.window_size)

        # 清理过期的计数
        self._cleanup_expired_counts(window_start)

        # 检查当前窗口的请求数
        key = f"{client_ip}:{window_start}"
        current_count = self.request_counts.get(key, 0)

        if current_count >= self.requests_per_minute:
            # 返回429状态码
            from fastapi.responses import JSONResponse
            return JSONResponse(
                status_code=429,
                content={
                    "error": {
                        "code": 429,
                        "message": "请求过于频繁，请稍后再试",
                        "type": "rate_limit_exceeded"
                    }
                },
                headers={
                    "Retry-After": str(self.window_size),
                    "X-RateLimit-Limit": str(self.requests_per_minute),
                    "X-RateLimit-Remaining": "0",
                    "X-RateLimit-Reset": str(window_start + self.window_size)
                }
            )

        # 增加请求计数
        self.request_counts[key] = current_count + 1

        # 处理请求
        response = await call_next(request)

        # 添加速率限制头
        remaining = max(0, self.requests_per_minute - self.request_counts[key])
        response.headers["X-RateLimit-Limit"] = str(self.requests_per_minute)
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        response.headers["X-RateLimit-Reset"] = str(window_start + self.window_size)

        return response

    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()

        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip

        return request.client.host if request.client else "unknown"

    def _cleanup_expired_counts(self, current_window: int):
        """清理过期的请求计数"""
        expired_keys = [
            key for key in self.request_counts.keys()
            if int(key.split(":")[1]) < current_window - self.window_size
        ]
        for key in expired_keys:
            del self.request_counts[key]


# 便捷函数
def get_request_id() -> str:
    """获取当前请求ID"""
    return request_id_var.get()


def get_user_id() -> Optional[str]:
    """获取当前用户ID"""
    return user_id_var.get()
