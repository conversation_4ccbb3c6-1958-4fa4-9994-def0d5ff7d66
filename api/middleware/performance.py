# -*- coding: utf-8 -*-
"""
性能监控中间件
"""

import time
from collections import defaultdict, deque
from datetime import datetime
from typing import Callable

import psutil
from fastapi import Request, Response
from utils.logger import logger
from starlette.middleware.base import BaseHTTPMiddleware


class PerformanceMonitor:
    """性能监控器"""

    def __init__(self, max_records: int = 1000):
        self.max_records = max_records
        self.request_times = deque(maxlen=max_records)
        self.endpoint_stats = defaultdict(lambda: {
            'count': 0,
            'total_time': 0,
            'min_time': float('inf'),
            'max_time': 0,
            'errors': 0
        })
        self.status_codes = defaultdict(int)
        self.start_time = datetime.now()

    def record_request(self, endpoint: str, method: str, duration: float, status_code: int):
        """记录请求性能数据"""
        # 记录请求时间
        self.request_times.append({
            'timestamp': datetime.now(),
            'duration': duration,
            'endpoint': endpoint,
            'method': method,
            'status_code': status_code
        })

        # 更新端点统计
        key = f"{method} {endpoint}"
        stats = self.endpoint_stats[key]
        stats['count'] += 1
        stats['total_time'] += duration
        stats['min_time'] = min(stats['min_time'], duration)
        stats['max_time'] = max(stats['max_time'], duration)

        if status_code >= 400:
            stats['errors'] += 1

        # 更新状态码统计
        self.status_codes[status_code] += 1

    def get_stats(self) -> dict:
        """获取性能统计信息"""
        total_requests = len(self.request_times)

        if total_requests == 0:
            return {
                'total_requests': 0,
                'avg_response_time': 0,
                'requests_per_minute': 0,
                'error_rate': 0,
                'uptime_seconds': (datetime.now() - self.start_time).total_seconds()
            }

        # 计算平均响应时间
        avg_response_time = sum(r['duration'] for r in self.request_times) / total_requests

        # 计算每分钟请求数（基于最近的请求）
        now = datetime.now()
        recent_requests = [r for r in self.request_times if (now - r['timestamp']).total_seconds() <= 60]
        requests_per_minute = len(recent_requests)

        # 计算错误率
        error_requests = sum(1 for r in self.request_times if r['status_code'] >= 400)
        error_rate = error_requests / total_requests if total_requests > 0 else 0

        # 系统资源使用情况
        cpu_percent = psutil.cpu_percent()
        memory = psutil.virtual_memory()

        return {
            'total_requests': total_requests,
            'avg_response_time': round(avg_response_time, 4),
            'requests_per_minute': requests_per_minute,
            'error_rate': round(error_rate, 4),
            'uptime_seconds': round((datetime.now() - self.start_time).total_seconds(), 2),
            'system': {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_used_mb': round(memory.used / 1024 / 1024, 2),
                'memory_total_mb': round(memory.total / 1024 / 1024, 2)
            },
            'endpoint_stats': dict(self.endpoint_stats),
            'status_codes': dict(self.status_codes)
        }


# 全局性能监控器实例
performance_monitor = PerformanceMonitor()


class PerformanceMiddleware(BaseHTTPMiddleware):
    """性能监控中间件"""

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()

        try:
            response = await call_next(request)

            # 计算处理时间
            duration = time.time() - start_time

            # 记录性能数据
            endpoint = request.url.path
            method = request.method
            status_code = response.status_code

            performance_monitor.record_request(endpoint, method, duration, status_code)

            # 如果响应时间过长，记录警告
            if duration > 5.0:  # 5秒
                logger.warning(
                    f"慢请求检测",
                    extra={
                        "endpoint": endpoint,
                        "method": method,
                        "duration": round(duration, 4),
                        "status_code": status_code
                    }
                )

            return response

        except Exception as e:
            # 记录异常请求的性能数据
            duration = time.time() - start_time
            endpoint = request.url.path
            method = request.method

            performance_monitor.record_request(endpoint, method, duration, 500)

            raise


def get_performance_stats() -> dict:
    """获取性能统计信息"""
    return performance_monitor.get_stats()
