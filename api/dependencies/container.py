"""依赖注入容器模块

提供统一的依赖管理和注入机制，支持单例模式和工厂模式。
"""

import asyncio
from functools import lru_cache
from typing import Dict, Any, Callable, TypeVar, Type, Optional

from api.config.logging_config import LoggerManager, setup_logging
from api.config.unified_config import UnifiedConfig, get_unified_config
from services.cache_service import CacheService
from services.conversation_service import ConversationService
from services.cost_service import CostService
from services.model_service import ModelService
from services.session_service import SessionService
from services.user_behavior_service import UserBehaviorService
from src.core.comparison_engine import ComparisonEngine
from src.core.conversation_manager import ConversationManager
from src.core.model_manager import ModelManager
from src.core.prompt_manager import PromptManager
from utils.logger import get_logger
from utils.performance_monitor import PerformanceMonitor

T = TypeVar('T')
logger = get_logger(__name__)


class DependencyContainer:
    """依赖注入容器
    
    管理应用程序中所有依赖项的创建、配置和生命周期。
    支持单例模式、工厂模式和异步初始化。
    """

    def __init__(self):
        self._singletons: Dict[str, Any] = {}
        self._factories: Dict[str, Callable] = {}
        self._initialized = False
        self._lock = asyncio.Lock()

    async def initialize(self):
        """初始化容器和所有依赖项"""
        if self._initialized:
            return

        async with self._lock:
            if self._initialized:
                return

            logger.info("正在初始化依赖注入容器...")

            # 注册核心配置
            await self._register_core_dependencies()

            # 注册服务层依赖
            await self._register_service_dependencies()

            # 注册工具类依赖
            await self._register_utility_dependencies()

            self._initialized = True
            logger.info("依赖注入容器初始化完成")

    async def _register_core_dependencies(self):
        """注册核心依赖项"""
        # 统一配置管理器
        unified_config = get_unified_config()
        self._singletons['unified_config'] = unified_config
        self._singletons['api_settings'] = unified_config  # 兼容性别名
        self._singletons['config_manager'] = unified_config  # 兼容性别名

        # 初始化日志系统
        logger_manager = setup_logging(
            level=unified_config.log_level,
            log_dir=unified_config.log_dir
        )
        self._singletons['logger_manager'] = logger_manager

        # 核心管理器
        config_manager = unified_config
        self._singletons['model_manager'] = ModelManager(config_manager)
        self._singletons['conversation_manager'] = ConversationManager(config_manager)
        self._singletons['prompt_manager'] = PromptManager()

        # 比较引擎
        self._singletons['comparison_engine'] = ComparisonEngine(
            model_manager=self._singletons['model_manager'],
            conv_manager=self._singletons['conversation_manager'],
            prompt_manager=self._singletons['prompt_manager']
        )

    async def _register_service_dependencies(self):
        """注册服务层依赖项"""
        # 缓存服务
        self._singletons['cache_service'] = CacheService()

        # 业务服务（使用工厂模式，因为需要数据库会话）
        self._factories['session_service'] = lambda: SessionService()
        self._factories['conversation_service'] = lambda: ConversationService(
            comparison_engine=self.get('comparison_engine'),
            cache_service=self.get('cache_service')
        )
        self._factories['model_service'] = lambda: ModelService(
            model_manager=self.get('model_manager')
        )
        self._factories['cost_service'] = lambda: CostService()
        self._factories['user_behavior_service'] = lambda: UserBehaviorService()

    async def _register_utility_dependencies(self):
        """注册工具类依赖项"""
        self._singletons['performance_monitor'] = PerformanceMonitor()

    def get(self, name: str) -> Any:
        """获取依赖项
        
        Args:
            name: 依赖项名称
            
        Returns:
            依赖项实例
            
        Raises:
            KeyError: 依赖项不存在
        """
        if not self._initialized:
            raise RuntimeError("容器尚未初始化，请先调用 initialize()")

        # 检查单例
        if name in self._singletons:
            return self._singletons[name]

        # 检查工厂
        if name in self._factories:
            return self._factories[name]()

        raise KeyError(f"依赖项 '{name}' 未注册")

    def get_typed(self, dependency_type: Type[T]) -> T:
        """根据类型获取依赖项
        
        Args:
            dependency_type: 依赖项类型
            
        Returns:
            依赖项实例
        """
        type_name = dependency_type.__name__.lower().replace('manager', '_manager').replace('service', '_service')
        return self.get(type_name)

    def register_singleton(self, name: str, instance: Any):
        """注册单例依赖项
        
        Args:
            name: 依赖项名称
            instance: 依赖项实例
        """
        self._singletons[name] = instance

    def register_factory(self, name: str, factory: Callable[[], Any]):
        """注册工厂依赖项
        
        Args:
            name: 依赖项名称
            factory: 工厂函数
        """
        self._factories[name] = factory

    async def cleanup(self):
        """清理容器和所有依赖项"""
        logger.info("正在清理依赖注入容器...")

        # 清理有清理方法的依赖项
        for name, instance in self._singletons.items():
            if hasattr(instance, 'cleanup'):
                try:
                    if asyncio.iscoroutinefunction(instance.cleanup):
                        await instance.cleanup()
                    else:
                        instance.cleanup()
                except Exception as e:
                    logger.error(f"清理依赖项 {name} 时出错: {e}")

        self._singletons.clear()
        self._factories.clear()
        self._initialized = False

        logger.info("依赖注入容器清理完成")


# 全局容器实例
_container: Optional[DependencyContainer] = None


@lru_cache(maxsize=1)
def get_container() -> DependencyContainer:
    """获取全局依赖注入容器
    
    Returns:
        DependencyContainer: 全局容器实例
    """
    global _container
    if _container is None:
        _container = DependencyContainer()
    return _container


async def init_container():
    """初始化全局容器"""
    container = get_container()
    await container.initialize()


async def cleanup_container():
    """清理全局容器"""
    global _container
    if _container:
        await _container.cleanup()
        _container = None
        # 清除缓存
        get_container.cache_clear()


# FastAPI依赖项函数
def get_api_settings() -> UnifiedConfig:
    """获取API配置依赖项"""
    return get_container().get('unified_config')


def get_config_manager() -> UnifiedConfig:
    """获取配置管理器依赖项"""
    return get_container().get('unified_config')


def get_unified_config_dep() -> UnifiedConfig:
    """获取统一配置依赖项"""
    return get_container().get('unified_config')


def get_model_manager() -> ModelManager:
    """获取模型管理器依赖项"""
    return get_container().get('model_manager')


def get_conversation_manager() -> ConversationManager:
    """获取对话管理器依赖项"""
    return get_container().get('conversation_manager')


def get_prompt_manager() -> PromptManager:
    """获取提示词管理器依赖项"""
    return get_container().get('prompt_manager')


def get_comparison_engine() -> ComparisonEngine:
    """获取比较引擎依赖项"""
    return get_container().get('comparison_engine')


def get_session_service() -> SessionService:
    """获取会话服务依赖项"""
    return get_container().get('session_service')


def get_conversation_service() -> ConversationService:
    """获取对话服务依赖项"""
    return get_container().get('conversation_service')


def get_model_service() -> ModelService:
    """获取模型服务依赖项"""
    return get_container().get('model_service')


def get_cache_service() -> CacheService:
    """获取缓存服务依赖项"""
    return get_container().get('cache_service')


def get_cost_service() -> CostService:
    """获取成本服务依赖项"""
    return get_container().get('cost_service')


def get_user_behavior_service() -> UserBehaviorService:
    """获取用户行为服务依赖项"""
    return get_container().get('user_behavior_service')


def get_performance_monitor() -> PerformanceMonitor:
    """获取性能监控器依赖项"""
    return get_container().get('performance_monitor')


def get_logger_manager() -> LoggerManager:
    """获取日志管理器依赖项"""
    return get_container().get('logger_manager')
