# -*- coding: utf-8 -*-
"""
数据库依赖项
"""

from typing import Generator

from fastapi import HTTPException, status
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session

from database.connection import get_db_session
from utils.logger import get_logger

logger = get_logger(__name__)


def get_database() -> Generator[Session, None, None]:
    """
    获取数据库会话依赖
    
    Yields:
        Session: 数据库会话
        
    Raises:
        HTTPException: 数据库连接失败时抛出500错误
    """
    db = None
    try:
        db = get_db_session()
        yield db
    except SQLAlchemyError as e:
        logger.error(f"Database connection error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database connection failed"
        )
    except Exception as e:
        logger.error(f"Unexpected database error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )
    finally:
        if db:
            try:
                db.close()
            except Exception as e:
                logger.error(f"Error closing database session: {e}")


def get_db() -> Generator[Session, None, None]:
    """
    数据库会话依赖的简化别名
    """
    return get_database()
