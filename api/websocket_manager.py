"""WebSocket管理器

负责管理WebSocket连接，处理实时消息传输和连接状态管理。
"""

import asyncio
import json
import time
from dataclasses import dataclass
from datetime import datetime
from typing import Dict, List, Set, Any, Optional, Callable
from uuid import uuid4

from fastapi import WebSocket
from fastapi.websockets import WebSocketState

from utils.logger import get_logger
from utils.performance_monitor import PerformanceMonitor


@dataclass
class ConnectionInfo:
    """连接信息"""
    connection_id: str
    websocket: WebSocket
    session_id: Optional[str] = None
    user_id: Optional[str] = None
    connected_at: float = 0
    last_ping: float = 0
    subscriptions: Set[str] = None
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.subscriptions is None:
            self.subscriptions = set()
        if self.metadata is None:
            self.metadata = {}
        if self.connected_at == 0:
            self.connected_at = time.time()
        if self.last_ping == 0:
            self.last_ping = self.connected_at


@dataclass
class Message:
    """WebSocket消息"""
    type: str
    data: Any
    timestamp: float = 0
    message_id: str = ""
    target: Optional[str] = None  # 目标连接ID或频道

    def __post_init__(self):
        if self.timestamp == 0:
            self.timestamp = time.time()
        if not self.message_id:
            self.message_id = str(uuid4())

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "type": self.type,
            "data": self.data,
            "timestamp": self.timestamp,
            "message_id": self.message_id,
            "target": self.target
        }


class WebSocketManager:
    """WebSocket管理器
    
    功能：
    - 连接管理
    - 消息广播
    - 频道订阅
    - 心跳检测
    - 连接状态监控
    """

    def __init__(self):
        self.logger = get_logger("WebSocketManager")
        self.performance_monitor = PerformanceMonitor()

        # 连接管理
        self._connections: Dict[str, ConnectionInfo] = {}
        self._session_connections: Dict[str, Set[str]] = {}  # session_id -> connection_ids
        self._user_connections: Dict[str, Set[str]] = {}  # user_id -> connection_ids

        # 频道订阅
        self._channels: Dict[str, Set[str]] = {}  # channel -> connection_ids

        # 消息处理
        self._message_handlers: Dict[str, Callable] = {}
        self._message_queue: asyncio.Queue = asyncio.Queue()

        # 统计信息
        self._stats = {
            "total_connections": 0,
            "active_connections": 0,
            "messages_sent": 0,
            "messages_received": 0,
            "disconnections": 0,
            "errors": 0
        }

        # 心跳配置
        self.heartbeat_interval = 30  # 秒
        self.heartbeat_timeout = 60  # 秒

        # 启动后台任务
        self._background_tasks: List[asyncio.Task] = []
        self._running = False

    async def start(self) -> None:
        """启动WebSocket管理器"""
        if self._running:
            return

        self.logger.info("Starting WebSocket manager")
        self._running = True

        # 启动后台任务
        self._background_tasks = [
            asyncio.create_task(self._heartbeat_task()),
            asyncio.create_task(self._message_processor()),
            asyncio.create_task(self._cleanup_task())
        ]

        self.logger.info("WebSocket manager started")

    async def stop(self) -> None:
        """停止WebSocket管理器"""
        if not self._running:
            return

        self.logger.info("Stopping WebSocket manager")
        self._running = False

        # 取消后台任务
        for task in self._background_tasks:
            task.cancel()

        # 等待任务完成
        await asyncio.gather(*self._background_tasks, return_exceptions=True)

        # 关闭所有连接
        await self._close_all_connections()

        self.logger.info("WebSocket manager stopped")

    async def connect(
            self,
            websocket: WebSocket,
            session_id: Optional[str] = None,
            user_id: Optional[str] = None,
            metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """建立WebSocket连接
        
        Args:
            websocket: WebSocket实例
            session_id: 会话ID
            user_id: 用户ID
            metadata: 连接元数据
        
        Returns:
            连接ID
        """
        connection_id = str(uuid4())

        try:
            # 接受连接
            await websocket.accept()

            # 创建连接信息
            connection_info = ConnectionInfo(
                connection_id=connection_id,
                websocket=websocket,
                session_id=session_id,
                user_id=user_id,
                metadata=metadata or {}
            )

            # 存储连接
            self._connections[connection_id] = connection_info

            # 按会话和用户分组
            if session_id:
                if session_id not in self._session_connections:
                    self._session_connections[session_id] = set()
                self._session_connections[session_id].add(connection_id)

            if user_id:
                if user_id not in self._user_connections:
                    self._user_connections[user_id] = set()
                self._user_connections[user_id].add(connection_id)

            # 更新统计
            self._stats["total_connections"] += 1
            self._stats["active_connections"] += 1

            # 发送连接确认消息
            await self.send_to_connection(
                connection_id,
                Message(
                    type="connection_established",
                    data={
                        "connection_id": connection_id,
                        "session_id": session_id,
                        "user_id": user_id,
                        "server_time": datetime.utcnow().isoformat()
                    }
                )
            )

            self.logger.info(
                f"WebSocket connected: {connection_id} "
                f"(session: {session_id}, user: {user_id})"
            )

            return connection_id

        except Exception as e:
            self.logger.error(f"Failed to establish WebSocket connection: {e}")
            self._stats["errors"] += 1
            raise

    async def disconnect(self, connection_id: str, code: int = 1000) -> None:
        """断开WebSocket连接
        
        Args:
            connection_id: 连接ID
            code: 关闭代码
        """
        if connection_id not in self._connections:
            return

        connection_info = self._connections[connection_id]

        try:
            # 关闭WebSocket连接
            if connection_info.websocket.client_state == WebSocketState.CONNECTED:
                await connection_info.websocket.close(code=code)

        except Exception as e:
            self.logger.warning(f"Error closing WebSocket {connection_id}: {e}")

        finally:
            # 清理连接信息
            await self._cleanup_connection(connection_id)

    async def send_to_connection(
            self,
            connection_id: str,
            message: Message
    ) -> bool:
        """发送消息到指定连接
        
        Args:
            connection_id: 连接ID
            message: 消息对象
        
        Returns:
            是否发送成功
        """
        if connection_id not in self._connections:
            return False

        connection_info = self._connections[connection_id]

        try:
            if connection_info.websocket.client_state == WebSocketState.CONNECTED:
                await connection_info.websocket.send_text(
                    json.dumps(message.to_dict(), default=str)
                )
                self._stats["messages_sent"] += 1
                return True
            else:
                # 连接已断开，清理
                await self._cleanup_connection(connection_id)
                return False

        except Exception as e:
            self.logger.error(f"Failed to send message to {connection_id}: {e}")
            self._stats["errors"] += 1
            await self._cleanup_connection(connection_id)
            return False

    async def send_to_session(
            self,
            session_id: str,
            message: Message,
            exclude_connection: Optional[str] = None
    ) -> int:
        """发送消息到会话的所有连接
        
        Args:
            session_id: 会话ID
            message: 消息对象
            exclude_connection: 排除的连接ID
        
        Returns:
            成功发送的连接数
        """
        if session_id not in self._session_connections:
            return 0

        connection_ids = self._session_connections[session_id].copy()
        if exclude_connection:
            connection_ids.discard(exclude_connection)

        success_count = 0
        for connection_id in connection_ids:
            if await self.send_to_connection(connection_id, message):
                success_count += 1

        return success_count

    async def send_to_user(
            self,
            user_id: str,
            message: Message,
            exclude_connection: Optional[str] = None
    ) -> int:
        """发送消息到用户的所有连接
        
        Args:
            user_id: 用户ID
            message: 消息对象
            exclude_connection: 排除的连接ID
        
        Returns:
            成功发送的连接数
        """
        if user_id not in self._user_connections:
            return 0

        connection_ids = self._user_connections[user_id].copy()
        if exclude_connection:
            connection_ids.discard(exclude_connection)

        success_count = 0
        for connection_id in connection_ids:
            if await self.send_to_connection(connection_id, message):
                success_count += 1

        return success_count

    async def broadcast(
            self,
            message: Message,
            exclude_connections: Optional[Set[str]] = None
    ) -> int:
        """广播消息到所有连接
        
        Args:
            message: 消息对象
            exclude_connections: 排除的连接ID集合
        
        Returns:
            成功发送的连接数
        """
        connection_ids = set(self._connections.keys())
        if exclude_connections:
            connection_ids -= exclude_connections

        success_count = 0
        for connection_id in connection_ids:
            if await self.send_to_connection(connection_id, message):
                success_count += 1

        return success_count

    async def subscribe_to_channel(
            self,
            connection_id: str,
            channel: str
    ) -> bool:
        """订阅频道
        
        Args:
            connection_id: 连接ID
            channel: 频道名称
        
        Returns:
            是否订阅成功
        """
        if connection_id not in self._connections:
            return False

        # 添加到频道
        if channel not in self._channels:
            self._channels[channel] = set()
        self._channels[channel].add(connection_id)

        # 添加到连接的订阅列表
        self._connections[connection_id].subscriptions.add(channel)

        self.logger.debug(f"Connection {connection_id} subscribed to channel {channel}")
        return True

    async def unsubscribe_from_channel(
            self,
            connection_id: str,
            channel: str
    ) -> bool:
        """取消订阅频道
        
        Args:
            connection_id: 连接ID
            channel: 频道名称
        
        Returns:
            是否取消订阅成功
        """
        if connection_id not in self._connections:
            return False

        # 从频道移除
        if channel in self._channels:
            self._channels[channel].discard(connection_id)
            if not self._channels[channel]:
                del self._channels[channel]

        # 从连接的订阅列表移除
        self._connections[connection_id].subscriptions.discard(channel)

        self.logger.debug(f"Connection {connection_id} unsubscribed from channel {channel}")
        return True

    async def send_to_channel(
            self,
            channel: str,
            message: Message,
            exclude_connection: Optional[str] = None
    ) -> int:
        """发送消息到频道的所有订阅者
        
        Args:
            channel: 频道名称
            message: 消息对象
            exclude_connection: 排除的连接ID
        
        Returns:
            成功发送的连接数
        """
        if channel not in self._channels:
            return 0

        connection_ids = self._channels[channel].copy()
        if exclude_connection:
            connection_ids.discard(exclude_connection)

        success_count = 0
        for connection_id in connection_ids:
            if await self.send_to_connection(connection_id, message):
                success_count += 1

        return success_count

    async def handle_message(
            self,
            connection_id: str,
            raw_message: str
    ) -> None:
        """处理接收到的消息
        
        Args:
            connection_id: 连接ID
            raw_message: 原始消息
        """
        try:
            # 解析消息
            message_data = json.loads(raw_message)
            message_type = message_data.get("type")

            if not message_type:
                await self.send_to_connection(
                    connection_id,
                    Message(
                        type="error",
                        data={"error": "Missing message type"}
                    )
                )
                return

            # 更新统计
            self._stats["messages_received"] += 1

            # 更新连接的最后活动时间
            if connection_id in self._connections:
                self._connections[connection_id].last_ping = time.time()

            # 处理特殊消息类型
            if message_type == "ping":
                await self._handle_ping(connection_id, message_data)
            elif message_type == "subscribe":
                await self._handle_subscribe(connection_id, message_data)
            elif message_type == "unsubscribe":
                await self._handle_unsubscribe(connection_id, message_data)
            else:
                # 调用注册的消息处理器
                if message_type in self._message_handlers:
                    await self._message_handlers[message_type](
                        connection_id, message_data
                    )
                else:
                    # 未知消息类型
                    await self.send_to_connection(
                        connection_id,
                        Message(
                            type="error",
                            data={"error": f"Unknown message type: {message_type}"}
                        )
                    )

        except json.JSONDecodeError:
            await self.send_to_connection(
                connection_id,
                Message(
                    type="error",
                    data={"error": "Invalid JSON format"}
                )
            )
        except Exception as e:
            self.logger.error(f"Error handling message from {connection_id}: {e}")
            self._stats["errors"] += 1
            await self.send_to_connection(
                connection_id,
                Message(
                    type="error",
                    data={"error": "Internal server error"}
                )
            )

    def register_message_handler(
            self,
            message_type: str,
            handler: Callable[[str, Dict[str, Any]], None]
    ) -> None:
        """注册消息处理器
        
        Args:
            message_type: 消息类型
            handler: 处理器函数
        """
        self._message_handlers[message_type] = handler
        self.logger.debug(f"Registered handler for message type: {message_type}")

    def get_connection_info(self, connection_id: str) -> Optional[ConnectionInfo]:
        """获取连接信息
        
        Args:
            connection_id: 连接ID
        
        Returns:
            连接信息或None
        """
        return self._connections.get(connection_id)

    def get_session_connections(self, session_id: str) -> Set[str]:
        """获取会话的所有连接
        
        Args:
            session_id: 会话ID
        
        Returns:
            连接ID集合
        """
        return self._session_connections.get(session_id, set()).copy()

    def get_user_connections(self, user_id: str) -> Set[str]:
        """获取用户的所有连接
        
        Args:
            user_id: 用户ID
        
        Returns:
            连接ID集合
        """
        return self._user_connections.get(user_id, set()).copy()

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息
        
        Returns:
            统计信息字典
        """
        return {
            **self._stats,
            "channels": len(self._channels),
            "total_subscriptions": sum(len(subs) for subs in self._channels.values())
        }

    async def _handle_ping(self, connection_id: str, message_data: Dict[str, Any]) -> None:
        """处理ping消息"""
        await self.send_to_connection(
            connection_id,
            Message(
                type="pong",
                data={
                    "timestamp": time.time(),
                    "server_time": datetime.utcnow().isoformat()
                }
            )
        )

    async def _handle_subscribe(self, connection_id: str, message_data: Dict[str, Any]) -> None:
        """处理订阅消息"""
        channel = message_data.get("channel")
        if not channel:
            await self.send_to_connection(
                connection_id,
                Message(
                    type="error",
                    data={"error": "Missing channel name"}
                )
            )
            return

        success = await self.subscribe_to_channel(connection_id, channel)
        await self.send_to_connection(
            connection_id,
            Message(
                type="subscription_result",
                data={
                    "channel": channel,
                    "subscribed": success
                }
            )
        )

    async def _handle_unsubscribe(self, connection_id: str, message_data: Dict[str, Any]) -> None:
        """处理取消订阅消息"""
        channel = message_data.get("channel")
        if not channel:
            await self.send_to_connection(
                connection_id,
                Message(
                    type="error",
                    data={"error": "Missing channel name"}
                )
            )
            return

        success = await self.unsubscribe_from_channel(connection_id, channel)
        await self.send_to_connection(
            connection_id,
            Message(
                type="unsubscription_result",
                data={
                    "channel": channel,
                    "unsubscribed": success
                }
            )
        )

    async def _cleanup_connection(self, connection_id: str) -> None:
        """清理连接"""
        if connection_id not in self._connections:
            return

        connection_info = self._connections[connection_id]

        # 从会话连接中移除
        if connection_info.session_id:
            if connection_info.session_id in self._session_connections:
                self._session_connections[connection_info.session_id].discard(connection_id)
                if not self._session_connections[connection_info.session_id]:
                    del self._session_connections[connection_info.session_id]

        # 从用户连接中移除
        if connection_info.user_id:
            if connection_info.user_id in self._user_connections:
                self._user_connections[connection_info.user_id].discard(connection_id)
                if not self._user_connections[connection_info.user_id]:
                    del self._user_connections[connection_info.user_id]

        # 从频道订阅中移除
        for channel in connection_info.subscriptions.copy():
            await self.unsubscribe_from_channel(connection_id, channel)

        # 移除连接
        del self._connections[connection_id]

        # 更新统计
        self._stats["active_connections"] -= 1
        self._stats["disconnections"] += 1

        self.logger.info(f"Connection {connection_id} cleaned up")

    async def _close_all_connections(self) -> None:
        """关闭所有连接"""
        connection_ids = list(self._connections.keys())
        for connection_id in connection_ids:
            await self.disconnect(connection_id)

    async def _heartbeat_task(self) -> None:
        """心跳检测任务"""
        while self._running:
            try:
                current_time = time.time()
                expired_connections = []

                for connection_id, connection_info in self._connections.items():
                    # 检查连接是否超时
                    if current_time - connection_info.last_ping > self.heartbeat_timeout:
                        expired_connections.append(connection_id)

                # 清理超时连接
                for connection_id in expired_connections:
                    self.logger.info(f"Connection {connection_id} timed out")
                    await self.disconnect(connection_id, code=1001)

                await asyncio.sleep(self.heartbeat_interval)

            except Exception as e:
                self.logger.error(f"Error in heartbeat task: {e}")
                await asyncio.sleep(5)

    async def _message_processor(self) -> None:
        """消息处理任务"""
        while self._running:
            try:
                # 这里可以实现消息队列处理逻辑
                await asyncio.sleep(0.1)
            except Exception as e:
                self.logger.error(f"Error in message processor: {e}")
                await asyncio.sleep(1)

    async def _cleanup_task(self) -> None:
        """清理任务"""
        while self._running:
            try:
                # 定期清理空的频道
                empty_channels = [
                    channel for channel, connections in self._channels.items()
                    if not connections
                ]

                for channel in empty_channels:
                    del self._channels[channel]

                await asyncio.sleep(300)  # 每5分钟清理一次

            except Exception as e:
                self.logger.error(f"Error in cleanup task: {e}")
                await asyncio.sleep(60)


# 全局WebSocket管理器实例
websocket_manager = WebSocketManager()
