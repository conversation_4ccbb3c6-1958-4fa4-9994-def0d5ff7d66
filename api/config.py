# -*- coding: utf-8 -*-
"""
API服务配置 - 简化版本，使用统一配置管理

此文件保持向后兼容性，实际配置由 unified_config.py 管理
"""

from functools import lru_cache
from pathlib import Path
from typing import List, Dict, Any

from .config.unified_config import get_unified_config

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent


class APISettings:
    """
    API服务配置 - 代理到统一配置

    保持向后兼容性的包装器类
    """

    def __init__(self):
        self._config = get_unified_config()

    # 环境配置
    @property
    def ENVIRONMENT(self) -> str:
        return self._config.environment.environment

    @property
    def DEBUG(self) -> bool:
        return self._config.environment.debug

    @property
    def LOG_LEVEL(self) -> str:
        return self._config.environment.log_level

    @property
    def STRUCTURED_LOGS(self) -> bool:
        return self._config.environment.structured_logs

    # 服务器配置
    @property
    def API_HOST(self) -> str:
        return self._config.server.host

    @property
    def API_PORT(self) -> int:
        return self._config.server.port

    @property
    def API_WORKERS(self) -> int:
        return self._config.server.workers

    @property
    def API_RELOAD(self) -> bool:
        return self._config.server.reload

    @property
    def API_TIMEOUT(self) -> int:
        return self._config.server.timeout

    # 数据库配置
    @property
    def DATABASE_URL(self) -> str:
        return self._config.database.url

    @property
    def DATABASE_POOL_SIZE(self) -> int:
        return self._config.database.pool_size

    @property
    def DATABASE_MAX_OVERFLOW(self) -> int:
        return self._config.database.max_overflow

    @property
    def DATABASE_POOL_TIMEOUT(self) -> int:
        return self._config.database.pool_timeout

    @property
    def DATABASE_ECHO(self) -> bool:
        return self._config.database.echo

    # 安全配置
    @property
    def SECRET_KEY(self) -> str:
        return self._config.security.secret_key

    @property
    def API_KEY_HEADER(self) -> str:
        return self._config.security.api_key_header

    @property
    def ALLOWED_ORIGINS(self) -> List[str]:
        return self._config.security.allowed_origins

    @property
    def ALLOWED_HOSTS(self) -> List[str]:
        return self._config.security.allowed_hosts

    @property
    def RATE_LIMIT_ENABLED(self) -> bool:
        return self._config.security.rate_limit_enabled

    @property
    def RATE_LIMIT_DEFAULT(self) -> str:
        return self._config.security.rate_limit_default

    # 会话配置
    @property
    def DEFAULT_SESSION_TIMEOUT(self) -> int:
        return self._config.session.default_timeout

    @property
    def MAX_CONVERSATIONS_PER_SESSION(self) -> int:
        return self._config.session.max_conversations_per_session

    @property
    def MAX_HISTORY_LENGTH(self) -> int:
        return self._config.session.max_history_length

    # 缓存配置
    @property
    def CACHE_ENABLED(self) -> bool:
        return self._config.cache.enabled

    @property
    def CACHE_TTL(self) -> int:
        return self._config.cache.ttl

    # 文件存储配置
    @property
    def STORAGE_DIR(self) -> str:
        return self._config.storage.storage_dir

    @property
    def MAX_UPLOAD_SIZE(self) -> int:
        return self._config.storage.max_upload_size

    @property
    def ALLOWED_UPLOAD_TYPES(self) -> List[str]:
        return self._config.storage.allowed_upload_types

    # 监控配置
    @property
    def ENABLE_PERFORMANCE_MONITORING(self) -> bool:
        return self._config.monitoring.enabled

    @property
    def SLOW_REQUEST_THRESHOLD(self) -> float:
        return self._config.monitoring.slow_request_threshold

    # 向后兼容的方法

    def get_database_settings(self) -> Dict[str, Any]:
        """获取数据库设置"""
        return self._config.database.to_dict()

    def get_api_settings(self) -> Dict[str, Any]:
        """获取API设置"""
        return self._config.server.to_dict()

    def get_security_settings(self) -> Dict[str, Any]:
        """获取安全设置"""
        return self._config.security.to_dict()


# 简化的全局实例和函数
@lru_cache(maxsize=1)
def get_settings() -> APISettings:
    """获取全局设置实例（单例模式）"""
    return APISettings()


def create_required_directories() -> None:
    """创建必要的目录 - 委托给统一配置"""
    config = get_unified_config()
    config.create_directories(
        config.storage.storage_dir,
        config.logging.log_dir,
        str(PROJECT_ROOT / "data")
    )


# 向后兼容的全局实例
settings = get_settings()

# 初始化时创建目录
create_required_directories()
