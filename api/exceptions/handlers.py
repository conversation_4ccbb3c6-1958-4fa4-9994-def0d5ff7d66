"""统一异常处理系统

提供统一的异常处理、错误响应格式和错误日志记录。
"""

import traceback
from datetime import datetime
from typing import Dict, Any, Optional

from fastapi import Request, HTTPException, status
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from sqlalchemy.exc import SQLAlchemyError, IntegrityError, OperationalError

from services.base_service import ServiceError
from utils.logger import get_logger

logger = get_logger(__name__)


class APIError(Exception):
    """API异常基类"""

    def __init__(
            self,
            message: str,
            error_code: str = "API_ERROR",
            status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
            details: Optional[Dict[str, Any]] = None,
            cause: Optional[Exception] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        self.details = details or {}
        self.cause = cause
        self.timestamp = datetime.utcnow().isoformat()


class ValidationAPIError(APIError):
    """验证错误"""

    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            details=details
        )


class NotFoundAPIError(APIError):
    """资源未找到错误"""

    def __init__(self, resource: str, identifier: str = ""):
        message = f"{resource} not found"
        if identifier:
            message += f": {identifier}"

        super().__init__(
            message=message,
            error_code="NOT_FOUND",
            status_code=status.HTTP_404_NOT_FOUND,
            details={"resource": resource, "identifier": identifier}
        )


class ConflictAPIError(APIError):
    """冲突错误"""

    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="CONFLICT",
            status_code=status.HTTP_409_CONFLICT,
            details=details
        )


class AuthenticationAPIError(APIError):
    """认证错误"""

    def __init__(self, message: str = "Authentication required"):
        super().__init__(
            message=message,
            error_code="AUTHENTICATION_ERROR",
            status_code=status.HTTP_401_UNAUTHORIZED
        )


class AuthorizationAPIError(APIError):
    """授权错误"""

    def __init__(self, message: str = "Insufficient permissions"):
        super().__init__(
            message=message,
            error_code="AUTHORIZATION_ERROR",
            status_code=status.HTTP_403_FORBIDDEN
        )


class RateLimitAPIError(APIError):
    """速率限制错误"""

    def __init__(self, message: str = "Rate limit exceeded", retry_after: Optional[int] = None):
        details = {}
        if retry_after:
            details["retry_after"] = retry_after

        super().__init__(
            message=message,
            error_code="RATE_LIMIT_EXCEEDED",
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            details=details
        )


class ExternalServiceError(APIError):
    """外部服务错误"""

    def __init__(self, service: str, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=f"External service error ({service}): {message}",
            error_code="EXTERNAL_SERVICE_ERROR",
            status_code=status.HTTP_502_BAD_GATEWAY,
            details={"service": service, **(details or {})}
        )


class ErrorResponse:
    """错误响应格式化器"""

    @staticmethod
    def format_error(
            error_code: str,
            message: str,
            details: Optional[Dict[str, Any]] = None,
            request_id: Optional[str] = None,
            timestamp: Optional[str] = None
    ) -> Dict[str, Any]:
        """格式化错误响应
        
        Args:
            error_code: 错误代码
            message: 错误消息
            details: 错误详情
            request_id: 请求ID
            timestamp: 时间戳
            
        Returns:
            Dict[str, Any]: 格式化的错误响应
        """
        error_response = {
            "error": {
                "code": error_code,
                "message": message,
                "timestamp": timestamp or datetime.utcnow().isoformat()
            }
        }

        if details:
            error_response["error"]["details"] = details

        if request_id:
            error_response["error"]["request_id"] = request_id

        return error_response

    @staticmethod
    def from_api_error(error: APIError, request_id: Optional[str] = None) -> Dict[str, Any]:
        """从API错误创建响应
        
        Args:
            error: API错误
            request_id: 请求ID
            
        Returns:
            Dict[str, Any]: 错误响应
        """
        return ErrorResponse.format_error(
            error_code=error.error_code,
            message=error.message,
            details=error.details,
            request_id=request_id,
            timestamp=error.timestamp
        )


class ExceptionHandler:
    """异常处理器"""

    def __init__(self):
        self.logger = logger

    def get_request_id(self, request: Request) -> Optional[str]:
        """获取请求ID
        
        Args:
            request: FastAPI请求对象
            
        Returns:
            str: 请求ID
        """
        return getattr(request.state, "request_id", None)

    def log_error(
            self,
            error: Exception,
            request: Request,
            error_code: str = "UNKNOWN_ERROR",
            additional_context: Optional[Dict[str, Any]] = None
    ):
        """记录错误日志
        
        Args:
            error: 异常对象
            request: 请求对象
            error_code: 错误代码
            additional_context: 额外上下文信息
        """
        context = {
            "error_code": error_code,
            "error_type": type(error).__name__,
            "error_message": str(error),
            "request_method": request.method,
            "request_url": str(request.url),
            "request_id": self.get_request_id(request),
            "user_agent": request.headers.get("user-agent"),
            "client_ip": request.client.host if request.client else None
        }

        if additional_context:
            context.update(additional_context)

        # 记录错误日志
        if isinstance(error, (APIError, HTTPException)):
            self.logger.warning(f"API错误: {error_code}", extra=context)
        else:
            self.logger.error(
                f"未处理的异常: {error_code}",
                extra={
                    **context,
                    "traceback": traceback.format_exc()
                }
            )

    async def handle_api_error(self, request: Request, error: APIError) -> JSONResponse:
        """处理API错误
        
        Args:
            request: 请求对象
            error: API错误
            
        Returns:
            JSONResponse: 错误响应
        """
        request_id = self.get_request_id(request)

        self.log_error(error, request, error.error_code)

        response_data = ErrorResponse.from_api_error(error, request_id)

        return JSONResponse(
            status_code=error.status_code,
            content=response_data
        )

    async def handle_http_exception(self, request: Request, exc: HTTPException) -> JSONResponse:
        """处理HTTP异常
        
        Args:
            request: 请求对象
            exc: HTTP异常
            
        Returns:
            JSONResponse: 错误响应
        """
        request_id = self.get_request_id(request)

        self.log_error(exc, request, "HTTP_ERROR")

        response_data = ErrorResponse.format_error(
            error_code="HTTP_ERROR",
            message=exc.detail,
            request_id=request_id
        )

        return JSONResponse(
            status_code=exc.status_code,
            content=response_data
        )

    async def handle_validation_error(self, request: Request, exc: RequestValidationError) -> JSONResponse:
        """处理验证错误
        
        Args:
            request: 请求对象
            exc: 验证错误
            
        Returns:
            JSONResponse: 错误响应
        """
        request_id = self.get_request_id(request)

        # 格式化验证错误详情
        validation_details = []
        for error in exc.errors():
            validation_details.append({
                "field": ".".join(str(x) for x in error["loc"]),
                "message": error["msg"],
                "type": error["type"]
            })

        self.log_error(
            exc, request, "VALIDATION_ERROR",
            {"validation_errors": validation_details}
        )

        response_data = ErrorResponse.format_error(
            error_code="VALIDATION_ERROR",
            message="Request validation failed",
            details={"validation_errors": validation_details},
            request_id=request_id
        )

        return JSONResponse(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            content=response_data
        )

    async def handle_database_error(self, request: Request, exc: SQLAlchemyError) -> JSONResponse:
        """处理数据库错误
        
        Args:
            request: 请求对象
            exc: 数据库错误
            
        Returns:
            JSONResponse: 错误响应
        """
        request_id = self.get_request_id(request)

        # 根据具体的数据库错误类型确定错误代码和状态码
        if isinstance(exc, IntegrityError):
            error_code = "DATABASE_INTEGRITY_ERROR"
            status_code = status.HTTP_409_CONFLICT
            message = "Data integrity constraint violation"
        elif isinstance(exc, OperationalError):
            error_code = "DATABASE_OPERATIONAL_ERROR"
            status_code = status.HTTP_503_SERVICE_UNAVAILABLE
            message = "Database operation failed"
        else:
            error_code = "DATABASE_ERROR"
            status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
            message = "Database error occurred"

        self.log_error(exc, request, error_code)

        response_data = ErrorResponse.format_error(
            error_code=error_code,
            message=message,
            request_id=request_id
        )

        return JSONResponse(
            status_code=status_code,
            content=response_data
        )

    async def handle_service_error(self, request: Request, exc: ServiceError) -> JSONResponse:
        """处理服务层错误
        
        Args:
            request: 请求对象
            exc: 服务错误
            
        Returns:
            JSONResponse: 错误响应
        """
        request_id = self.get_request_id(request)

        self.log_error(exc, request, exc.error_code, exc.details)

        response_data = ErrorResponse.format_error(
            error_code=exc.error_code,
            message=exc.message,
            details=exc.details,
            request_id=request_id
        )

        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=response_data
        )

    async def handle_general_exception(self, request: Request, exc: Exception) -> JSONResponse:
        """处理通用异常
        
        Args:
            request: 请求对象
            exc: 异常
            
        Returns:
            JSONResponse: 错误响应
        """
        request_id = self.get_request_id(request)

        self.log_error(exc, request, "INTERNAL_ERROR")

        response_data = ErrorResponse.format_error(
            error_code="INTERNAL_ERROR",
            message="An internal server error occurred",
            request_id=request_id
        )

        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=response_data
        )


# 全局异常处理器实例
exception_handler = ExceptionHandler()


# FastAPI异常处理器函数
async def api_error_handler(request: Request, exc: APIError) -> JSONResponse:
    """API错误处理器"""
    return await exception_handler.handle_api_error(request, exc)


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """HTTP异常处理器"""
    return await exception_handler.handle_http_exception(request, exc)


async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """验证异常处理器"""
    return await exception_handler.handle_validation_error(request, exc)


async def database_exception_handler(request: Request, exc: SQLAlchemyError) -> JSONResponse:
    """数据库异常处理器"""
    return await exception_handler.handle_database_error(request, exc)


async def service_exception_handler(request: Request, exc: ServiceError) -> JSONResponse:
    """服务异常处理器"""
    return await exception_handler.handle_service_error(request, exc)


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """通用异常处理器"""
    return await exception_handler.handle_general_exception(request, exc)
