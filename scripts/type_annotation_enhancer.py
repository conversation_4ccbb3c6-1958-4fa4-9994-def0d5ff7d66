#!/usr/bin/env python3
"""类型注解增强工具"""

import argparse
import ast
import os
import sys
from dataclasses import dataclass
from pathlib import Path
from typing import List, Set, Optional, Union


@dataclass
class TypeAnnotationIssue:
    """类型注解问题"""
    file_path: str
    line_number: int
    function_name: str
    issue_type: str
    description: str
    suggestion: str


class TypeAnnotationAnalyzer(ast.NodeVisitor):
    """类型注解分析器"""
    
    def __init__(self, file_path: str):
        self.file_path = file_path
        self.issues: List[TypeAnnotationIssue] = []
        self.imports: Set[str] = set()
        self.current_class: Optional[str] = None
        
        # 常见类型映射
        self.type_mappings = {
            'str': 'str',
            'int': 'int',
            'float': 'float',
            'bool': 'bool',
            'list': 'List',
            'dict': 'Dict',
            'set': 'Set',
            'tuple': 'Tuple',
            'None': 'None',
        }
    
    def visit_Import(self, node: ast.Import):
        """访问import语句"""
        for alias in node.names:
            self.imports.add(alias.name)
        self.generic_visit(node)
    
    def visit_ImportFrom(self, node: ast.ImportFrom):
        """访问from import语句"""
        if node.module:
            for alias in node.names:
                self.imports.add(f"{node.module}.{alias.name}")
        self.generic_visit(node)
    
    def visit_ClassDef(self, node: ast.ClassDef):
        """访问类定义"""
        old_class = self.current_class
        self.current_class = node.name
        self.generic_visit(node)
        self.current_class = old_class
    
    def visit_FunctionDef(self, node: ast.FunctionDef):
        """访问函数定义"""
        self._analyze_function(node)
        self.generic_visit(node)
    
    def visit_AsyncFunctionDef(self, node: ast.AsyncFunctionDef):
        """访问异步函数定义"""
        self._analyze_function(node)
        self.generic_visit(node)
    
    def _analyze_function(self, node: Union[ast.FunctionDef, ast.AsyncFunctionDef]):
        """分析函数的类型注解"""
        function_name = node.name
        
        # 跳过特殊方法和私有方法的某些检查
        is_special = function_name.startswith('__') and function_name.endswith('__')
        is_private = function_name.startswith('_') and not is_special
        
        # 检查返回类型注解
        if not node.returns and not is_special:
            issue = TypeAnnotationIssue(
                file_path=self.file_path,
                line_number=node.lineno,
                function_name=function_name,
                issue_type="missing_return_annotation",
                description=f"函数 '{function_name}' 缺少返回类型注解",
                suggestion=self._suggest_return_type(node)
            )
            self.issues.append(issue)
        
        # 检查参数类型注解
        for arg in node.args.args:
            if arg.arg == 'self' or arg.arg == 'cls':
                continue
            
            if not arg.annotation:
                issue = TypeAnnotationIssue(
                    file_path=self.file_path,
                    line_number=node.lineno,
                    function_name=function_name,
                    issue_type="missing_parameter_annotation",
                    description=f"参数 '{arg.arg}' 缺少类型注解",
                    suggestion=self._suggest_parameter_type(arg.arg, node)
                )
                self.issues.append(issue)
        
        # 检查关键字参数
        for arg in node.args.kwonlyargs:
            if not arg.annotation:
                issue = TypeAnnotationIssue(
                    file_path=self.file_path,
                    line_number=node.lineno,
                    function_name=function_name,
                    issue_type="missing_parameter_annotation",
                    description=f"关键字参数 '{arg.arg}' 缺少类型注解",
                    suggestion=self._suggest_parameter_type(arg.arg, node)
                )
                self.issues.append(issue)
    
    def _suggest_return_type(self, node: Union[ast.FunctionDef, ast.AsyncFunctionDef]) -> str:
        """建议返回类型"""
        # 分析函数体中的return语句
        return_types = set()
        
        for child in ast.walk(node):
            if isinstance(child, ast.Return):
                if child.value is None:
                    return_types.add('None')
                elif isinstance(child.value, ast.Constant):
                    if isinstance(child.value.value, str):
                        return_types.add('str')
                    elif isinstance(child.value.value, int):
                        return_types.add('int')
                    elif isinstance(child.value.value, float):
                        return_types.add('float')
                    elif isinstance(child.value.value, bool):
                        return_types.add('bool')
                elif isinstance(child.value, ast.List):
                    return_types.add('List')
                elif isinstance(child.value, ast.Dict):
                    return_types.add('Dict')
                elif isinstance(child.value, ast.Set):
                    return_types.add('Set')
                elif isinstance(child.value, ast.Tuple):
                    return_types.add('Tuple')
        
        if not return_types:
            return "-> None  # 建议：如果函数不返回值"
        elif len(return_types) == 1:
            return_type = list(return_types)[0]
            if return_type in ['List', 'Dict', 'Set', 'Tuple']:
                return f"-> {return_type}[Any]  # 建议：请指定具体的元素类型"
            return f"-> {return_type}"
        else:
            types_str = ', '.join(sorted(return_types))
            return f"-> Union[{types_str}]  # 建议：函数返回多种类型"
    
    def _suggest_parameter_type(self, param_name: str, node: Union[ast.FunctionDef, ast.AsyncFunctionDef]) -> str:
        """建议参数类型"""
        # 基于参数名称的启发式建议
        name_lower = param_name.lower()
        
        if 'id' in name_lower or name_lower.endswith('_id'):
            return ": str  # 建议：ID通常是字符串类型"
        elif 'count' in name_lower or 'num' in name_lower or 'size' in name_lower:
            return ": int  # 建议：计数/数量通常是整数类型"
        elif 'rate' in name_lower or 'ratio' in name_lower or 'percent' in name_lower:
            return ": float  # 建议：比率/百分比通常是浮点数类型"
        elif 'enabled' in name_lower or 'disabled' in name_lower or name_lower.startswith('is_') or name_lower.startswith('has_'):
            return ": bool  # 建议：布尔标志"
        elif 'list' in name_lower or name_lower.endswith('s'):
            return ": List[Any]  # 建议：列表类型，请指定元素类型"
        elif 'dict' in name_lower or 'config' in name_lower or 'settings' in name_lower:
            return ": Dict[str, Any]  # 建议：字典类型"
        elif 'file' in name_lower or 'path' in name_lower:
            return ": str  # 建议：文件路径通常是字符串"
        else:
            return ": Any  # 建议：请根据实际用途指定具体类型"


class TypeAnnotationEnhancer:
    """类型注解增强器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.issues: List[TypeAnnotationIssue] = []
    
    def analyze_project(self, exclude_dirs: Optional[List[str]] = None) -> List[TypeAnnotationIssue]:
        """分析整个项目"""
        if exclude_dirs is None:
            exclude_dirs = ['__pycache__', '.git', '.pytest_cache', 'node_modules', 'venv', '.venv']
        
        python_files = self._find_python_files(exclude_dirs)
        
        for file_path in python_files:
            try:
                self._analyze_file(file_path)
            except Exception as e:
                print(f"分析文件 {file_path} 时出错: {e}")
        
        return self.issues
    
    def _find_python_files(self, exclude_dirs: List[str]) -> List[Path]:
        """查找Python文件"""
        python_files = []
        
        for root, dirs, files in os.walk(self.project_root):
            # 排除指定目录
            dirs[:] = [d for d in dirs if d not in exclude_dirs]
            
            for file in files:
                if file.endswith('.py'):
                    python_files.append(Path(root) / file)
        
        return python_files
    
    def _analyze_file(self, file_path: Path):
        """分析单个文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            analyzer = TypeAnnotationAnalyzer(str(file_path))
            analyzer.visit(tree)
            
            self.issues.extend(analyzer.issues)
            
        except SyntaxError as e:
            print(f"语法错误在文件 {file_path}: {e}")
        except Exception as e:
            print(f"分析文件 {file_path} 时出错: {e}")
    
    def generate_report(self, output_file: Optional[str] = None) -> str:
        """生成报告"""
        report_lines = []
        report_lines.append("# 类型注解分析报告\n")
        report_lines.append(f"总共发现 {len(self.issues)} 个类型注解问题\n")
        
        # 按文件分组
        issues_by_file = {}
        for issue in self.issues:
            if issue.file_path not in issues_by_file:
                issues_by_file[issue.file_path] = []
            issues_by_file[issue.file_path].append(issue)
        
        # 统计信息
        issue_types = {}
        for issue in self.issues:
            issue_types[issue.issue_type] = issue_types.get(issue.issue_type, 0) + 1
        
        report_lines.append("## 问题类型统计\n")
        for issue_type, count in sorted(issue_types.items()):
            type_name = {
                'missing_return_annotation': '缺少返回类型注解',
                'missing_parameter_annotation': '缺少参数类型注解'
            }.get(issue_type, issue_type)
            report_lines.append(f"- {type_name}: {count}\n")
        
        report_lines.append("\n## 详细问题列表\n")
        
        for file_path in sorted(issues_by_file.keys()):
            relative_path = os.path.relpath(file_path, self.project_root)
            report_lines.append(f"### {relative_path}\n")
            
            for issue in sorted(issues_by_file[file_path], key=lambda x: x.line_number):
                report_lines.append(f"**行 {issue.line_number}**: {issue.description}\n")
                report_lines.append(f"建议: `{issue.suggestion}`\n\n")
        
        report_content = ''.join(report_lines)
        
        if output_file:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            print(f"报告已保存到: {output_file}")
        
        return report_content
    
    def generate_typing_imports_suggestion(self) -> str:
        """生成typing导入建议"""
        needed_imports = set()
        
        for issue in self.issues:
            suggestion = issue.suggestion
            if 'List' in suggestion:
                needed_imports.add('List')
            if 'Dict' in suggestion:
                needed_imports.add('Dict')
            if 'Set' in suggestion:
                needed_imports.add('Set')
            if 'Tuple' in suggestion:
                needed_imports.add('Tuple')
            if 'Union' in suggestion:
                needed_imports.add('Union')
            if 'Optional' in suggestion:
                needed_imports.add('Optional')
            if 'Any' in suggestion:
                needed_imports.add('Any')
        
        if needed_imports:
            imports_str = ', '.join(sorted(needed_imports))
            return f"from typing import {imports_str}"
        
        return ""


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Python类型注解增强工具')
    parser.add_argument('project_path', help='项目根目录路径')
    parser.add_argument('--output', '-o', help='输出报告文件路径')
    parser.add_argument('--exclude', nargs='*', default=['__pycache__', '.git', '.pytest_cache', 'venv', '.venv'],
                       help='要排除的目录')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.project_path):
        print(f"错误: 项目路径 '{args.project_path}' 不存在")
        sys.exit(1)
    
    enhancer = TypeAnnotationEnhancer(args.project_path)
    issues = enhancer.analyze_project(exclude_dirs=args.exclude)
    
    if not issues:
        print("恭喜！没有发现类型注解问题。")
        return
    
    # 生成报告
    output_file = args.output or os.path.join(args.project_path, 'type_annotation_report.md')
    report = enhancer.generate_report(output_file)
    
    # 生成导入建议
    import_suggestion = enhancer.generate_typing_imports_suggestion()
    if import_suggestion:
        print(f"\n建议添加的typing导入:\n{import_suggestion}\n")
    
    print(f"\n分析完成！发现 {len(issues)} 个类型注解问题。")
    print(f"详细报告已保存到: {output_file}")


if __name__ == '__main__':
    main()