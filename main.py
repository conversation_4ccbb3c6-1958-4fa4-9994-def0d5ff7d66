import argparse
import sys
from pathlib import Path

from colorama import Fore, Style, init
from tabulate import tabulate

# 初始化colorama
init(autoreset=True)

# 将src添加到路径中以允许绝对导入
sys.path.insert(0, './src')

from src.utils.config_optimized import OptimizedConfigManager
from src.utils.loguru_config import init_default_logging, get_logger, init_structured_logging
from src.utils.validation import validate_all_configs, ValidationError
from src.utils.performance import get_performance_monitor
from src.core.model_manager import ModelManager
from src.core.conversation_manager import ConversationManager
from src.core.prompt_manager import PromptManager
from src.core.comparison_engine import ComparisonEngine

# 日志和性能监控的全局变量
logger = None
app_logger = None
performance_monitor = None

def initialize_logging(use_structured: bool = False):
    """初始化日志系统。"""
    global logger, app_logger
    if use_structured:
        logger = init_structured_logging()
        from src.utils.loguru_config import get_structured_logger
        app_logger = get_structured_logger(__name__)
    else:
        logger = init_default_logging()
        app_logger = get_logger(__name__)

def initialize_performance_monitoring():
    """初始化性能监控。"""
    global performance_monitor
    performance_monitor = get_performance_monitor()

class InteractiveSession:
    """
    处理比较工具的交互式命令行会话。
    """

    def __init__(self):
        app_logger.info("正在初始化应用程序...")
        
        # 在继续之前验证配置
        self._validate_configuration()
        
        self.config_manager = OptimizedConfigManager(config_dir="config", config_file="models_optimized.yaml")
        self.model_manager = ModelManager(self.config_manager)
        self.conv_manager = ConversationManager(self.config_manager)
        self.prompt_manager = PromptManager(prompts_dir="prompts")
        self.engine = ComparisonEngine(self.model_manager, self.conv_manager, self.prompt_manager)
        self.running = True
        
        app_logger.info("应用程序初始化成功")
    
    def _validate_configuration(self):
        """在启动前验证所有配置文件。"""
        app_logger.info("正在验证配置文件...")
        
        config_dir = Path("config")
        prompts_dir = Path("prompts")
        
        if not config_dir.exists():
            app_logger.error(f"未找到配置目录: {config_dir}")
            raise ValidationError(f"Configuration directory not found: {config_dir}")
        
        if not prompts_dir.exists():
            app_logger.error(f"未找到提示文件目录: {prompts_dir}")
            raise ValidationError(f"Prompts directory not found: {prompts_dir}")
        
        # 验证所有配置，明确传递提示文件目录
        validation_results = validate_all_configs(config_dir, prompts_dir)
        
        if not validation_results['valid']:
            app_logger.error("配置验证失败:")
            for error in validation_results['errors']:
                app_logger.error(f"  - {error}")
            
            print(Fore.RED + "配置验证失败:")
            for error in validation_results['errors']:
                print(Fore.RED + f"  - {error}")
            
            # 特别检查是否有启用的模型没有对应的配置
            if validation_results.get('missing_model_configs'):
                missing_models = validation_results['missing_model_configs']
                error_msg = f"错误: 以下启用的模型没有对应的配置: {', '.join(missing_models)}。请检查models.yaml文件或禁用这些模型。"
                app_logger.error(error_msg)
                print(Fore.RED + Style.BRIGHT + error_msg)
            
            raise ValidationError("配置验证失败。请修复上述错误。")
        
        # 记录警告（如果有）
        if validation_results['warnings']:
            app_logger.warning("配置警告:")
            for warning in validation_results['warnings']:
                app_logger.warning(f"  - {warning}")
            
            print(Fore.YELLOW + "配置警告:")
            for warning in validation_results['warnings']:
                print(Fore.YELLOW + f"  - {warning}")
        
        app_logger.info("配置验证成功完成")

    def start(self):
        """启动交互式会话。"""
        print(Fore.CYAN + Style.BRIGHT + "=== LLM Model Comparison Tool ===")
        self.show_active_models()
        
        while self.running:
            try:
                command = input(Fore.YELLOW + "\n输入提示或命令 (e.g., /help): ").strip()
                if not command:
                    continue
                self.handle_command(command)
            except KeyboardInterrupt:
                print("\n正在退出...")
                self.running = False
            except Exception as e:
                app_logger.error(f"发生意外错误: {e}", exc_info=True)
                print(Fore.RED + "发生意外错误。请查看日志了解详情。")
        
        self.conv_manager.save_all_conversations()
        
        # 在退出前显示性能报告
        print(Fore.CYAN + "\n=== 性能报告 ===")
        performance_report = performance_monitor.get_performance_report()
        print(performance_report)
        
        print(Fore.CYAN + "再见！")

    def handle_command(self, command: str):
        """处理用户输入，区分命令和提示。"""
        if command.startswith('/'):
            self.handle_system_command(command)
        else:
            self.handle_chat(command)

    def handle_system_command(self, command: str):
        """处理以'/'开头的系统命令。"""
        parts = command[1:].lower().split()
        cmd = parts[0]
        args = parts[1:]

        if cmd == "models":
            self.show_active_models()
        elif cmd == "enable" and args:
            self.model_manager.enable_model(args[0])
        elif cmd == "disable" and args:
            self.model_manager.disable_model(args[0])
        elif cmd == "switch" and len(args) > 1:
            self.model_manager.switch_model_version(args[0], args[1])
        elif cmd == "clear" and args:
            self.conv_manager.clear_conversation(args[0])
        elif cmd == "reload":
            self.model_manager.load_active_models()
            print("配置已重新加载。")
        elif cmd == "stats":
            self.show_performance_stats()
        elif cmd == "detailed":
            self.handle_detailed_command(args)
        elif cmd == "export":
            self.handle_export_command(args)
        elif cmd == "help":
            self.show_help()
        elif cmd == "exit" or cmd == "quit":
            self.running = False
        else:
            print(Fore.RED + f"未知命令: {command}。输入 /help 查看可用命令。")

    def handle_chat(self, user_input: str):
        """处理聊天提示，将其发送到比较引擎。"""
        print(Fore.BLUE + "\n正在查询模型...")
        results = self.engine.compare_models(user_input)
        self.display_results(results)

    def show_active_models(self):
        """显示当前活动模型的表格。"""
        models = self.model_manager.get_active_models_config()
        headers = [Fore.GREEN + "Name", Fore.GREEN + "Model Version", Fore.GREEN + "Status"]
        table = []
        for model in models:
            status = Fore.GREEN + "Enabled" if model['enabled'] else Fore.RED + "Disabled"
            table.append([model['name'], model['model'], status])
        
        print("\n--- 活动模型 ---")
        print(tabulate(table, headers=headers, tablefmt="pretty"))

    def show_performance_stats(self):
        """显示当前性能统计信息。"""
        print(Fore.CYAN + "\n=== 性能统计 ===")
        
        # 总体统计
        overall_stats = performance_monitor.get_overall_stats()
        print(f"\nAPI调用总数: {overall_stats.total_calls}")
        print(f"成功率: {overall_stats.success_rate:.2%}")
        print(f"平均响应时间: {overall_stats.avg_response_time:.2f}秒")
        print(f"总请求大小: {overall_stats.total_request_size} 字节")
        print(f"总响应大小: {overall_stats.total_response_size} 字节")
        
        # 按提供商统计
        provider_stats = performance_monitor.get_stats_by_provider()
        if provider_stats:
            print(Fore.YELLOW + "\n--- 按提供商统计 ---")
            for provider, stats in provider_stats.items():
                print(f"\n{provider}:")
                print(f"  调用次数: {stats.total_calls}")
                print(f"  成功率: {stats.success_rate:.2%}")
                print(f"  平均响应时间: {stats.avg_response_time:.2f}秒")
        
        # 错误摘要
        error_summary = performance_monitor.get_error_summary()
        if error_summary:
            print(Fore.RED + "\n--- 错误摘要 ---")
            for error_type, count in error_summary.items():
                print(f"  {error_type}: {count}")
    
    def handle_detailed_command(self, args: list):
        """处理详细对话相关命令。"""
        if not args:
            print(Fore.YELLOW + "详细对话命令用法:")
            print("  /detailed list                    - 列出所有有详细对话记录的模型")
            print("  /detailed show <model_name>       - 显示指定模型的详细对话历史")
            print("  /detailed stats [model_name]      - 显示对话统计信息")
            print("  /detailed clear <model_name>      - 清除指定模型的详细对话历史")
            return
        
        subcmd = args[0].lower()
        
        if subcmd == 'list':
            models_with_detailed = list(self.conv_manager.detailed_conversations.keys())
            if models_with_detailed:
                print(Fore.GREEN + "\n有详细对话记录的模型:")
                for model in models_with_detailed:
                    count = len(self.conv_manager.get_detailed_history(model))
                    print(f"  - {model}: {count} 条对话记录")
            else:
                print(Fore.YELLOW + "暂无详细对话记录。")
        
        elif subcmd == 'show':
            if len(args) < 2:
                print(Fore.RED + "请指定模型名称: /detailed show <model_name>")
                return
            
            model_name = args[1]
            history = self.conv_manager.get_detailed_history(model_name, limit=5)  # 显示最近5条
            
            if not history:
                print(Fore.YELLOW + f"模型 {model_name} 暂无详细对话记录。")
                return
            
            print(Fore.CYAN + f"\n=== {model_name} 详细对话历史（最近5条）===\n")
            for entry in history:
                print(f"对话ID: {entry['conversation_id']}")
                print(f"时间: {entry['timestamp']}")
                print(f"用户输入: {entry['user_input']}")
                print(f"使用的提示词: {entry['prompt_used'] or '无'}")
                
                if model_name in entry['model_responses']:
                    response_data = entry['model_responses'][model_name]
                    print(f"模型响应: {response_data['response'][:200]}{'...' if len(response_data['response']) > 200 else ''}")
                    print(f"状态: {response_data['status']}")
                
                print("-" * 50)
        
        elif subcmd == 'stats':
            if len(args) >= 2:
                model_name = args[1]
                stats = self.conv_manager.get_conversation_statistics(model_name)
                if stats:
                    print(Fore.CYAN + f"\n=== {model_name} 对话统计 ===")
                    print(f"总对话数: {stats['total_conversations']}")
                    print(f"日期范围: {stats['date_range']['start']} 到 {stats['date_range']['end']}")
                    if stats['average_response_time']:
                        print(f"平均响应时间: {stats['average_response_time']:.2f}秒")
                else:
                    print(Fore.YELLOW + f"模型 {model_name} 暂无统计数据。")
            else:
                all_stats = self.conv_manager.get_conversation_statistics()
                if all_stats:
                    print(Fore.CYAN + "\n=== 所有模型对话统计 ===")
                    for model, stats in all_stats.items():
                        print(f"\n{model}:")
                        print(f"  总对话数: {stats['total_conversations']}")
                        if stats['date_range']['start']:
                            print(f"  日期范围: {stats['date_range']['start']} 到 {stats['date_range']['end']}")
                        if stats['average_response_time']:
                            print(f"  平均响应时间: {stats['average_response_time']:.2f}秒")
                else:
                    print(Fore.YELLOW + "暂无对话统计数据。")
        
        elif subcmd == 'clear':
            if len(args) < 2:
                print(Fore.RED + "请指定模型名称: /detailed clear <model_name>")
                return
            
            model_name = args[1]
            if model_name in self.conv_manager.detailed_conversations:
                self.conv_manager.detailed_conversations[model_name] = []
                self.conv_manager.save_detailed_conversation(model_name)
                print(Fore.GREEN + f"已清除 {model_name} 的详细对话历史。")
            else:
                print(Fore.YELLOW + f"模型 {model_name} 暂无详细对话记录。")
        
        else:
            print(Fore.RED + f"未知的详细对话子命令: {subcmd}")
    
    def handle_export_command(self, args: list):
        """处理导出命令。"""
        if len(args) < 1:
            print(Fore.YELLOW + "导出命令用法:")
            print("  /export <model_name> [format] [output_path]")
            print("  格式选项: json, csv, txt (默认: json)")
            print("  示例: /export gemini-2.0-flash json my_export.json")
            return
        
        model_name = args[0]
        format_type = args[1] if len(args) > 1 else "json"
        output_path = args[2] if len(args) > 2 else None
        
        if format_type not in ["json", "csv", "txt"]:
            print(Fore.RED + "不支持的格式。支持的格式: json, csv, txt")
            return
        
        try:
            exported_path = self.conv_manager.export_conversations(model_name, format_type, output_path)
            print(Fore.GREEN + f"对话历史已成功导出到: {exported_path}")
        except Exception as e:
            print(Fore.RED + f"导出失败: {e}")
            logger.error(f"导出对话历史时出错: {e}", exc_info=True)
    
    def show_help(self):
        """显示帮助信息。"""
        print(Fore.YELLOW + """
可用命令:
  /models          - 显示当前活动的模型。
  /enable <name>   - 启用模型（例如，/enable doubao）。
  /disable <name>  - 禁用模型（例如，/disable gemini）。
  /switch <name> <version> - 切换模型版本。
  /clear <name>    - 清除模型的对话历史。
  /reload          - 重新加载活动模型配置。
  /stats           - 显示性能统计信息。
  /detailed        - 详细对话管理命令（输入 /detailed 查看子命令）。
  /export          - 导出对话历史（输入 /export 查看用法）。
  /help            - 显示此帮助信息。
  /exit or /quit   - 退出应用程序。
  
任何其他文本将被视为对模型的提示。
        """)

    def display_results(self, results: dict):
        """以格式化表格显示比较结果。"""
        print("\n--- 比较结果 ---")
        for model_name, result in results.items():
            # 根据状态设置不同的颜色
            if result['status'] == 'success':
                color = Fore.GREEN
                status_text = "成功"
            elif result['status'] == 'config_error':
                color = Fore.YELLOW
                status_text = "配置错误"
            else:
                color = Fore.RED
                status_text = "调用错误"
                
            print(color + Style.BRIGHT + f"\n--- {model_name} ({result['model_version']}) [状态: {status_text}] ---")
            print(Style.RESET_ALL + result['response'])

def main():
    parser = argparse.ArgumentParser(
        description="LLM模型比较工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  python main.py                    # 使用标准日志运行
  python main.py --structured-logs  # 使用结构化JSON日志运行
  python main.py --help            # 显示此帮助信息
        """
    )
    
    parser.add_argument(
        '--structured-logs',
        action='store_true',
        help='启用结构化JSON日志以便更好地进行日志分析'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
        default='INFO',
        help='设置日志级别（默认：INFO）'
    )
    
    args = parser.parse_args()
    
    # 初始化日志和性能监控
    initialize_logging(use_structured=args.structured_logs)
    initialize_performance_monitoring()
    
    # 日志级别现在由loguru配置处理
    
    try:
        session = InteractiveSession()
        session.start()
    except KeyboardInterrupt:
        print("\n应用程序被用户中断")
        if app_logger:
            app_logger.info("应用程序被用户中断")
    except Exception as e:
        print(f"\n致命错误: {e}")
        if app_logger:
            app_logger.critical(f"致命错误: {e}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main()
