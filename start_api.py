#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API服务启动脚本
"""

import argparse
import os
import sys
from pathlib import Path

import uvicorn

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# from src.utils.loguru_config import setup_loguru
from utils.logger import logger
from database.init_db import init_database, check_database

# 设置日志
logger.info("日志系统已初始化")


def setup_environment():
    """
    设置环境变量
    """
    # 设置默认环境变量
    os.environ.setdefault("ENVIRONMENT", "development")
    os.environ.setdefault("LOG_LEVEL", "INFO")
    os.environ.setdefault("DATABASE_URL", "sqlite:///./data/llm_comparison.db")
    os.environ.setdefault("API_HOST", "0.0.0.0")
    os.environ.setdefault("API_PORT", "8000")
    os.environ.setdefault("API_WORKERS", "1")
    
    # 从.env文件加载环境变量（如果存在）
    env_file = project_root / ".env"
    if env_file.exists():
        try:
            from dotenv import load_dotenv
            load_dotenv(env_file)
            logger.info(f"Loaded environment variables from: {env_file}")
        except ImportError:
            logger.warning("python-dotenv not installed, skipping .env file")


def check_dependencies():
    """
    检查依赖项
    
    Returns:
        bool: 依赖项是否满足
    """
    try:
        import fastapi
        import uvicorn
        import sqlalchemy
        import pydantic
        logger.info("All required dependencies are available")
        return True
    except ImportError as e:
        logger.error(f"Missing required dependency: {e}")
        logger.error("Please install dependencies: pip install -r requirements-api.txt")
        return False


def prepare_database(force_init: bool = False):
    """
    准备数据库
    
    Args:
        force_init: 是否强制初始化数据库
        
    Returns:
        bool: 数据库是否准备就绪
    """
    try:
        # 检查数据库是否存在和可用
        if not force_init and check_database():
            logger.info("Database is ready")
            return True
        
        # 初始化数据库
        logger.info("Initializing database...")
        if init_database(force=force_init):
            logger.info("Database initialized successfully")
            return True
        else:
            logger.error("Failed to initialize database")
            return False
            
    except Exception as e:
        logger.error(f"Database preparation failed: {e}")
        return False


def start_api_server(
    host: str = "0.0.0.0",
    port: int = 8000,
    workers: int = 1,
    reload: bool = False,
    log_level: str = "info"
):
    """
    启动API服务器
    
    Args:
        host: 服务器主机地址
        port: 服务器端口
        workers: 工作进程数
        reload: 是否启用自动重载
        log_level: 日志级别
    """
    try:
        logger.info(f"Starting API server on {host}:{port}")
        logger.info(f"Workers: {workers}, Reload: {reload}, Log Level: {log_level}")
        
        # 配置uvicorn
        config = {
            "app": "api.main:app",
            "host": host,
            "port": port,
            "log_level": log_level.lower(),
            "access_log": True,
        }
        
        if reload:
            config["reload"] = True
            config["reload_dirs"] = [str(project_root)]
        else:
            config["workers"] = workers
        
        # 启动服务器
        uvicorn.run(**config)
        
    except KeyboardInterrupt:
        logger.info("API server stopped by user")
    except Exception as e:
        logger.error(f"Failed to start API server: {e}")
        sys.exit(1)


def show_startup_info():
    """
    显示启动信息
    """
    host = os.getenv("API_HOST", "0.0.0.0")
    port = os.getenv("API_PORT", "8000")
    environment = os.getenv("ENVIRONMENT", "development")
    
    print("\n" + "="*60)
    print("🚀 LLM Comparison Tool API Server")
    print("="*60)
    print(f"Environment: {environment}")
    print(f"Server URL: http://{host}:{port}")
    print(f"API Documentation: http://{host}:{port}/docs")
    print(f"Alternative Docs: http://{host}:{port}/redoc")
    print(f"Health Check: http://{host}:{port}/health")
    
    # 显示开发API密钥信息
    dev_key_file = project_root / "dev_api_key.txt"
    if dev_key_file.exists() and environment == "development":
        print(f"\n📋 Development API Key: {dev_key_file}")
        try:
            with open(dev_key_file, "r") as f:
                lines = f.readlines()
                for line in lines:
                    if line.strip():
                        print(f"   {line.strip()}")
        except Exception:
            pass
    
    print("\n💡 Tips:")
    print("   - Use Ctrl+C to stop the server")
    print("   - Check logs for detailed information")
    print("   - Visit /docs for interactive API documentation")
    print("="*60 + "\n")


def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description="LLM Comparison Tool API Server")
    parser.add_argument(
        "--host",
        default=None,
        help="Server host address (default: from environment or 0.0.0.0)"
    )
    parser.add_argument(
        "--port",
        type=int,
        default=None,
        help="Server port (default: from environment or 8000)"
    )
    parser.add_argument(
        "--workers",
        type=int,
        default=None,
        help="Number of worker processes (default: from environment or 1)"
    )
    parser.add_argument(
        "--reload",
        action="store_true",
        help="Enable auto-reload for development"
    )
    parser.add_argument(
        "--log-level",
        choices=["debug", "info", "warning", "error"],
        default=None,
        help="Log level (default: from environment or info)"
    )
    parser.add_argument(
        "--init-db",
        action="store_true",
        help="Force database initialization before starting"
    )
    parser.add_argument(
        "--check-only",
        action="store_true",
        help="Only check dependencies and database, don't start server"
    )
    
    args = parser.parse_args()
    
    # 设置环境
    setup_environment()
    
    # 从环境变量或参数获取配置
    host = args.host or os.getenv("API_HOST", "0.0.0.0")
    port = args.port or int(os.getenv("API_PORT", "8000"))
    workers = args.workers or int(os.getenv("API_WORKERS", "1"))
    log_level = args.log_level or os.getenv("LOG_LEVEL", "info")
    
    # 检查依赖项
    logger.info("Checking dependencies...")
    if not check_dependencies():
        sys.exit(1)
    
    # 准备数据库
    logger.info("Preparing database...")
    if not prepare_database(force_init=args.init_db):
        sys.exit(1)
    
    # 如果只是检查，则退出
    if args.check_only:
        logger.info("All checks passed. Server is ready to start.")
        sys.exit(0)
    
    # 显示启动信息
    show_startup_info()
    
    # 启动API服务器
    start_api_server(
        host=host,
        port=port,
        workers=workers,
        reload=args.reload,
        log_level=log_level
    )


if __name__ == "__main__":
    main()