#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库初始化脚本
"""

import argparse
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from database.connection import DatabaseManager
from database.migrations.initial_schema import upgrade, downgrade
from loguru import logger

# 设置日志
logger.info("数据库日志系统已初始化")


def init_database(db_path: str = None, force: bool = False):
    """
    初始化数据库
    
    Args:
        db_path: 数据库文件路径
        force: 是否强制重新创建
    """
    try:
        # 使用指定路径或默认路径
        if db_path:
            os.environ["DATABASE_URL"] = f"sqlite:///{db_path}"
        
        # 初始化数据库管理器
        db_manager = DatabaseManager()
        
        # 检查数据库是否已存在
        db_file = db_manager.get_db_file_path()
        if db_file and os.path.exists(db_file) and not force:
            logger.info(f"Database already exists at: {db_file}")
            response = input("Database already exists. Do you want to recreate it? (y/N): ")
            if response.lower() != 'y':
                logger.info("Database initialization cancelled")
                return False
        
        # 删除现有数据库（如果强制重新创建）
        if force and db_file and os.path.exists(db_file):
            os.remove(db_file)
            logger.info(f"Removed existing database: {db_file}")
        
        # 创建数据库目录
        if db_file:
            os.makedirs(os.path.dirname(db_file), exist_ok=True)
        
        # 初始化数据库
        logger.info("Initializing database...")
        from database.connection import init_database as init_db_func
        import asyncio
        asyncio.run(init_db_func())
        
        # 运行迁移
        logger.info("Running database migrations...")
        engine = db_manager.get_engine()
        upgrade(engine)
        
        logger.info("Database initialized successfully")
        
        # 显示数据库信息
        if db_file:
            logger.info(f"Database location: {db_file}")
        
        # 检查开发API密钥
        dev_key_file = project_root / "dev_api_key.txt"
        if dev_key_file.exists():
            logger.info(f"Development API key saved to: {dev_key_file}")
            logger.info("Please use this key for API authentication during development")
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        return False


def reset_database(db_path: str = None):
    """
    重置数据库
    
    Args:
        db_path: 数据库文件路径
    """
    try:
        # 使用指定路径或默认路径
        if db_path:
            os.environ["DATABASE_URL"] = f"sqlite:///{db_path}"
        
        # 初始化数据库管理器
        db_manager = DatabaseManager()
        
        logger.info("Resetting database...")
        
        # 运行降级迁移
        engine = db_manager.get_engine()
        downgrade(engine)
        
        # 重新运行升级迁移
        upgrade(engine)
        
        logger.info("Database reset successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to reset database: {e}")
        return False


def backup_database(db_path: str = None, backup_path: str = None):
    """
    备份数据库
    
    Args:
        db_path: 数据库文件路径
        backup_path: 备份文件路径
    """
    try:
        # 使用指定路径或默认路径
        if db_path:
            os.environ["DATABASE_URL"] = f"sqlite:///{db_path}"
        
        # 初始化数据库管理器
        db_manager = DatabaseManager()
        
        # 执行备份
        if backup_path:
            result = db_manager.backup_database(backup_path)
        else:
            result = db_manager.backup_database()
        
        if result:
            logger.info(f"Database backed up to: {result}")
            return True
        else:
            logger.error("Failed to backup database")
            return False
        
    except Exception as e:
        logger.error(f"Failed to backup database: {e}")
        return False


def restore_database(backup_path: str, db_path: str = None):
    """
    恢复数据库
    
    Args:
        backup_path: 备份文件路径
        db_path: 数据库文件路径
    """
    try:
        # 使用指定路径或默认路径
        if db_path:
            os.environ["DATABASE_URL"] = f"sqlite:///{db_path}"
        
        # 初始化数据库管理器
        db_manager = DatabaseManager()
        
        # 执行恢复
        result = db_manager.restore_database(backup_path)
        
        if result:
            logger.info(f"Database restored from: {backup_path}")
            return True
        else:
            logger.error("Failed to restore database")
            return False
        
    except Exception as e:
        logger.error(f"Failed to restore database: {e}")
        return False


def check_database(db_path: str = None):
    """
    检查数据库状态
    
    Args:
        db_path: 数据库文件路径
    """
    try:
        # 使用指定路径或默认路径
        if db_path:
            os.environ["DATABASE_URL"] = f"sqlite:///{db_path}"
        
        # 初始化数据库管理器
        db_manager = DatabaseManager()
        
        # 检查数据库连接
        logger.info("Checking database connection...")
        session_gen = db_manager.get_session()
        session = next(session_gen)
        try:
            from sqlalchemy import text
            result = session.execute(text("SELECT 1")).fetchone()
            if result:
                logger.info("Database connection: OK")
            else:
                logger.error("Database connection: FAILED")
                return False
        finally:
            session.close()
        
        # 检查表结构
        logger.info("Checking database tables...")
        engine = db_manager.get_engine()
        
        tables = [
            "sessions", "conversations", "prompts", "api_keys"
        ]
        
        for table in tables:
            try:
                with engine.connect() as conn:
                    result = conn.execute(text(f"SELECT COUNT(*) FROM {table}")).fetchone()
                    count = result[0] if result else 0
                    logger.info(f"Table '{table}': {count} records")
            except Exception as e:
                logger.error(f"Table '{table}': ERROR - {e}")
                return False
        
        # 检查索引
        logger.info("Checking database indexes...")
        try:
            with engine.connect() as conn:
                indexes = conn.execute(text("""
                    SELECT name FROM sqlite_master 
                    WHERE type='index' AND name LIKE 'idx_%'
                """)).fetchall()
            
            logger.info(f"Found {len(indexes)} custom indexes")
            for idx in indexes:
                logger.info(f"  - {idx[0]}")
        except Exception as e:
            logger.warning(f"Could not check indexes: {e}")
        
        logger.info("Database check completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Database check failed: {e}")
        return False


def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description="Database management script")
    parser.add_argument(
        "action",
        choices=["init", "reset", "backup", "restore", "check"],
        help="Action to perform"
    )
    parser.add_argument(
        "--db-path",
        help="Database file path"
    )
    parser.add_argument(
        "--backup-path",
        help="Backup file path (for backup/restore operations)"
    )
    parser.add_argument(
        "--force",
        action="store_true",
        help="Force operation without confirmation"
    )
    
    args = parser.parse_args()
    
    # 设置日志级别
    import logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    
    success = False
    
    if args.action == "init":
        success = init_database(args.db_path, args.force)
    elif args.action == "reset":
        success = reset_database(args.db_path)
    elif args.action == "backup":
        success = backup_database(args.db_path, args.backup_path)
    elif args.action == "restore":
        if not args.backup_path:
            logger.error("Backup path is required for restore operation")
            sys.exit(1)
        success = restore_database(args.backup_path, args.db_path)
    elif args.action == "check":
        success = check_database(args.db_path)
    
    if success:
        logger.info(f"Operation '{args.action}' completed successfully")
        sys.exit(0)
    else:
        logger.error(f"Operation '{args.action}' failed")
        sys.exit(1)


if __name__ == "__main__":
    main()