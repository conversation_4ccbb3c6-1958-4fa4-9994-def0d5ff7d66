# -*- coding: utf-8 -*-
"""
初始数据库架构迁移脚本
"""

from datetime import datetime

from sqlalchemy import (
    Column, String, Text, Integer, Float, Boolean, DateTime, JSON,
    ForeignKey, text
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func

Base = declarative_base()


def upgrade(engine):
    """
    执行数据库升级
    
    Args:
        engine: SQLAlchemy引擎
    """
    # 创建所有表
    Base.metadata.create_all(bind=engine)
    
    # 创建索引
    _create_indexes(engine)
    
    # 插入初始数据
    _insert_initial_data(engine)


def downgrade(engine):
    """
    执行数据库降级
    
    Args:
        engine: SQLAlchemy引擎
    """
    # 删除所有表
    Base.metadata.drop_all(bind=engine)


def _create_indexes(engine):
    """
    创建数据库索引
    
    Args:
        engine: SQLAlchemy引擎
    """
    with engine.connect() as conn:
        # Sessions表索引
        conn.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_sessions_status 
            ON sessions(status)
        """))
        
        conn.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_sessions_created_at 
            ON sessions(created_at)
        """))
        
        conn.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_sessions_last_activity 
            ON sessions(last_activity)
        """))
        
        # Conversations表索引
        conn.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_conversations_session_id 
            ON conversations(session_id)
        """))
        
        conn.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_conversations_model_name 
            ON conversations(model_name)
        """))
        
        conn.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_conversations_status 
            ON conversations(status)
        """))
        
        conn.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_conversations_timestamp 
            ON conversations(timestamp)
        """))
        
        # Prompts表索引
        conn.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_prompts_name 
            ON prompts(name)
        """))
        
        conn.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_prompts_category 
            ON prompts(category)
        """))
        
        conn.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_prompts_is_active 
            ON prompts(is_active)
        """))
        
        conn.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_prompts_is_system 
            ON prompts(is_system)
        """))
        
        # API Keys表索引
        conn.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_api_keys_key 
            ON api_keys(key)
        """))
        
        conn.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_api_keys_is_active 
            ON api_keys(is_active)
        """))
        
        conn.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_api_keys_expires_at 
            ON api_keys(expires_at)
        """))
        
        conn.commit()


def _insert_initial_data(engine):
    """
    插入初始数据
    
    Args:
        engine: SQLAlchemy引擎
    """
    conn = engine.connect()
    
    try:
        # 插入系统提示词
        system_prompts = [
            {
                "id": "system-default",
                "name": "默认系统提示词",
                "description": "默认的系统级提示词，用于基础对话",
                "content": "你是一个有用的AI助手。请根据用户的问题提供准确、有帮助的回答。",
                "variables": [],
                "category": "system",
                "tags": ["default", "system"],
                "version": "1.0",
                "is_active": True,
                "is_system": True,
                "usage_count": 0
            },
            {
                "id": "system-coding",
                "name": "编程助手提示词",
                "description": "专门用于编程相关问题的系统提示词",
                "content": "你是一个专业的编程助手。请帮助用户解决编程问题，提供清晰的代码示例和解释。",
                "variables": [],
                "category": "system",
                "tags": ["coding", "programming", "system"],
                "version": "1.0",
                "is_active": True,
                "is_system": True,
                "usage_count": 0
            },
            {
                "id": "system-analysis",
                "name": "分析助手提示词",
                "description": "用于数据分析和逻辑推理的系统提示词",
                "content": "你是一个专业的分析师。请帮助用户分析数据、解决问题，并提供基于逻辑的见解和建议。",
                "variables": [],
                "category": "system",
                "tags": ["analysis", "data", "logic", "system"],
                "version": "1.0",
                "is_active": True,
                "is_system": True,
                "usage_count": 0
            }
        ]
        
        for prompt_data in system_prompts:
            # 检查是否已存在
            existing = conn.execute(
                text("SELECT id FROM prompts WHERE id = :id"),
                {"id": prompt_data["id"]}
            ).fetchone()
            
            if not existing:
                conn.execute(text("""
                    INSERT INTO prompts (
                        id, name, description, content, variables, category, tags,
                        version, parent_id, is_active, is_system, usage_count,
                        last_used, created_at, updated_at
                    ) VALUES (
                        :id, :name, :description, :content, :variables, :category, :tags,
                        :version, :parent_id, :is_active, :is_system, :usage_count,
                        :last_used, :created_at, :updated_at
                    )
                """), {
                    "id": prompt_data["id"],
                    "name": prompt_data["name"],
                    "description": prompt_data["description"],
                    "content": prompt_data["content"],
                    "variables": str(prompt_data["variables"]),  # JSON as string
                    "category": prompt_data["category"],
                    "tags": str(prompt_data["tags"]),  # JSON as string
                    "version": prompt_data["version"],
                    "parent_id": None,
                    "is_active": prompt_data["is_active"],
                    "is_system": prompt_data["is_system"],
                    "usage_count": prompt_data["usage_count"],
                    "last_used": None,
                    "created_at": datetime.utcnow(),
                    "updated_at": datetime.utcnow()
                })
        
        # 插入示例API密钥（仅用于开发环境）
        import os
        if os.getenv("ENVIRONMENT", "development") == "development":
            # 生成示例API密钥
            import secrets
            import hashlib
            
            api_key = f"llm_api_{secrets.token_urlsafe(32)}"
            key_hash = hashlib.sha256(api_key.encode()).hexdigest()
            
            # 检查是否已存在开发API密钥
            existing_dev_key = conn.execute(
                text("SELECT id FROM api_keys WHERE name = :name"),
                {"name": "Development Key"}
            ).fetchone()
            
            if not existing_dev_key:
                conn.execute(text("""
                    INSERT INTO api_keys (
                        key, name, description, permissions, is_active,
                        rate_limit, daily_limit, usage_count, last_used, 
                        expires_at, created_at
                    ) VALUES (:key, :name, :description, :permissions, :is_active,
                             :rate_limit, :daily_limit, :usage_count, :last_used,
                             :expires_at, :created_at)
                """), {
                    "key": api_key,
                    "name": "Development Key",
                    "description": "Development API key for testing",
                    "permissions": str(["read", "write", "admin"]),  # JSON as string
                    "is_active": True,
                    "rate_limit": None,
                    "daily_limit": None,
                    "usage_count": 0,
                    "last_used": None,
                    "expires_at": None,  # 不过期
                    "created_at": datetime.utcnow()
                })
                
                # 将API密钥写入文件供开发使用
                with open("dev_api_key.txt", "w") as f:
                    f.write(f"Development API Key: {api_key}\n")
                    f.write(f"Use this key for API authentication during development.\n")
                    f.write(f"Add it to your requests as: Authorization: Bearer {api_key}\n")
        
        conn.commit()
        print("Initial data inserted successfully")
        
    except Exception as e:
        conn.rollback()
        print(f"Error inserting initial data: {e}")
        raise
    finally:
        conn.close()


# 表定义（与models.py保持一致）

class Session(Base):
    __tablename__ = "sessions"
    
    id = Column(String(36), primary_key=True)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    status = Column(String(20), nullable=False, default="active")
    config = Column(JSON)
    enabled_models = Column(JSON)
    total_messages = Column(Integer, default=0)
    total_tokens = Column(Integer, default=0)
    api_key_id = Column(String(36), ForeignKey("api_keys.id"))
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    last_activity = Column(DateTime, default=func.now())


class Conversation(Base):
    __tablename__ = "conversations"
    
    id = Column(String(36), primary_key=True)
    session_id = Column(String(36), ForeignKey("sessions.id"), nullable=False)
    model_name = Column(String(100), nullable=False)
    user_message = Column(Text, nullable=False)
    assistant_message = Column(Text)
    system_prompt = Column(Text)
    status = Column(String(20), nullable=False, default="pending")
    response_time = Column(Float)
    input_tokens = Column(Integer)
    output_tokens = Column(Integer)
    error_message = Column(Text)
    session_metadata = Column(JSON)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())


class Prompt(Base):
    __tablename__ = "prompts"
    
    id = Column(String(36), primary_key=True)
    name = Column(String(255), nullable=False, unique=True)
    description = Column(Text)
    content = Column(Text, nullable=False)
    variables = Column(JSON)
    category = Column(String(100))
    tags = Column(JSON)
    version = Column(String(20), default="1.0")
    parent_id = Column(String(36), ForeignKey("prompts.id"))
    is_active = Column(Boolean, default=True)
    is_system = Column(Boolean, default=False)
    usage_count = Column(Integer, default=0)
    last_used = Column(DateTime)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())


class APIKey(Base):
    __tablename__ = "api_keys"
    
    id = Column(String(36), primary_key=True)
    name = Column(String(255), nullable=False)
    key_hash = Column(String(64), nullable=False, unique=True)
    permissions = Column(JSON)
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)
    usage_count = Column(Integer, default=0)
    last_used = Column(DateTime)
    expires_at = Column(DateTime)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())