# -*- coding: utf-8 -*-
"""
数据库模型定义
"""

from datetime import datetime
from typing import Dict, Any

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, Float, ForeignKey, JSON
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import relationship

from database.connection import Base


class Session(Base):
    """会话模型"""
    __tablename__ = "sessions"
    
    id = Column(String, primary_key=True, index=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    last_activity = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # 会话配置
    models = Column(JSON, nullable=False, default=list)  # 启用的模型列表
    system_prompt = Column(Text, nullable=True)  # 系统提示词
    system_prompt_id = Column(String, ForeignKey("prompts.id"), nullable=True)  # 关联的提示词ID
    
    # 会话元数据
    session_metadata = Column(JSON, nullable=False, default=dict)  # 额外的元数据
    status = Column(String, default="active", nullable=False)  # active, inactive, expired
    
    # 统计信息
    total_messages = Column(Integer, default=0, nullable=False)
    total_tokens = Column(Integer, default=0, nullable=False)
    
    # 关系
    conversations = relationship("Conversation", back_populates="session", cascade="all, delete-orphan")
    prompt = relationship("Prompt", back_populates="sessions")
    
    def __repr__(self):
        return f"<Session(id='{self.id}', status='{self.status}', models={len(self.models)})>"
    
    @hybrid_property
    def is_active(self) -> bool:
        """检查会话是否活跃"""
        return self.status == "active"
    
    @hybrid_property
    def duration_minutes(self) -> float:
        """会话持续时间（分钟）"""
        if self.last_activity and self.created_at:
            return (self.last_activity - self.created_at).total_seconds() / 60
        return 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "last_activity": self.last_activity.isoformat() if self.last_activity else None,
            "models": self.models,
            "system_prompt": self.system_prompt,
            "system_prompt_id": self.system_prompt_id,
            "metadata": self.metadata,
            "status": self.status,
            "total_messages": self.total_messages,
            "total_tokens": self.total_tokens,
            "is_active": self.is_active,
            "duration_minutes": self.duration_minutes
        }


class Conversation(Base):
    """对话记录模型"""
    __tablename__ = "conversations"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    session_id = Column(String, ForeignKey("sessions.id"), nullable=False, index=True)
    
    # 对话内容
    user_message = Column(Text, nullable=False)
    model_name = Column(String, nullable=False, index=True)
    model_response = Column(Text, nullable=False)
    
    # 时间信息
    timestamp = Column(DateTime, default=datetime.utcnow, nullable=False, index=True)
    
    # 性能信息
    response_time = Column(Float, nullable=True)  # 响应时间（秒）
    token_count = Column(Integer, nullable=True)  # token数量
    
    # 状态信息
    status = Column(String, default="success", nullable=False)  # success, error, timeout
    error_message = Column(Text, nullable=True)  # 错误信息
    
    # 模型配置
    model_version = Column(String, nullable=True)  # 模型版本
    model_config = Column(JSON, nullable=True)  # 模型配置参数
    
    # 提示词信息
    system_prompt_used = Column(Text, nullable=True)  # 使用的系统提示词
    prompt_variables = Column(JSON, nullable=True)  # 提示词变量
    
    # 关系
    session = relationship("Session", back_populates="conversations")
    
    def __repr__(self):
        return f"<Conversation(id={self.id}, session_id='{self.session_id}', model='{self.model_name}')>"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "session_id": self.session_id,
            "user_message": self.user_message,
            "model_name": self.model_name,
            "model_response": self.model_response,
            "timestamp": self.timestamp.isoformat() if self.timestamp else None,
            "response_time": self.response_time,
            "token_count": self.token_count,
            "status": self.status,
            "error_message": self.error_message,
            "model_version": self.model_version,
            "model_config": self.model_config,
            "system_prompt_used": self.system_prompt_used,
            "prompt_variables": self.prompt_variables
        }


class Prompt(Base):
    """提示词模型"""
    __tablename__ = "prompts"
    
    id = Column(String, primary_key=True, index=True)
    name = Column(String, nullable=False, index=True)
    description = Column(Text, nullable=True)
    
    # 提示词内容
    content = Column(Text, nullable=False)
    variables = Column(JSON, nullable=False, default=list)  # 变量列表
    
    # 分类和标签
    category = Column(String, nullable=True, index=True)
    tags = Column(JSON, nullable=False, default=list)
    
    # 时间信息
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # 状态信息
    is_active = Column(Boolean, default=True, nullable=False)
    is_system = Column(Boolean, default=False, nullable=False)  # 是否为系统内置提示词
    
    # 使用统计
    usage_count = Column(Integer, default=0, nullable=False)
    last_used = Column(DateTime, nullable=True)
    
    # 版本信息
    version = Column(String, default="1.0", nullable=False)
    parent_id = Column(String, ForeignKey("prompts.id"), nullable=True)  # 父提示词ID（用于版本控制）
    
    # 关系
    sessions = relationship("Session", back_populates="prompt")
    children = relationship("Prompt", backref="parent", remote_side=[id])
    
    def __repr__(self):
        return f"<Prompt(id='{self.id}', name='{self.name}', version='{self.version}')>"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "content": self.content,
            "variables": self.variables,
            "category": self.category,
            "tags": self.tags,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "is_active": self.is_active,
            "is_system": self.is_system,
            "usage_count": self.usage_count,
            "last_used": self.last_used.isoformat() if self.last_used else None,
            "version": self.version,
            "parent_id": self.parent_id
        }
    
    def render(self, variables: Dict[str, Any] = None) -> str:
        """渲染提示词模板"""
        if not variables:
            return self.content
        
        try:
            # 简单的变量替换
            content = self.content
            for var_name, var_value in variables.items():
                placeholder = f"{{{var_name}}}"
                content = content.replace(placeholder, str(var_value))
            return content
        except Exception as e:
            # 如果渲染失败，返回原始内容
            return self.content


class APIKey(Base):
    """API密钥模型（用于认证）"""
    __tablename__ = "api_keys"
    
    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    key = Column(String, unique=True, nullable=False, index=True)
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    
    # 权限控制
    is_active = Column(Boolean, default=True, nullable=False)
    permissions = Column(JSON, nullable=False, default=list)  # 权限列表
    
    # 使用限制
    rate_limit = Column(Integer, nullable=True)  # 每分钟请求限制
    daily_limit = Column(Integer, nullable=True)  # 每日请求限制
    
    # 时间信息
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    expires_at = Column(DateTime, nullable=True)
    last_used = Column(DateTime, nullable=True)
    
    # 使用统计
    usage_count = Column(Integer, default=0, nullable=False)
    
    def __repr__(self):
        return f"<APIKey(id={self.id}, name='{self.name}', active={self.is_active})>"
    
    @hybrid_property
    def is_expired(self) -> bool:
        """检查密钥是否过期"""
        if self.expires_at:
            return datetime.utcnow() > self.expires_at
        return False
    
    @hybrid_property
    def is_valid(self) -> bool:
        """检查密钥是否有效"""
        return self.is_active and not self.is_expired
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典（不包含敏感信息）"""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "is_active": self.is_active,
            "permissions": self.permissions,
            "rate_limit": self.rate_limit,
            "daily_limit": self.daily_limit,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "last_used": self.last_used.isoformat() if self.last_used else None,
            "usage_count": self.usage_count,
            "is_expired": self.is_expired,
            "is_valid": self.is_valid
        }