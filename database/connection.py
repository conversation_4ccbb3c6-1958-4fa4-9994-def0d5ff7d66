# -*- coding: utf-8 -*-
"""
数据库连接管理
"""

import os
from pathlib import Path
from typing import AsyncGenerator

from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import declarative_base, sessionmaker
from loguru import logger

# 数据库配置
DATABASE_DIR = Path("data/database")
DATABASE_DIR.mkdir(parents=True, exist_ok=True)
DATABASE_URL = f"sqlite:///{DATABASE_DIR}/llm_comparison.db"
ASYNC_DATABASE_URL = f"sqlite+aiosqlite:///{DATABASE_DIR}/llm_comparison.db"

# 创建数据库引擎
engine = create_engine(
    DATABASE_URL,
    connect_args={"check_same_thread": False},  # SQLite特定配置
    echo=False  # 设置为True可以看到SQL语句
)

async_engine = create_async_engine(
    ASYNC_DATABASE_URL,
    echo=False
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
AsyncSessionLocal = async_sessionmaker(
    async_engine,
    class_=AsyncSession,
    expire_on_commit=False
)

# 创建基础模型类
Base = declarative_base()

# 元数据
metadata = MetaData()


async def init_database():
    """初始化数据库"""
    try:
        logger.info("正在初始化数据库...")
        
        # 导入所有模型以确保它们被注册
        from database.models import Session, Conversation, Prompt
        
        # 创建所有表
        async with async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        logger.info("数据库初始化完成")
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}", exc_info=True)
        raise


async def close_database():
    """关闭数据库连接"""
    try:
        logger.info("正在关闭数据库连接...")
        await async_engine.dispose()
        logger.info("数据库连接已关闭")
    except Exception as e:
        logger.error(f"关闭数据库连接时出错: {e}", exc_info=True)


async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    """获取异步数据库会话"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception as e:
            await session.rollback()
            logger.error(f"数据库会话异常: {e}", exc_info=True)
            raise
        finally:
            await session.close()


def get_session():
    """获取同步数据库会话（用于兼容现有代码）"""
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        db.rollback()
        logger.error(f"数据库会话异常: {e}", exc_info=True)
        raise
    finally:
        db.close()


def get_db_session():
    """获取数据库会话（FastAPI依赖注入使用）"""
    return get_session()


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.engine = async_engine
        self.session_factory = AsyncSessionLocal
    
    async def create_session(self) -> AsyncSession:
        """创建新的数据库会话"""
        return self.session_factory()
    
    async def execute_query(self, query: str, params: dict = None):
        """执行原生SQL查询"""
        async with self.session_factory() as session:
            try:
                result = await session.execute(query, params or {})
                await session.commit()
                return result
            except Exception as e:
                await session.rollback()
                logger.error(f"执行查询失败: {e}", exc_info=True)
                raise
        return None

    async def backup_database(self, backup_path: str = None):
        """备份数据库"""
        if not backup_path:
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"{DATABASE_DIR}/backup_llm_comparison_{timestamp}.db"
        
        try:
            import shutil
            shutil.copy2(f"{DATABASE_DIR}/llm_comparison.db", backup_path)
            logger.info(f"数据库备份完成: {backup_path}")
            return backup_path
        except Exception as e:
            logger.error(f"数据库备份失败: {e}", exc_info=True)
            raise
    
    async def restore_database(self, backup_path: str):
        """恢复数据库"""
        try:
            import shutil
            # 关闭所有连接
            await self.engine.dispose()
            
            # 恢复数据库文件
            shutil.copy2(backup_path, f"{DATABASE_DIR}/llm_comparison.db")
            
            # 重新创建引擎
            global async_engine, AsyncSessionLocal
            async_engine = create_async_engine(ASYNC_DATABASE_URL, echo=False)
            AsyncSessionLocal = async_sessionmaker(
                async_engine,
                class_=AsyncSession,
                expire_on_commit=False
            )
            self.engine = async_engine
            self.session_factory = AsyncSessionLocal
            
            logger.info(f"数据库恢复完成: {backup_path}")
            
        except Exception as e:
            logger.error(f"数据库恢复失败: {e}", exc_info=True)
            raise
    
    def get_session(self):
        """获取同步数据库会话（兼容方法）"""
        return get_session()
    
    def get_db_file_path(self) -> str:
        """获取数据库文件路径"""
        return f"{DATABASE_DIR}/llm_comparison.db"
    
    async def init_database(self):
        """初始化数据库（异步方法）"""
        await init_database()
    
    def get_engine(self):
        """获取数据库引擎"""
        return engine


# 全局数据库管理器实例
db_manager = DatabaseManager()