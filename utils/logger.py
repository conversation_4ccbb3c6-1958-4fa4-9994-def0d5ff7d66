"""日志工具模块

提供统一的日志接口，兼容测试和生产环境
"""

import os
import sys
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
import datatime

try:
    from utils.logger import logger as loguru_logger
    LOGURU_AVAILABLE = True
except ImportError:
    LOGURU_AVAILABLE = False
    import logging as loguru_logger


class Logger:
    """基础日志器类"""

    def __init__(self, name: str = "default"):
        self.name = name
        if LOGURU_AVAILABLE:
            self._logger = loguru_logger.bind(name=name)
        else:
            self._logger = logging.getLogger(name)

    def info(self, message: str, **kwargs):
        """记录信息日志"""
        if LOGURU_AVAILABLE:
            self._logger.info(message, **kwargs)
        else:
            self._logger.info(message, extra=kwargs)

    def error(self, message: str, **kwargs):
        """记录错误日志"""
        if LOGURU_AVAILABLE:
            self._logger.error(message, **kwargs)
        else:
            self._logger.error(message, extra=kwargs)

    def warning(self, message: str, **kwargs):
        """记录警告日志"""
        if LOGURU_AVAILABLE:
            self._logger.warning(message, **kwargs)
        else:
            self._logger.warning(message, extra=kwargs)

    def debug(self, message: str, **kwargs):
        """记录调试日志"""
        if LOGURU_AVAILABLE:
            self._logger.debug(message, **kwargs)
        else:
            self._logger.debug(message, extra=kwargs)


class StructuredLogger(Logger):
    """结构化日志器"""

    def __init__(self, name: str = "structured", context: Optional[Dict[str, Any]] = None):
        super().__init__(name)
        self.context = context or {}

    def log_with_context(self, level: str, message: str, **kwargs):
        """带上下文的日志记录"""
        combined_context = {**self.context, **kwargs}
        getattr(self, level.lower())(message, **combined_context)


class PerformanceLogger(Logger):
    """性能日志器"""

    def __init__(self, name: str = "performance"):
        super().__init__(name)

    def log_timing(self, operation: str, duration: float, **kwargs):
        """记录操作耗时"""
        self.info(f"Operation '{operation}' took {duration:.3f}s",
                 operation=operation, duration=duration, **kwargs)


class SecurityLogger(Logger):
    """安全日志器"""

    def __init__(self, name: str = "security"):
        super().__init__(name)

    def log_security_event(self, event_type: str, details: Dict[str, Any]):
        """记录安全事件"""
        self.warning(f"Security event: {event_type}",
                    event_type=event_type, **details)


class LogFormatter:
    """基础日志格式化器"""

    def __init__(self, format_string: str = None):
        self.format_string = format_string or "{time} | {level} | {message}"

    def format(self, record: Dict[str, Any]) -> str:
        """格式化日志记录"""
        return self.format_string.format(**record)


class JSONFormatter(LogFormatter):
    """JSON格式化器"""

    def format(self, record: Dict[str, Any]) -> str:
        """格式化为JSON"""
        return json.dumps(record, ensure_ascii=False)


class ColoredFormatter(LogFormatter):
    """彩色格式化器"""

    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }

    def format(self, record: Dict[str, Any]) -> str:
        """格式化为彩色输出"""
        level = record.get('level', 'INFO')
        color = self.COLORS.get(level, '')
        reset = self.COLORS['RESET']
        formatted = super().format(record)
        return f"{color}{formatted}{reset}"


class LogFilter:
    """基础日志过滤器"""

    def __init__(self, filter_func=None):
        self.filter_func = filter_func or (lambda record: True)

    def filter(self, record: Dict[str, Any]) -> bool:
        """过滤日志记录"""
        return self.filter_func(record)


class SensitiveDataFilter(LogFilter):
    """敏感数据过滤器"""

    def __init__(self, sensitive_patterns: List[str] = None):
        self.sensitive_patterns = sensitive_patterns or [
            r'password', r'token', r'key', r'secret'
        ]
        super().__init__(self._filter_sensitive)

    def _filter_sensitive(self, record: Dict[str, Any]) -> bool:
        """过滤敏感数据"""
        message = str(record.get('message', ''))
        for pattern in self.sensitive_patterns:
            if pattern.lower() in message.lower():
                record['message'] = message.replace(pattern, '***')
        return True


class LogRotationHandler:
    """日志轮转处理器"""

    def __init__(self, filename: str, max_size: int = 10*1024*1024, backup_count: int = 5):
        self.filename = filename
        self.max_size = max_size
        self.backup_count = backup_count
        self.current_size = 0

    def should_rotate(self) -> bool:
        """检查是否需要轮转"""
        if os.path.exists(self.filename):
            self.current_size = os.path.getsize(self.filename)
            return self.current_size >= self.max_size
        return False

    def rotate(self):
        """执行日志轮转"""
        if self.should_rotate():
            # 简单的轮转逻辑
            for i in range(self.backup_count - 1, 0, -1):
                old_file = f"{self.filename}.{i}"
                new_file = f"{self.filename}.{i + 1}"
                if os.path.exists(old_file):
                    os.rename(old_file, new_file)

            if os.path.exists(self.filename):
                os.rename(self.filename, f"{self.filename}.1")


class LogAggregator:
    """日志聚合器"""

    def __init__(self):
        self.logs = []

    def add_log(self, record: Dict[str, Any]):
        """添加日志记录"""
        self.logs.append(record)

    def get_logs(self, level: str = None, limit: int = None) -> List[Dict[str, Any]]:
        """获取日志记录"""
        filtered_logs = self.logs

        if level:
            filtered_logs = [log for log in filtered_logs if log.get('level') == level]

        if limit:
            filtered_logs = filtered_logs[-limit:]

        return filtered_logs

    def clear(self):
        """清空日志"""
        self.logs.clear()


class LogAnalyzer:
    """日志分析器"""

    def __init__(self, logs: List[Dict[str, Any]] = None):
        self.logs = logs or []

    def analyze_levels(self) -> Dict[str, int]:
        """分析日志级别分布"""
        level_counts = {}
        for log in self.logs:
            level = log.get('level', 'UNKNOWN')
            level_counts[level] = level_counts.get(level, 0) + 1
        return level_counts

    def analyze_errors(self) -> List[Dict[str, Any]]:
        """分析错误日志"""
        return [log for log in self.logs if log.get('level') in ['ERROR', 'CRITICAL']]

    def get_statistics(self) -> Dict[str, Any]:
        """获取日志统计信息"""
        return {
            'total_logs': len(self.logs),
            'level_distribution': self.analyze_levels(),
            'error_count': len(self.analyze_errors()),
            'time_range': self._get_time_range()
        }

    def _get_time_range(self) -> Dict[str, Any]:
        """获取时间范围"""
        if not self.logs:
            return {'start': None, 'end': None}

        timestamps = [log.get('timestamp') for log in self.logs if log.get('timestamp')]
        if not timestamps:
            return {'start': None, 'end': None}

        return {
            'start': min(timestamps),
            'end': max(timestamps)
        }


def setup_logging(
    level: str = "INFO",
    log_dir: str = "logs",
    enable_console: bool = True,
    enable_file: bool = True,
    structured: bool = False
) -> Logger:
    """设置日志系统

    Args:
        level: 日志级别
        log_dir: 日志目录
        enable_console: 是否启用控制台输出
        enable_file: 是否启用文件输出
        structured: 是否使用结构化日志

    Returns:
        配置好的日志器
    """
    # 创建日志目录
    Path(log_dir).mkdir(parents=True, exist_ok=True)

    if LOGURU_AVAILABLE:
        # 使用 loguru 配置
        loguru_logger.remove()  # 移除默认处理器

        if enable_console:
            loguru_logger.add(
                sys.stdout,
                level=level,
                format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan> | <level>{message}</level>",
                colorize=True
            )

        if enable_file:
            log_file = Path(log_dir) / "app.log"
            loguru_logger.add(
                log_file,
                level=level,
                format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name} | {message}",
                rotation="10 MB",
                retention="30 days",
                compression="zip",
                serialize=structured
            )
    else:
        # 使用标准 logging 配置
        logging.basicConfig(
            level=getattr(logging, level.upper()),
            format='%(asctime)s | %(levelname)-8s | %(name)s | %(message)s',
            handlers=[
                logging.StreamHandler() if enable_console else None,
                logging.FileHandler(Path(log_dir) / "app.log") if enable_file else None
            ]
        )

    return Logger("root")


def get_logger(name: str = None) -> Logger:
    """获取日志器实例

    Args:
        name: 日志器名称

    Returns:
        日志器实例
    """
    if name is None:
        name = "default"

    return Logger(name)


# 向后兼容的别名
logger = get_logger("utils.logger")


# 导出所有类和函数
__all__ = [
    'Logger', 'StructuredLogger', 'PerformanceLogger', 'SecurityLogger',
    'LogFormatter', 'JSONFormatter', 'ColoredFormatter',
    'LogFilter', 'SensitiveDataFilter', 'LogRotationHandler',
    'LogAggregator', 'LogAnalyzer',
    'setup_logging', 'get_logger', 'logger'
]