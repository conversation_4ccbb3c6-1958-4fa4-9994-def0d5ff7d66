"""日志工具模块

提供统一的日志接口，兼容测试和生产环境
"""

import os
import sys
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime

try:
    from loguru import logger as loguru_logger
    LOGURU_AVAILABLE = True
except ImportError:
    LOGURU_AVAILABLE = False
    import logging as loguru_logger


class Logger:
    """基础日志器类"""

    def __init__(self, name: str = "default"):
        self.name = name
        if LOGURU_AVAILABLE:
            self._logger = loguru_logger.bind(name=name)
        else:
            self._logger = logging.getLogger(name)

    def info(self, message: str, **kwargs):
        """记录信息日志"""
        if LOGURU_AVAILABLE:
            self._logger.info(message, **kwargs)
        else:
            self._logger.info(message, extra=kwargs)

    def error(self, message: str, **kwargs):
        """记录错误日志"""
        if LOGURU_AVAILABLE:
            self._logger.error(message, **kwargs)
        else:
            self._logger.error(message, extra=kwargs)

    def warning(self, message: str, **kwargs):
        """记录警告日志"""
        if LOGURU_AVAILABLE:
            self._logger.warning(message, **kwargs)
        else:
            self._logger.warning(message, extra=kwargs)

    def debug(self, message: str, **kwargs):
        """记录调试日志"""
        if LOGURU_AVAILABLE:
            self._logger.debug(message, **kwargs)
        else:
            self._logger.debug(message, extra=kwargs)


class StructuredLogger(Logger):
    """结构化日志器"""

    def __init__(self, name: str = "structured", context: Optional[Dict[str, Any]] = None):
        super().__init__(name)
        self.context = context or {}

    def log_with_context(self, level: str, message: str, **kwargs):
        """带上下文的日志记录"""
        combined_context = {**self.context, **kwargs}
        getattr(self, level.lower())(message, **combined_context)


class PerformanceLogger(Logger):
    """性能日志器"""

    def __init__(self, name: str = "performance"):
        super().__init__(name)

    def log_timing(self, operation: str, duration: float, **kwargs):
        """记录操作耗时"""
        self.info(f"Operation '{operation}' took {duration:.3f}s",
                 operation=operation, duration=duration, **kwargs)


class SecurityLogger(Logger):
    """安全日志器"""

    def __init__(self, name: str = "security"):
        super().__init__(name)

    def log_security_event(self, event_type: str, details: Dict[str, Any]):
        """记录安全事件"""
        self.warning(f"Security event: {event_type}",
                    event_type=event_type, **details)