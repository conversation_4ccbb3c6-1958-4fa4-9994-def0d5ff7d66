#!/usr/bin/env python3
"""
数据库优化工具
提供慢查询分析、索引建议和查询优化功能
"""

import time
from dataclasses import dataclass
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional, Tu<PERSON>

from loguru import logger
from sqlalchemy import inspect
from sqlalchemy.orm import Session

from api.config import get_settings
from database.connection import engine

settings = get_settings()


@dataclass
class QueryStats:
    """查询统计信息"""
    query: str
    execution_time: float
    timestamp: datetime
    table_name: Optional[str] = None
    operation_type: Optional[str] = None  # SELECT, INSERT, UPDATE, DELETE
    rows_affected: Optional[int] = None


@dataclass
class SlowQuery:
    """慢查询信息"""
    query: str
    avg_execution_time: float
    max_execution_time: float
    min_execution_time: float
    execution_count: int
    total_time: float
    first_seen: datetime
    last_seen: datetime
    suggested_indexes: List[str]


@dataclass
class IndexSuggestion:
    """索引建议"""
    table_name: str
    column_names: List[str]
    index_type: str  # BTREE, HASH, etc.
    reason: str
    estimated_improvement: float  # 预估性能提升百分比
    priority: str  # HIGH, MEDIUM, LOW


class DatabaseOptimizer:
    """数据库优化器"""
    
    def __init__(self):
        self.query_stats: List[QueryStats] = []
        self.slow_query_threshold = 1.0  # 1秒
        self.max_stats_history = 10000
        
    def record_query(self, query: str, execution_time: float, 
                    table_name: Optional[str] = None,
                    operation_type: Optional[str] = None,
                    rows_affected: Optional[int] = None):
        """记录查询统计"""
        stats = QueryStats(
            query=query,
            execution_time=execution_time,
            timestamp=datetime.now(),
            table_name=table_name,
            operation_type=operation_type,
            rows_affected=rows_affected
        )
        
        self.query_stats.append(stats)
        
        # 保持历史记录在限制范围内
        if len(self.query_stats) > self.max_stats_history:
            self.query_stats = self.query_stats[-self.max_stats_history:]
            
        # 记录慢查询
        if execution_time > self.slow_query_threshold:
            logger.warning(f"Slow query detected: {execution_time:.3f}s - {query[:100]}...")
    
    def analyze_slow_queries(self, db: Session = None, days: int = 7, limit: int = 10) -> List[SlowQuery]:
        """分析慢查询"""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        # 按查询分组统计
        query_groups = {}
        
        for stat in self.query_stats:
            if stat.timestamp < cutoff_date:
                continue
                
            # 简化查询用于分组（移除参数值）
            normalized_query = self._normalize_query(stat.query)
            
            if normalized_query not in query_groups:
                query_groups[normalized_query] = []
            query_groups[normalized_query].append(stat)
        
        slow_queries = []
        
        for query, stats in query_groups.items():
            execution_times = [s.execution_time for s in stats]
            avg_time = sum(execution_times) / len(execution_times)
            
            # 只关注慢查询
            if avg_time > self.slow_query_threshold:
                slow_query = SlowQuery(
                    query=query,
                    avg_execution_time=avg_time,
                    max_execution_time=max(execution_times),
                    min_execution_time=min(execution_times),
                    execution_count=len(stats),
                    total_time=sum(execution_times),
                    first_seen=min(s.timestamp for s in stats),
                    last_seen=max(s.timestamp for s in stats),
                    suggested_indexes=self._suggest_indexes_for_query(query)
                )
                slow_queries.append(slow_query)
        
        # 按总时间排序
        slow_queries.sort(key=lambda x: x.total_time, reverse=True)
        return slow_queries[:limit]
    
    def _normalize_query(self, query: str) -> str:
        """标准化查询（移除参数值用于分组）"""
        import re
        
        # 移除字符串字面量
        query = re.sub(r"'[^']*'", "'?'", query)
        query = re.sub(r'"[^"]*"', '"?"', query)
        
        # 移除数字字面量
        query = re.sub(r'\b\d+\b', '?', query)
        
        # 移除多余空格
        query = re.sub(r'\s+', ' ', query)
        
        return query.strip()
    
    def _suggest_indexes_for_query(self, query: str) -> List[str]:
        """为查询建议索引"""
        suggestions = []
        query_lower = query.lower()
        
        # 分析WHERE子句
        if 'where' in query_lower:
            # 简单的WHERE子句分析
            import re
            where_patterns = [
                r'where\s+(\w+)\s*=',
                r'where\s+(\w+)\s*in',
                r'where\s+(\w+)\s*>',
                r'where\s+(\w+)\s*<',
                r'and\s+(\w+)\s*=',
                r'and\s+(\w+)\s*in',
                r'and\s+(\w+)\s*>',
                r'and\s+(\w+)\s*<'
            ]
            
            for pattern in where_patterns:
                matches = re.findall(pattern, query_lower)
                for match in matches:
                    suggestions.append(f"CREATE INDEX idx_{match} ON table_name ({match})")
        
        # 分析ORDER BY子句
        if 'order by' in query_lower:
            import re
            order_pattern = r'order\s+by\s+(\w+)'
            matches = re.findall(order_pattern, query_lower)
            for match in matches:
                suggestions.append(f"CREATE INDEX idx_{match}_order ON table_name ({match})")
        
        return list(set(suggestions))  # 去重
    
    def analyze_table_usage(self) -> Dict[str, Dict[str, Any]]:
        """分析表使用情况"""
        table_stats = {}
        
        for stat in self.query_stats:
            if not stat.table_name:
                continue
                
            if stat.table_name not in table_stats:
                table_stats[stat.table_name] = {
                    'total_queries': 0,
                    'total_time': 0.0,
                    'avg_time': 0.0,
                    'operations': {'SELECT': 0, 'INSERT': 0, 'UPDATE': 0, 'DELETE': 0},
                    'slow_queries': 0
                }
            
            stats = table_stats[stat.table_name]
            stats['total_queries'] += 1
            stats['total_time'] += stat.execution_time
            
            if stat.operation_type:
                stats['operations'][stat.operation_type] = stats['operations'].get(stat.operation_type, 0) + 1
            
            if stat.execution_time > self.slow_query_threshold:
                stats['slow_queries'] += 1
        
        # 计算平均时间
        for table_name, stats in table_stats.items():
            if stats['total_queries'] > 0:
                stats['avg_time'] = stats['total_time'] / stats['total_queries']
        
        return table_stats
    
    def suggest_indexes(self) -> List[IndexSuggestion]:
        """建议索引优化"""
        suggestions = []
        
        # 基于慢查询分析
        slow_queries = self.analyze_slow_queries()
        
        for slow_query in slow_queries[:10]:  # 只分析前10个最慢的查询
            # 分析查询模式
            query_lower = slow_query.query.lower()
            
            # 检查是否有WHERE子句但缺少索引
            if 'where' in query_lower and 'conversations' in query_lower:
                if 'session_id' in query_lower:
                    suggestions.append(IndexSuggestion(
                        table_name='conversations',
                        column_names=['session_id'],
                        index_type='BTREE',
                        reason='频繁的session_id查询',
                        estimated_improvement=30.0,
                        priority='HIGH'
                    ))
                
                if 'timestamp' in query_lower:
                    suggestions.append(IndexSuggestion(
                        table_name='conversations',
                        column_names=['timestamp'],
                        index_type='BTREE',
                        reason='时间范围查询优化',
                        estimated_improvement=25.0,
                        priority='MEDIUM'
                    ))
            
            if 'order by' in query_lower and 'timestamp' in query_lower:
                suggestions.append(IndexSuggestion(
                    table_name='conversations',
                    column_names=['timestamp'],
                    index_type='BTREE',
                    reason='排序优化',
                    estimated_improvement=20.0,
                    priority='MEDIUM'
                ))
        
        # 去重
        unique_suggestions = []
        seen = set()
        
        for suggestion in suggestions:
            key = (suggestion.table_name, tuple(suggestion.column_names))
            if key not in seen:
                seen.add(key)
                unique_suggestions.append(suggestion)
        
        return unique_suggestions
    
    def get_index_recommendations(self, db: Session = None, table_name: Optional[str] = None) -> List[IndexSuggestion]:
        """获取索引建议"""
        suggestions = self.suggest_indexes()
        
        if table_name:
            suggestions = [s for s in suggestions if s.table_name == table_name]
        
        return suggestions
    
    def generate_optimization_report(self, db: Session = None) -> Dict[str, Any]:
        """生成优化报告"""
        return self.get_performance_report()
    
    def check_existing_indexes(self) -> Dict[str, List[str]]:
        """检查现有索引"""
        indexes = {}
        
        try:
            with engine.connect() as conn:
                # 获取所有表
                inspector = inspect(engine)
                tables = inspector.get_table_names()
                
                for table in tables:
                    table_indexes = inspector.get_indexes(table)
                    indexes[table] = []
                    
                    for index in table_indexes:
                        index_info = f"{index['name']}: {', '.join(index['column_names'])}"
                        if index.get('unique'):
                            index_info += " (UNIQUE)"
                        indexes[table].append(index_info)
        
        except Exception as e:
            logger.error(f"检查索引时出错: {e}")
        
        return indexes
    
    def optimize_query(self, query: str) -> Tuple[str, List[str]]:
        """优化查询建议"""
        suggestions = []
        optimized_query = query
        
        query_lower = query.lower()
        
        # 检查是否使用了SELECT *
        if 'select *' in query_lower:
            suggestions.append("避免使用SELECT *，只选择需要的列")
        
        # 检查是否有LIMIT
        if 'select' in query_lower and 'limit' not in query_lower and 'count' not in query_lower:
            suggestions.append("考虑添加LIMIT子句限制返回结果数量")
        
        # 检查JOIN优化
        if 'join' in query_lower:
            suggestions.append("确保JOIN条件列上有索引")
        
        # 检查WHERE子句
        if 'where' in query_lower:
            if 'like' in query_lower and query_lower.count('%') > 0:
                suggestions.append("LIKE查询以%开头会导致全表扫描，考虑使用全文搜索")
        
        # 检查ORDER BY
        if 'order by' in query_lower and 'limit' in query_lower:
            suggestions.append("ORDER BY + LIMIT查询确保排序列有索引")
        
        return optimized_query, suggestions
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        if not self.query_stats:
            return {"message": "暂无查询统计数据"}
        
        total_queries = len(self.query_stats)
        total_time = sum(stat.execution_time for stat in self.query_stats)
        avg_time = total_time / total_queries if total_queries > 0 else 0
        
        slow_queries_count = sum(1 for stat in self.query_stats 
                               if stat.execution_time > self.slow_query_threshold)
        
        return {
            "summary": {
                "total_queries": total_queries,
                "total_execution_time": round(total_time, 3),
                "average_execution_time": round(avg_time, 3),
                "slow_queries_count": slow_queries_count,
                "slow_queries_percentage": round((slow_queries_count / total_queries) * 100, 2) if total_queries > 0 else 0
            },
            "slow_queries": self.analyze_slow_queries(days=1),
            "table_usage": self.analyze_table_usage(),
            "index_suggestions": self.suggest_indexes(),
            "existing_indexes": self.check_existing_indexes()
        }
    
    def export_report(self, filepath: Optional[str] = None) -> str:
        """导出性能报告"""
        if not filepath:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filepath = f"data/reports/db_performance_report_{timestamp}.json"
        
        # 确保目录存在
        Path(filepath).parent.mkdir(parents=True, exist_ok=True)
        
        report = self.get_performance_report()
        
        import json
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info(f"性能报告已导出到: {filepath}")
        return filepath


# 全局优化器实例
_optimizer_instance: Optional[DatabaseOptimizer] = None


def get_database_optimizer() -> DatabaseOptimizer:
    """获取数据库优化器实例"""
    global _optimizer_instance
    if _optimizer_instance is None:
        _optimizer_instance = DatabaseOptimizer()
    return _optimizer_instance


def record_query_performance(query: str, execution_time: float, 
                            table_name: Optional[str] = None,
                            operation_type: Optional[str] = None,
                            rows_affected: Optional[int] = None):
    """记录查询性能（便捷函数）"""
    optimizer = get_database_optimizer()
    optimizer.record_query(query, execution_time, table_name, operation_type, rows_affected)


class QueryProfiler:
    """查询性能分析器（装饰器）"""
    
    def __init__(self, table_name: Optional[str] = None, operation_type: Optional[str] = None):
        self.table_name = table_name
        self.operation_type = operation_type
    
    def __call__(self, func):
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                
                # 记录性能
                record_query_performance(
                    query=func.__name__,
                    execution_time=execution_time,
                    table_name=self.table_name,
                    operation_type=self.operation_type
                )
                
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                record_query_performance(
                    query=f"{func.__name__} (ERROR)",
                    execution_time=execution_time,
                    table_name=self.table_name,
                    operation_type=self.operation_type
                )
                raise
        
        return wrapper


if __name__ == "__main__":
    # 测试代码
    optimizer = get_database_optimizer()
    
    # 模拟一些查询
    optimizer.record_query(
        "SELECT * FROM conversations WHERE session_id = ?",
        1.5,
        "conversations",
        "SELECT"
    )
    
    # 生成报告
    report = optimizer.get_performance_report()
    print("数据库性能报告:")
    print(f"总查询数: {report['summary']['total_queries']}")
    print(f"平均执行时间: {report['summary']['average_execution_time']}s")
    print(f"慢查询数: {report['summary']['slow_queries_count']}")