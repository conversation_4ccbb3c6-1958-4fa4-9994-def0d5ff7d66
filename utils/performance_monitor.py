"""性能监控模块"""

import time
import threading
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from datetime import datetime
from collections import defaultdict, deque
from loguru import logger


@dataclass
class RequestMetrics:
    """请求指标"""
    endpoint: str
    method: str
    start_time: float
    end_time: Optional[float] = None
    duration: Optional[float] = None
    status_code: Optional[int] = None
    success: bool = True
    error_message: Optional[str] = None


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        self.start_time = time.time()
        self.requests: deque = deque(maxlen=max_history)
        self.active_requests: Dict[str, RequestMetrics] = {}
        self.stats: Dict[str, Any] = defaultdict(int)
        self._lock = threading.Lock()
        
    def start_request(self, request_id: str, endpoint: str = "", method: str = "GET"):
        """开始记录请求"""
        with self._lock:
            metrics = RequestMetrics(
                endpoint=endpoint,
                method=method,
                start_time=time.time()
            )
            self.active_requests[request_id] = metrics
            
    def end_request(self, request_id: str, duration: Optional[float] = None, status_code: int = 200):
        """结束记录请求"""
        with self._lock:
            if request_id in self.active_requests:
                metrics = self.active_requests.pop(request_id)
                metrics.end_time = time.time()
                metrics.duration = duration or (metrics.end_time - metrics.start_time)
                metrics.status_code = status_code
                metrics.success = 200 <= status_code < 400
                
                self.requests.append(metrics)
                self._update_stats(metrics)
                
    def record_request(self, endpoint: str, method: str, duration: float, status_code: int):
        """直接记录请求"""
        with self._lock:
            metrics = RequestMetrics(
                endpoint=endpoint,
                method=method,
                start_time=time.time() - duration,
                end_time=time.time(),
                duration=duration,
                status_code=status_code,
                success=200 <= status_code < 400
            )
            self.requests.append(metrics)
            self._update_stats(metrics)
            
    def _update_stats(self, metrics: RequestMetrics):
        """更新统计信息"""
        self.stats['total_requests'] += 1
        if metrics.success:
            self.stats['successful_requests'] += 1
        else:
            self.stats['failed_requests'] += 1
            
        self.stats['total_duration'] += metrics.duration or 0
        
        # 按端点统计
        endpoint_key = f"endpoint_{metrics.endpoint}"
        self.stats[endpoint_key] += 1
        
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            total_requests = self.stats['total_requests']
            if total_requests == 0:
                return {
                    'total_requests': 0,
                    'successful_requests': 0,
                    'failed_requests': 0,
                    'success_rate': 0.0,
                    'average_duration': 0.0,
                    'uptime_seconds': time.time() - self.start_time
                }
                
            return {
                'total_requests': total_requests,
                'successful_requests': self.stats['successful_requests'],
                'failed_requests': self.stats['failed_requests'],
                'success_rate': self.stats['successful_requests'] / total_requests * 100,
                'average_duration': self.stats['total_duration'] / total_requests,
                'uptime_seconds': time.time() - self.start_time
            }
        return None

    def reset(self):
        """重置统计信息"""
        with self._lock:
            self.requests.clear()
            self.active_requests.clear()
            self.stats.clear()
            self.start_time = time.time()
            
    def get_recent_requests(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取最近的请求记录"""
        with self._lock:
            recent = list(self.requests)[-limit:]
            return [
                {
                    'endpoint': req.endpoint,
                    'method': req.method,
                    'duration': req.duration,
                    'status_code': req.status_code,
                    'success': req.success,
                    'timestamp': req.start_time
                }
                for req in recent
            ]
        return None