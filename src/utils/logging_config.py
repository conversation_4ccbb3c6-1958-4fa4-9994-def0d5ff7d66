import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional

from .structured_logging import (
    StructuredLoggingConfig,
    setup_structured_logging,
    get_structured_logger
)


def setup_logging(
        log_level: str = "INFO",
        log_file: Optional[str] = None,
        log_dir: Optional[str] = None,
        enable_console: bool = True,
        enable_file: bool = True,
        max_file_size: int = 10 * 1024 * 1024,  # 10MB
        backup_count: int = 5,
        log_format: Optional[str] = None
) -> logging.Logger:
    """
    设置全面的日志配置。
    
    参数:
        log_level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: 特定的日志文件名 (可选)
        log_dir: 日志文件目录 (默认为 logs/)
        enable_console: 是否启用控制台日志
        enable_file: 是否启用文件日志
        max_file_size: 每个日志文件的最大大小（字节）
        backup_count: 保留的备份文件数量
        log_format: 自定义日志格式字符串
        
    返回:
        配置好的根日志记录器
    """
    # Convert log level string to logging constant
    numeric_level = getattr(logging, log_level.upper(), logging.INFO)

    # Create root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(numeric_level)

    # Clear any existing handlers
    root_logger.handlers.clear()

    # Default log format
    if log_format is None:
        log_format = (
            "%(asctime)s - %(name)s - %(levelname)s - "
            "[%(filename)s:%(lineno)d] - %(message)s"
        )

    formatter = logging.Formatter(log_format)

    # Console handler
    if enable_console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(numeric_level)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)

    # File handler
    if enable_file:
        # Setup log directory
        if log_dir is None:
            log_dir = Path.cwd() / "logs"
        else:
            log_dir = Path(log_dir)

        log_dir.mkdir(exist_ok=True)

        # Setup log file name
        if log_file is None:
            timestamp = datetime.now().strftime("%Y%m%d")
            log_file = f"llm_comparison_{timestamp}.log"

        log_file_path = log_dir / log_file

        # Rotating file handler
        file_handler = logging.handlers.RotatingFileHandler(
            log_file_path,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(numeric_level)
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)

    # 记录配置信息
    root_logger.info(f"日志已配置 - 级别: {log_level}, 控制台: {enable_console}, 文件: {enable_file}")
    if enable_file:
        root_logger.info(f"日志文件: {log_file_path}")

    return root_logger


def get_logger(name: str) -> logging.Logger:
    """
    获取指定名称的日志记录器。
    
    参数:
        name: 日志记录器名称（通常为 __name__）
        
    返回:
        日志记录器实例
    """
    return logging.getLogger(name)


def configure_third_party_loggers(level: str = "WARNING") -> None:
    """
    配置第三方库的日志记录器以减少噪音。
    
    参数:
        level: 第三方日志记录器的日志级别
    """
    third_party_loggers = [
        'requests',
        'urllib3',
        'httpx',
        'aiohttp',
        'asyncio'
    ]

    numeric_level = getattr(logging, level.upper(), logging.WARNING)

    for logger_name in third_party_loggers:
        logger = logging.getLogger(logger_name)
        logger.setLevel(numeric_level)


def setup_performance_logging() -> logging.Logger:
    """
    为性能指标设置单独的日志记录器。
    
    返回:
        性能日志记录器实例
    """
    perf_logger = logging.getLogger('performance')
    perf_logger.setLevel(logging.INFO)

    # Create logs directory if it doesn't exist
    log_dir = Path.cwd() / "logs"
    log_dir.mkdir(exist_ok=True)

    # Performance log file
    perf_file = log_dir / "performance.log"

    # File handler for performance logs
    perf_handler = logging.handlers.RotatingFileHandler(
        perf_file,
        maxBytes=5 * 1024 * 1024,  # 5MB
        backupCount=3,
        encoding='utf-8'
    )

    # Simple format for performance logs
    perf_formatter = logging.Formatter(
        "%(asctime)s - %(levelname)s - %(message)s"
    )
    perf_handler.setFormatter(perf_formatter)

    perf_logger.addHandler(perf_handler)
    perf_logger.propagate = False  # Don't propagate to root logger

    return perf_logger


def log_function_call(func_name: str, args: dict, logger: Optional[logging.Logger] = None) -> None:
    """
    记录函数调用详情以便调试。
    
    参数:
        func_name: 被调用的函数名称
        args: 函数参数
        logger: 日志记录器实例（如果为None则使用根日志记录器）
    """
    if logger is None:
        logger = logging.getLogger()

    # Filter sensitive information
    safe_args = {}
    sensitive_keys = ['api_key', 'password', 'token', 'secret']

    for key, value in args.items():
        if any(sensitive in key.lower() for sensitive in sensitive_keys):
            safe_args[key] = "[REDACTED]"
        else:
            safe_args[key] = value

    logger.debug(f"Calling {func_name} with args: {safe_args}")


def log_api_request(provider: str, model: str, request_size: int, logger: Optional[logging.Logger] = None) -> None:
    """
    记录API请求详情。
    
    参数:
        provider: API提供商名称
        model: 模型名称
        request_size: 请求大小（字符数）
        logger: 日志记录器实例（如果为None则使用根日志记录器）
    """
    if logger is None:
        logger = logging.getLogger()

    logger.info(f"API请求 - 提供商: {provider}, 模型: {model}, 大小: {request_size} 字符")


def log_api_response(provider: str, model: str, response_size: int, duration: float,
                     logger: Optional[logging.Logger] = None) -> None:
    """
    记录API响应详情。
    
    参数:
        provider: API提供商名称
        model: 模型名称
        response_size: 响应大小（字符数）
        duration: 请求持续时间（秒）
        logger: 日志记录器实例（如果为None则使用根日志记录器）
    """
    if logger is None:
        logger = logging.getLogger()

    logger.info(f"API响应 - 提供商: {provider}, 模型: {model}, 大小: {response_size} 字符, 持续时间: {duration:.2f}秒")


def log_error_with_context(error: Exception, context: dict, logger: Optional[logging.Logger] = None) -> None:
    """
    记录错误及其附加上下文信息。
    
    参数:
        error: 发生的异常
        context: 附加的上下文信息
        logger: 日志记录器实例（如果为None则使用根日志记录器）
    """
    if logger is None:
        logger = logging.getLogger()

    # Filter sensitive information from context
    safe_context = {}
    sensitive_keys = ['api_key', 'password', 'token', 'secret']

    for key, value in context.items():
        if any(sensitive in key.lower() for sensitive in sensitive_keys):
            safe_context[key] = "[REDACTED]"
        else:
            safe_context[key] = value

    logger.error(f"错误: {error.__class__.__name__}: {error}")
    logger.error(f"上下文: {safe_context}")
    logger.error("堆栈跟踪:", exc_info=True)


class LoggingConfig:
    """
    日志设置的配置类。
    """

    def __init__(self):
        self.log_level = os.getenv('LOG_LEVEL', 'INFO')
        self.log_dir = os.getenv('LOG_DIR', 'logs')
        self.enable_console = os.getenv('LOG_CONSOLE', 'true').lower() == 'true'
        self.enable_file = os.getenv('LOG_FILE', 'true').lower() == 'true'
        self.max_file_size = int(os.getenv('LOG_MAX_SIZE', '10485760'))  # 10MB
        self.backup_count = int(os.getenv('LOG_BACKUP_COUNT', '5'))

    def setup(self) -> logging.Logger:
        """
        使用当前配置设置日志。
        
        返回:
            配置好的根日志记录器
        """
        return setup_logging(
            log_level=self.log_level,
            log_dir=self.log_dir,
            enable_console=self.enable_console,
            enable_file=self.enable_file,
            max_file_size=self.max_file_size,
            backup_count=self.backup_count
        )


# Default logging configuration
def init_default_logging(use_structured: bool = False) -> logging.Logger:
    """
    初始化默认日志配置。
    
    参数:
        use_structured: 是否使用结构化JSON日志
        
    返回:
        配置好的根日志记录器
    """
    config = LoggingConfig()

    if use_structured:
        structured_config = StructuredLoggingConfig(
            log_dir=config.log_dir,
            app_name="llm_comparison_tool",
            log_level=config.log_level,
            enable_console=config.enable_console,
            enable_file=config.enable_file,
            enable_json=True,
            max_file_size=config.max_file_size,
            backup_count=config.backup_count
        )
        logger = setup_structured_logging(structured_config)
    else:
        logger = config.setup()

    # Configure third-party loggers
    configure_third_party_loggers()

    return logger


def get_structured_logger_with_context(name: str, **context):
    """
    获取带有上下文的结构化日志记录器。
    
    参数:
        name: 日志记录器名称
        **context: 要包含在所有日志消息中的附加上下文
    
    返回:
        结构化日志记录器实例
    """
    return get_structured_logger(name, context)


def init_structured_logging():
    """
    使用默认配置初始化结构化日志。
    
    返回:
        StructuredLoggingConfig实例
    """
    return init_default_logging(use_structured=True)
