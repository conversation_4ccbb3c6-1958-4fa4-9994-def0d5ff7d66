import time
from functools import wraps
from typing import Type, Callable, Any, Optional, Union, Tuple

import requests
from utils.logger import logger


class RetryException(Exception):
    """当所有重试尝试都失败时抛出的异常。"""

    def __init__(self, original_exception: Exception, attempts: int):
        self.original_exception = original_exception
        self.attempts = attempts
        super().__init__(f"在 {attempts} 次尝试后失败。最后错误: {original_exception}")


def retry(
        exceptions: Union[Type[Exception], Tuple[Type[Exception], ...]] = (requests.RequestException, TimeoutError),
        max_retries: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        backoff_factor: float = 2.0,
        jitter: bool = True,
        logger_name: Optional[str] = None,
) -> Callable:
    """
    带有指数退避策略的重试装饰器，用于可能引发指定异常的函数。
    
    参数:
        exceptions: 要捕获并重试的异常或异常元组
        max_retries: 最大重试次数
        base_delay: 重试之间的初始延迟（秒）
        max_delay: 重试之间的最大延迟（秒）
        backoff_factor: 指数退避的乘数因子
        jitter: 是否为延迟时间添加随机性
        logger_name: 要使用的日志记录器名称，默认为模块日志记录器
    
    返回:
        装饰后的函数
    
    示例:
        @retry(max_retries=3, base_delay=2)
        def api_call(url):
            return requests.get(url, timeout=10)
    """
    local_logger = logger  # Use loguru logger directly

    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            last_exception = None

            for attempt in range(max_retries + 1):  # +1 for the initial attempt
                try:
                    if attempt > 0:
                        # Calculate delay with exponential backoff
                        delay = min(base_delay * (backoff_factor ** (attempt - 1)), max_delay)

                        # Add jitter if enabled (±20%)
                        if jitter:
                            import random
                            delay = delay * (1 + random.uniform(-0.2, 0.2))

                        local_logger.warning(
                            f"为 {func.__name__} 进行第 {attempt}/{max_retries} 次重试，"
                            f"延迟 {delay:.2f}秒。上一个错误: {last_exception}"
                        )
                        time.sleep(delay)

                    return func(*args, **kwargs)

                except exceptions as e:
                    last_exception = e

                    # If this was the last attempt, raise a RetryException
                    if attempt == max_retries:
                        local_logger.error(
                            f"{func.__name__} 的所有 {max_retries} 次重试尝试均失败。"
                            f"最后错误: {e}"
                        )
                        raise RetryException(original_exception=e, attempts=max_retries + 1)

            # This should never be reached due to the exception handling above
            return None

        return wrapper

    return decorator
