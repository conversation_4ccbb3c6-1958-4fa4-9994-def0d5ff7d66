import threading
from collections import defaultdict, deque
from contextlib import contextmanager
from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List

import psutil
from utils.logger import logger


@dataclass
class APICallMetrics:
    """Metrics for a single API call."""
    provider: str
    model: str
    start_time: datetime
    end_time: Optional[datetime] = None
    duration: Optional[float] = None
    request_size: int = 0
    response_size: int = 0
    success: bool = True
    error_message: Optional[str] = None
    retry_count: int = 0

    def finish(self, success: bool = True, error_message: Optional[str] = None, retry_count: int = 0):
        """Mark the API call as finished."""
        self.end_time = datetime.now()
        self.duration = (self.end_time - self.start_time).total_seconds()
        self.success = success
        self.error_message = error_message
        self.retry_count = retry_count


@dataclass
class SystemMetrics:
    """System performance metrics."""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    disk_usage_percent: float
    network_sent_mb: float = 0.0
    network_recv_mb: float = 0.0


@dataclass
class PerformanceStats:
    """Aggregated performance statistics."""
    total_calls: int = 0
    successful_calls: int = 0
    failed_calls: int = 0
    total_duration: float = 0.0
    avg_duration: float = 0.0
    min_duration: float = float('inf')
    max_duration: float = 0.0
    total_request_size: int = 0
    total_response_size: int = 0
    avg_request_size: float = 0.0
    avg_response_size: float = 0.0
    error_rate: float = 0.0
    calls_per_minute: float = 0.0

    def update(self, metrics: List[APICallMetrics]):
        """Update stats from a list of metrics."""
        if not metrics:
            return

        self.total_calls = len(metrics)
        self.successful_calls = sum(1 for m in metrics if m.success)
        self.failed_calls = self.total_calls - self.successful_calls

        durations = [m.duration for m in metrics if m.duration is not None]
        if durations:
            self.total_duration = sum(durations)
            self.avg_duration = self.total_duration / len(durations)
            self.min_duration = min(durations)
            self.max_duration = max(durations)

        self.total_request_size = sum(m.request_size for m in metrics)
        self.total_response_size = sum(m.response_size for m in metrics)

        if self.total_calls > 0:
            self.avg_request_size = self.total_request_size / self.total_calls
            self.avg_response_size = self.total_response_size / self.total_calls
            self.error_rate = self.failed_calls / self.total_calls

        # Calculate calls per minute
        if metrics:
            time_span = (metrics[-1].start_time - metrics[0].start_time).total_seconds() / 60
            if time_span > 0:
                self.calls_per_minute = self.total_calls / time_span


class PerformanceMonitor:
    """Monitor and track performance metrics."""

    def __init__(self, max_history: int = 1000, system_monitoring: bool = True):
        self.max_history = max_history
        self.system_monitoring = system_monitoring

        # API call metrics
        self.api_metrics: deque[APICallMetrics] = deque(maxlen=max_history)
        self.provider_metrics: Dict[str, deque[APICallMetrics]] = defaultdict(lambda: deque(maxlen=max_history))
        self.model_metrics: Dict[str, deque[APICallMetrics]] = defaultdict(lambda: deque(maxlen=max_history))

        # 系统指标
        self.system_metrics: deque[SystemMetrics] = deque(maxlen=max_history)
        self._system_monitor_thread: Optional[threading.Thread] = None
        self._stop_monitoring = threading.Event()

        # 性能统计缓存
        self._stats_cache: Dict[str, PerformanceStats] = {}
        self._cache_timestamp = datetime.now()
        self._cache_ttl = timedelta(seconds=30)  # Cache for 30 seconds

        # 如果启用，开始系统监控
        if self.system_monitoring:
            self.start_system_monitoring()

    def start_api_call(self, provider: str, model: str, request_size: int = 0) -> APICallMetrics:
        """开始跟踪API调用。"""
        metrics = APICallMetrics(
            provider=provider,
            model=model,
            start_time=datetime.now(),
            request_size=request_size
        )

        logger.debug(f"开始跟踪API调用: {provider}/{model}")
        return metrics

    def finish_api_call(
            self,
            metrics: APICallMetrics,
            response_size: int = 0,
            success: bool = True,
            error_message: Optional[str] = None,
            retry_count: int = 0
    ):
        """完成API调用跟踪。"""
        metrics.response_size = response_size
        metrics.finish(success=success, error_message=error_message, retry_count=retry_count)

        # 存储指标
        self.api_metrics.append(metrics)
        self.provider_metrics[metrics.provider].append(metrics)
        self.model_metrics[f"{metrics.provider}/{metrics.model}"].append(metrics)

        # 清除统计缓存
        self._invalidate_cache()

        logger.debug(
            f"完成API调用跟踪: {metrics.provider}/{metrics.model} - "
            f"持续时间: {metrics.duration:.2f}秒, 成功: {metrics.success}"
        )

    @contextmanager
    def track_api_call(self, provider: str, model: str, request_size: int = 0):
        """用于跟踪API调用的上下文管理器。"""
        metrics = self.start_api_call(provider, model, request_size)
        try:
            yield metrics
            self.finish_api_call(metrics, success=True)
        except Exception as e:
            self.finish_api_call(metrics, success=False, error_message=str(e))
            raise

    def record_system_metrics(self):
        """记录当前系统指标。"""
        try:
            # 获取网络统计信息
            net_io = psutil.net_io_counters()

            metrics = SystemMetrics(
                timestamp=datetime.now(),
                cpu_percent=psutil.cpu_percent(interval=None),
                memory_percent=psutil.virtual_memory().percent,
                memory_used_mb=psutil.virtual_memory().used / (1024 * 1024),
                disk_usage_percent=psutil.disk_usage('/').percent,
                network_sent_mb=net_io.bytes_sent / (1024 * 1024),
                network_recv_mb=net_io.bytes_recv / (1024 * 1024)
            )

            self.system_metrics.append(metrics)

        except Exception as e:
            logger.warning(f"记录系统指标失败: {e}")

    def start_system_monitoring(self, interval: float = 10.0):
        """启动后台系统监控。"""
        if self._system_monitor_thread and self._system_monitor_thread.is_alive():
            return

        def monitor_loop():
            while not self._stop_monitoring.wait(interval):
                self.record_system_metrics()

        self._system_monitor_thread = threading.Thread(target=monitor_loop, daemon=True)
        self._system_monitor_thread.start()
        logger.info(f"已启动系统监控，间隔为{interval}秒")

    def stop_system_monitoring(self):
        """停止后台系统监控。"""
        self._stop_monitoring.set()
        if self._system_monitor_thread:
            self._system_monitor_thread.join(timeout=5.0)
        logger.info("已停止系统监控")

    def _invalidate_cache(self):
        """使统计缓存失效。"""
        self._stats_cache.clear()
        self._cache_timestamp = datetime.now()

    def _is_cache_valid(self) -> bool:
        """检查统计缓存是否仍然有效。"""
        return datetime.now() - self._cache_timestamp < self._cache_ttl

    def get_overall_stats(self) -> PerformanceStats:
        """获取总体性能统计信息。"""
        cache_key = "overall"

        if cache_key in self._stats_cache and self._is_cache_valid():
            return self._stats_cache[cache_key]

        stats = PerformanceStats()
        stats.update(list(self.api_metrics))

        self._stats_cache[cache_key] = stats
        return stats

    def get_provider_stats(self, provider: str) -> PerformanceStats:
        """获取特定提供商的性能统计信息。"""
        cache_key = f"provider_{provider}"

        if cache_key in self._stats_cache and self._is_cache_valid():
            return self._stats_cache[cache_key]

        stats = PerformanceStats()
        stats.update(list(self.provider_metrics[provider]))

        self._stats_cache[cache_key] = stats
        return stats

    def get_model_stats(self, provider: str, model: str) -> PerformanceStats:
        """获取特定模型的性能统计信息。"""
        model_key = f"{provider}/{model}"
        cache_key = f"model_{model_key}"

        if cache_key in self._stats_cache and self._is_cache_valid():
            return self._stats_cache[cache_key]

        stats = PerformanceStats()
        stats.update(list(self.model_metrics[model_key]))

        self._stats_cache[cache_key] = stats
        return stats

    def get_recent_system_metrics(self, minutes: int = 10) -> List[SystemMetrics]:
        """获取最近N分钟的系统指标。"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        return [m for m in self.system_metrics if m.timestamp >= cutoff_time]

    def get_error_summary(self) -> Dict[str, int]:
        """获取按类型分类的错误摘要。"""
        error_counts = defaultdict(int)

        for metrics in self.api_metrics:
            if not metrics.success and metrics.error_message:
                # 从错误消息中提取错误类型
                error_type = metrics.error_message.split(':')[
                    0] if ':' in metrics.error_message else metrics.error_message
                error_counts[error_type] += 1

        return dict(error_counts)

    def get_performance_report(self) -> Dict[str, Any]:
        """获取全面的性能报告。"""
        overall_stats = self.get_overall_stats()

        # 提供商细分
        provider_breakdown = {}
        for provider in self.provider_metrics.keys():
            provider_breakdown[provider] = self.get_provider_stats(provider)

        # 最近的系统指标
        recent_system = self.get_recent_system_metrics(10)
        avg_cpu = sum(m.cpu_percent for m in recent_system) / len(recent_system) if recent_system else 0
        avg_memory = sum(m.memory_percent for m in recent_system) / len(recent_system) if recent_system else 0

        return {
            'overall_stats': overall_stats,
            'provider_breakdown': provider_breakdown,
            'error_summary': self.get_error_summary(),
            'system_metrics': {
                'avg_cpu_percent': avg_cpu,
                'avg_memory_percent': avg_memory,
                'current_memory_mb': recent_system[-1].memory_used_mb if recent_system else 0
            },
            'monitoring_period': {
                'start_time': self.api_metrics[0].start_time if self.api_metrics else None,
                'end_time': self.api_metrics[-1].start_time if self.api_metrics else None,
                'total_calls': len(self.api_metrics)
            }
        }

    def log_performance_summary(self):
        """记录性能摘要。"""
        stats = self.get_overall_stats()

        logger.info("=== 性能摘要 ===")
        logger.info(f"API调用总数: {stats.total_calls}")
        logger.info(f"成功率: {(1 - stats.error_rate) * 100:.1f}%")
        logger.info(f"平均持续时间: {stats.avg_duration:.2f}秒")
        logger.info(f"每分钟调用次数: {stats.calls_per_minute:.1f}")

        if stats.total_calls > 0:
            logger.info(f"平均请求大小: {stats.avg_request_size:.0f} 字符")
            logger.info(f"平均响应大小: {stats.avg_response_size:.0f} 字符")

        # 如果有错误，记录错误摘要
        error_summary = self.get_error_summary()
        if error_summary:
            logger.warning("错误摘要:")
            for error_type, count in error_summary.items():
                logger.warning(f"  {error_type}: {count}")

    def cleanup(self):
        """清理资源。"""
        self.stop_system_monitoring()
        logger.info("性能监控已清理")


# 全局性能监控实例
_performance_monitor: Optional[PerformanceMonitor] = None


def get_performance_monitor() -> PerformanceMonitor:
    """获取全局性能监控实例。"""
    global _performance_monitor
    if _performance_monitor is None:
        _performance_monitor = PerformanceMonitor()
    return _performance_monitor


def init_performance_monitoring(max_history: int = 1000, system_monitoring: bool = True) -> PerformanceMonitor:
    """初始化全局性能监控。"""
    global _performance_monitor
    if _performance_monitor is not None:
        _performance_monitor.cleanup()

    _performance_monitor = PerformanceMonitor(max_history=max_history, system_monitoring=system_monitoring)
    logger.info("性能监控已初始化")
    return _performance_monitor


def cleanup_performance_monitoring():
    """清理全局性能监控。"""
    global _performance_monitor
    if _performance_monitor is not None:
        _performance_monitor.cleanup()
        _performance_monitor = None
