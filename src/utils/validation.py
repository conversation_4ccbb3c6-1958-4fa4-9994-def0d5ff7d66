import os
import yaml
from utils.logger import logger
from typing import Dict, List, Any, Optional, Union
from pathlib import Path


class ValidationError(Exception):
    """验证错误的自定义异常。"""
    pass


class ConfigValidator:
    """
    验证配置文件和设置。
    """

    @staticmethod
    def validate_yaml_file(file_path: Union[str, Path]) -> Dict[str, Any]:
        """
        验证YAML文件存在并且可以被解析。
        
        参数:
            file_path: YAML文件的路径
            
        返回:
            解析后的YAML内容
            
        抛出:
            ValidationError: 如果文件不存在或无法解析
        """
        file_path = Path(file_path)

        if not file_path.exists():
            raise ValidationError(f"Configuration file not found: {file_path}")

        if not file_path.is_file():
            raise ValidationError(f"Path is not a file: {file_path}")

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = yaml.safe_load(f)

            if content is None:
                raise ValidationError(f"YAML file is empty: {file_path}")

            return content

        except yaml.YAMLError as e:
            raise ValidationError(f"Invalid YAML syntax in {file_path}: {e}")
        except Exception as e:
            raise ValidationError(f"Error reading {file_path}: {e}")

    @staticmethod
    def validate_models_config(config: Dict[str, Any]) -> None:
        """
        验证models.yaml配置结构。
        
        参数:
            config: 解析后的模型配置
            
        抛出:
            ValidationError: 如果配置无效
        """
        # 检查是否有顶级 'models' 键
        if 'models' not in config:
            raise ValidationError("Missing top-level 'models' key in models config")

        if not isinstance(config['models'], dict):
            raise ValidationError("'models' section must be a dictionary")

        models_config = config['models']
        required_sections = ['openai', 'gemini', 'doubao', 'qianwen']

        for section in required_sections:
            if section not in models_config:
                raise ValidationError(f"Missing required section '{section}' in models config")

            section_config = models_config[section]
            if not isinstance(section_config, dict):
                raise ValidationError(f"Section '{section}' must be a dictionary")

            # Validate required fields for each provider
            if section == 'openai':
                ConfigValidator._validate_openai_config(section_config)
            elif section == 'gemini':
                ConfigValidator._validate_gemini_config(section_config)
            elif section == 'doubao':
                ConfigValidator._validate_doubao_config(section_config)
            elif section == 'qianwen':
                ConfigValidator._validate_qianwen_config(section_config)

    @staticmethod
    def _validate_openai_config(config: Dict[str, Any]) -> None:
        """验证OpenAI配置。"""
        required_fields = ['name', 'api_key', 'base_url', 'available_models']

        for field in required_fields:
            if field not in config:
                raise ValidationError(f"Missing required field '{field}' in OpenAI config")

        if not isinstance(config['available_models'], dict) or not config['available_models']:
            raise ValidationError("OpenAI 'available_models' must be a non-empty dictionary")

    @staticmethod
    def _validate_gemini_config(config: Dict[str, Any]) -> None:
        """验证Gemini配置。"""
        required_fields = ['name', 'api_key', 'base_url', 'available_models']

        for field in required_fields:
            if field not in config:
                raise ValidationError(f"Missing required field '{field}' in Gemini config")

        if not isinstance(config['available_models'], dict) or not config['available_models']:
            raise ValidationError("Gemini 'available_models' must be a non-empty dictionary")

    @staticmethod
    def _validate_doubao_config(config: Dict[str, Any]) -> None:
        """验证豆包配置。"""
        required_fields = ['name', 'api_key', 'base_url', 'available_models']

        for field in required_fields:
            if field not in config:
                raise ValidationError(f"Missing required field '{field}' in Doubao config")

        if not isinstance(config['available_models'], dict) or not config['available_models']:
            raise ValidationError("Doubao 'available_models' must be a non-empty dictionary")

    @staticmethod
    def _validate_qianwen_config(config: Dict[str, Any]) -> None:
        """验证千问配置。"""
        required_fields = ['name', 'api_key', 'base_url', 'available_models']

        for field in required_fields:
            if field not in config:
                raise ValidationError(f"Missing required field '{field}' in Qianwen config")

        if not isinstance(config['available_models'], dict) or not config['available_models']:
            raise ValidationError("Qianwen 'available_models' must be a non-empty dictionary")

    @staticmethod
    def validate_active_models_config(config: Dict[str, Any]) -> None:
        """
        验证active_models.yaml配置结构。
        
        参数:
            config: 解析后的活动模型配置
            
        抛出:
            ValidationError: 如果配置无效
        """
        if 'active_models' not in config:
            raise ValidationError("Missing 'active_models' section in active models config")

        active_models = config['active_models']
        if not isinstance(active_models, list) or not active_models:
            raise ValidationError("'active_models' section must be a non-empty list")

        # Validate each model configuration
        for model_config in active_models:
            if not isinstance(model_config, dict):
                raise ValidationError("Model configuration must be a dictionary")

            required_fields = ['name', 'enabled']
            for field in required_fields:
                if field not in model_config:
                    raise ValidationError(f"Missing required field '{field}' in model configuration")

            # Validate enabled field
            if not isinstance(model_config['enabled'], bool):
                raise ValidationError(f"'enabled' field for model '{model_config['name']}' must be a boolean")

        # Validate settings if present
        if 'settings' in config:
            settings_config = config['settings']
            if not isinstance(settings_config, dict):
                raise ValidationError("'settings' section must be a dictionary")

            # Validate optional numeric parameters
            numeric_params = ['temperature', 'max_tokens', 'timeout']
            for param in numeric_params:
                if param in settings_config:
                    value = settings_config[param]
                    if not isinstance(value, (int, float)) or value < 0:
                        raise ValidationError(f"'{param}' in settings must be a non-negative number")

        # Validate conversation settings if present
        if 'conversation' in config:
            conv_config = config['conversation']
            if not isinstance(conv_config, dict):
                raise ValidationError("'conversation' section must be a dictionary")

            if 'max_history_per_model' in conv_config:
                max_history = conv_config['max_history_per_model']
                if not isinstance(max_history, int) or max_history < 0:
                    raise ValidationError("'max_history_per_model' must be a non-negative integer")

            if 'save_conversations' in conv_config:
                save_conv = conv_config['save_conversations']
                if not isinstance(save_conv, bool):
                    raise ValidationError("'save_conversations' must be a boolean")

            if 'auto_save_interval' in conv_config:
                auto_save = conv_config['auto_save_interval']
                if not isinstance(auto_save, int) or auto_save < 0:
                    raise ValidationError("'auto_save_interval' must be a non-negative integer")

    @staticmethod
    def validate_environment_variables(models_config: Dict[str, Any]) -> List[str]:
        """
        验证所需的环境变量已设置。
        
        参数:
            models_config: 解析后的模型配置
            
        返回:
            缺失的环境变量列表
        """
        missing_vars = []

        # 检查是否有顶级 'models' 键
        if 'models' not in models_config:
            return missing_vars  # 如果没有 models 键，直接返回空列表

        providers_config = models_config['models']
        if not isinstance(providers_config, dict):
            return missing_vars

        for provider, config in providers_config.items():
            if isinstance(config, dict) and 'api_key' in config:
                # 从 api_key 字段中提取环境变量名
                api_key_value = config['api_key']
                if isinstance(api_key_value, str) and api_key_value.startswith('${') and api_key_value.endswith('}'):
                    env_var = api_key_value[2:-1]  # 去除 ${} 获取环境变量名
                    if not os.getenv(env_var):
                        missing_vars.append(env_var)

        return missing_vars

    @staticmethod
    def validate_file_paths(base_path: Union[str, Path], prompts_dir: Optional[Union[str, Path]] = None) -> List[str]:
        """
        验证所需的配置文件存在。
        
        参数:
            base_path: 基础目录路径
            prompts_dir: 提示文件目录路径，默认为项目根目录下的 'prompts'
            
        返回:
            缺失的文件列表
        """
        base_path = Path(base_path)
        project_root = base_path.parent  # 假设 base_path 是 config 目录
        prompts_dir = Path(prompts_dir) if prompts_dir else project_root / 'prompts'

        config_files = [
            'models_optimized.yaml',
            'active_models.yaml'
        ]

        prompt_files = [
            'system_prompts.md',
            'templates.md'
        ]

        missing_files = []

        # 检查配置文件
        for file_name in config_files:
            file_path = base_path / file_name
            if not file_path.exists():
                missing_files.append(str(file_path))

        # 检查提示文件
        for file_name in prompt_files:
            file_path = prompts_dir / file_name
            if not file_path.exists():
                missing_files.append(str(file_path))

        return missing_files

    @staticmethod
    def validate_prompt_files(file_path: Union[str, Path]) -> None:
        """
        验证提示文件的markdown结构。
        
        参数:
            file_path: markdown文件的路径
            
        抛出:
            ValidationError: 如果文件结构无效
        """
        file_path = Path(file_path)

        if not file_path.exists():
            raise ValidationError(f"Prompt file not found: {file_path}")

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            if not content.strip():
                raise ValidationError(f"Prompt file is empty: {file_path}")

            # Check for at least one section header
            if '##' not in content:
                raise ValidationError(f"Prompt file must contain at least one section header (##): {file_path}")

        except Exception as e:
            raise ValidationError(f"Error reading prompt file {file_path}: {e}")

    @staticmethod
    def validate_enabled_models_have_config(active_models_config: Dict[str, Any], models_config: Dict[str, Any]) -> \
            List[str]:
        """
        验证所有启用的模型都有对应的配置。
        
        参数:
            active_models_config: 解析后的活动模型配置
            models_config: 解析后的模型配置
            
        返回:
            没有对应配置的启用模型列表
        """
        missing_configs = []

        # 检查是否有顶级 'active_models' 键
        if 'active_models' not in active_models_config:
            return missing_configs

        # 检查是否有顶级 'models' 键
        if 'models' not in models_config:
            return missing_configs

        active_models = active_models_config['active_models']
        models = models_config['models']

        for model_config in active_models:
            if isinstance(model_config, dict) and model_config.get('enabled', False):
                model_name = model_config.get('name')
                if model_name:
                    # 检查模型别名是否存在于任何提供商的available_models中
                    found = False
                    for provider_name, provider_config in models.items():
                        if isinstance(provider_config, dict) and 'available_models' in provider_config:
                            available_models = provider_config['available_models']
                            if isinstance(available_models, dict):
                                for model_key, model_info in available_models.items():
                                    if isinstance(model_info, dict) and model_info.get('alias') == model_name:
                                        found = True
                                        break
                            if found:
                                break
                    if not found:
                        missing_configs.append(model_name)

        return missing_configs


def validate_all_configs(config_dir: Union[str, Path], prompts_dir: Optional[Union[str, Path]] = None) -> Dict[
    str, Any]:
    """
    验证所有配置文件并返回验证结果。
    
    参数:
        config_dir: 包含配置文件的目录
        prompts_dir: 包含提示文件的目录，默认为项目根目录下的 'prompts'
        
    返回:
        包含验证结果和任何错误的字典
    """
    config_dir = Path(config_dir)
    project_root = config_dir.parent  # 假设 config_dir 是 config 目录
    prompts_dir = Path(prompts_dir) if prompts_dir else project_root / 'prompts'

    results = {
        'valid': True,
        'errors': [],
        'warnings': [],
        'missing_files': [],
        'missing_env_vars': [],
        'missing_model_configs': []
    }

    try:
        # Check for missing files
        missing_files = ConfigValidator.validate_file_paths(config_dir, prompts_dir)
        if missing_files:
            results['missing_files'] = missing_files
            results['errors'].append(f"Missing configuration files: {', '.join(missing_files)}")
            results['valid'] = False

        # Validate models_optimized.yaml
        models_file = config_dir / 'models_optimized.yaml'
        models_config = None
        if models_file.exists():
            try:
                models_config = ConfigValidator.validate_yaml_file(models_file)
                ConfigValidator.validate_models_config(models_config)

                # Check environment variables
                missing_env_vars = ConfigValidator.validate_environment_variables(models_config)
                if missing_env_vars:
                    results['missing_env_vars'] = missing_env_vars
                    results['warnings'].append(f"Missing environment variables: {', '.join(missing_env_vars)}")

            except ValidationError as e:
                results['errors'].append(f"models_optimized.yaml validation error: {e}")
                results['valid'] = False

        # Validate active_models.yaml
        active_models_file = config_dir / 'active_models.yaml'
        active_models_config = None
        if active_models_file.exists():
            try:
                active_models_config = ConfigValidator.validate_yaml_file(active_models_file)
                ConfigValidator.validate_active_models_config(active_models_config)
            except ValidationError as e:
                results['errors'].append(f"active_models.yaml validation error: {e}")
                results['valid'] = False

        # 验证启用的模型是否有对应的配置
        if models_config and active_models_config:
            missing_model_configs = ConfigValidator.validate_enabled_models_have_config(active_models_config,
                                                                                        models_config)
            if missing_model_configs:
                results['missing_model_configs'] = missing_model_configs
                results['errors'].append(f"启用的模型没有对应的配置: {', '.join(missing_model_configs)}")
                results['valid'] = False

        # Validate prompt files
        prompt_files = ['system_prompts.md', 'templates.md']
        for prompt_file in prompt_files:
            file_path = prompts_dir / prompt_file
            if file_path.exists():
                try:
                    ConfigValidator.validate_prompt_files(file_path)
                except ValidationError as e:
                    results['errors'].append(f"{prompt_file} validation error: {e}")
                    results['valid'] = False

    except Exception as e:
        results['errors'].append(f"Unexpected validation error: {e}")
        results['valid'] = False
        logger.error(f"Validation error: {e}", exc_info=True)

    return results
