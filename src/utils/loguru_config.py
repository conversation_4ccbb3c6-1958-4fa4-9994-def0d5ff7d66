"""基于Loguru的LLM对比工具日志配置。"""

import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any

from utils.logger import logger


class LoguruConfig:
    """Loguru日志设置的配置类。"""

    def __init__(self):
        self.log_level = os.getenv('LOG_LEVEL', 'INFO')
        self.log_dir = Path(os.getenv('LOG_DIR', 'logs'))
        self.enable_console = os.getenv('LOG_CONSOLE', 'true').lower() == 'true'
        self.enable_file = os.getenv('LOG_FILE', 'true').lower() == 'true'
        self.max_file_size = os.getenv('LOG_MAX_SIZE', '10 MB')
        self.backup_count = int(os.getenv('LOG_BACKUP_COUNT', '5'))
        self.app_name = "llm_comparison_tool"

        # Create log directory
        self.log_dir.mkdir(exist_ok=True)


def setup_loguru(
        log_level: str = "INFO",
        log_dir: Optional[str] = None,
        enable_console: bool = True,
        enable_file: bool = True,
        enable_structured: bool = False,
        max_file_size: str = "10 MB",
        backup_count: int = 5,
        app_name: str = "llm_comparison_tool"
) -> None:
    """设置Loguru日志配置。
    
    参数:
        log_level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_dir: 日志文件目录 (默认为 logs/)
        enable_console: 是否启用控制台日志
        enable_file: 是否启用文件日志
        enable_structured: 是否使用结构化JSON日志
        max_file_size: 每个日志文件的最大大小
        backup_count: 保留的备份文件数量
        app_name: 日志文件的应用名称
    """
    # Remove default handler
    logger.remove()

    # Setup log directory
    if log_dir is None:
        log_dir = Path.cwd() / "logs"
    else:
        log_dir = Path(log_dir)
    log_dir.mkdir(exist_ok=True)

    # Console handler
    if enable_console:
        if enable_structured:
            console_format = "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} | {extra} | {message}"
        else:
            console_format = "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | <level>{message}</level>"

        logger.add(
            sys.stdout,
            format=console_format,
            level=log_level,
            colorize=not enable_structured,
            serialize=enable_structured
        )

    # File handlers
    if enable_file:
        # Main application log
        app_log_file = log_dir / f"{app_name}.log"
        if enable_structured:
            app_format = "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} | {extra} | {message}"
        else:
            app_format = "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} | {message}"

        logger.add(
            app_log_file,
            format=app_format,
            level=log_level,
            rotation=max_file_size,
            retention=backup_count,
            encoding='utf-8',
            serialize=enable_structured
        )

        # Error log (only ERROR and CRITICAL)
        error_log_file = log_dir / f"{app_name}_errors.log"
        logger.add(
            error_log_file,
            format=app_format,
            level="ERROR",
            rotation=max_file_size,
            retention=backup_count,
            encoding='utf-8',
            serialize=enable_structured
        )

        # Performance log (filtered by event_type)
        perf_log_file = log_dir / f"{app_name}_performance.log"
        logger.add(
            perf_log_file,
            format=app_format,
            level="INFO",
            rotation=max_file_size,
            retention=backup_count,
            encoding='utf-8',
            serialize=enable_structured,
            filter=lambda record: record["extra"].get("event_type") in ["api_call", "performance_metric"]
        )

    # Log the configuration
    logger.info(f"Loguru已配置 - 级别: {log_level}, 控制台: {enable_console}, 文件: {enable_file}")
    if enable_file:
        logger.info(f"日志目录: {log_dir}")


def get_logger(name: str) -> Any:
    """获取带有上下文的日志记录器实例。
    
    参数:
        name: 日志记录器名称 (通常为 __name__)
        
    返回:
        带有上下文的日志记录器实例
    """
    return logger.bind(name=name)


def get_structured_logger(name: str, context: Optional[Dict[str, Any]] = None) -> Any:
    """获取带有额外上下文的结构化日志记录器。
    
    参数:
        name: 日志记录器名称
        context: 要包含在所有日志消息中的额外上下文
        
    返回:
        带有上下文的日志记录器实例
    """
    if context is None:
        context = {}

    return logger.bind(name=name, **context)


def configure_third_party_loggers(level: str = "WARNING") -> None:
    """配置第三方库的日志记录器以减少噪音。
    
    参数:
        level: 第三方日志记录器的日志级别
    """
    import logging

    third_party_loggers = [
        'requests',
        'urllib3',
        'httpx',
        'aiohttp',
        'asyncio'
    ]

    numeric_level = getattr(logging, level.upper(), logging.WARNING)

    for logger_name in third_party_loggers:
        logging.getLogger(logger_name).setLevel(numeric_level)


def log_function_call(func_name: str, args: dict, logger_instance: Any = None) -> None:
    """记录函数调用详情以进行调试。
    
    参数:
        func_name: 被调用的函数名称
        args: 函数参数
        logger_instance: 日志记录器实例 (如果为None则使用默认日志记录器)
    """
    if logger_instance is None:
        logger_instance = logger

    # Filter sensitive information
    safe_args = {}
    sensitive_keys = ['api_key', 'password', 'token', 'secret']

    for key, value in args.items():
        if any(sensitive in key.lower() for sensitive in sensitive_keys):
            safe_args[key] = "[REDACTED]"
        else:
            safe_args[key] = value

    logger_instance.debug(f"Calling {func_name} with args: {safe_args}")


def log_api_request(provider: str, model: str, request_size: int, logger_instance: Any = None) -> None:
    """记录API请求详情。
    
    参数:
        provider: API提供商名称
        model: 模型名称
        request_size: 请求大小（字符数）
        logger_instance: 日志记录器实例（如果为None则使用默认日志记录器）
    """
    if logger_instance is None:
        logger_instance = logger

    logger_instance.bind(
        event_type="api_call",
        provider=provider,
        model=model,
        request_size=request_size
    ).info(f"API请求 - 提供商: {provider}, 模型: {model}, 大小: {request_size} 字符")


def log_api_response(provider: str, model: str, response_size: int, duration: float,
                     logger_instance: Any = None) -> None:
    """记录API响应详情。
    
    参数:
        provider: API提供商名称
        model: 模型名称
        response_size: 响应大小（字符数）
        duration: 请求持续时间（秒）
        logger_instance: 日志记录器实例（如果为None则使用默认日志记录器）
    """
    if logger_instance is None:
        logger_instance = logger

    logger_instance.bind(
        event_type="api_call",
        provider=provider,
        model=model,
        response_size=response_size,
        duration=duration
    ).info(f"API响应 - 提供商: {provider}, 模型: {model}, 大小: {response_size} 字符, 持续时间: {duration:.2f}秒")


def log_error_with_context(error: Exception, context: dict, logger_instance: Any = None) -> None:
    """记录带有额外上下文信息的错误。
    
    参数:
        error: 发生的异常
        context: 额外的上下文信息
        logger_instance: 日志记录器实例（如果为None则使用默认日志记录器）
    """
    if logger_instance is None:
        logger_instance = logger

    # Filter sensitive information from context
    safe_context = {}
    sensitive_keys = ['api_key', 'password', 'token', 'secret']

    for key, value in context.items():
        if any(sensitive in key.lower() for sensitive in sensitive_keys):
            safe_context[key] = "[REDACTED]"
        else:
            safe_context[key] = value

    logger_instance.bind(**safe_context).error(f"错误: {error.__class__.__name__}: {error}")


def log_performance_metric(metric_name: str, value: float, unit: str = None, logger_instance: Any = None, **kwargs):
    """记录性能指标。
    
    参数:
        metric_name: 指标名称
        value: 指标值
        unit: 计量单位
        logger_instance: 日志记录器实例（如果为None则使用默认日志记录器）
        **kwargs: 额外上下文
    """
    if logger_instance is None:
        logger_instance = logger

    logger_instance.bind(
        event_type="performance_metric",
        metric_name=metric_name,
        metric_value=value,
        metric_unit=unit,
        **kwargs
    ).info(f"性能指标: {metric_name} = {value}")


def log_user_interaction(action: str, logger_instance: Any = None, **kwargs):
    """记录用户交互。
    
    参数:
        action: 用户操作描述
        logger_instance: 日志记录器实例（如果为None则使用默认日志记录器）
        **kwargs: 额外上下文
    """
    if logger_instance is None:
        logger_instance = logger

    logger_instance.bind(
        event_type="user_interaction",
        action=action,
        **kwargs
    ).info(f"用户操作: {action}")


def init_default_logging(use_structured: bool = False) -> Any:
    """初始化默认日志配置。
    
    参数:
        use_structured: 是否使用结构化JSON日志
        
    返回:
        配置好的日志记录器
    """
    config = LoguruConfig()

    setup_loguru(
        log_level=config.log_level,
        log_dir=str(config.log_dir),
        enable_console=config.enable_console,
        enable_file=config.enable_file,
        enable_structured=use_structured,
        max_file_size=config.max_file_size,
        backup_count=config.backup_count,
        app_name=config.app_name
    )

    # 配置第三方日志记录器
    configure_third_party_loggers()

    return logger


def init_structured_logging() -> Any:
    """使用默认配置初始化结构化日志。
    
    返回:
        配置好的日志记录器
    """
    return init_default_logging(use_structured=True)


class TimedOperation:
    """用于计时操作并记录结果的上下文管理器。"""

    def __init__(self, logger_instance: Any, operation_name: str, **context):
        self.logger = logger_instance
        self.operation_name = operation_name
        self.context = context
        self.start_time = None

    def __enter__(self):
        self.start_time = datetime.now()
        self.logger.bind(
            event_type="operation_start",
            operation=self.operation_name,
            **self.context
        ).info(f"开始操作: {self.operation_name}")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        end_time = datetime.now()
        duration = (end_time - self.start_time).total_seconds()

        if exc_type is None:
            self.logger.bind(
                event_type="operation_complete",
                operation=self.operation_name,
                duration=duration,
                success=True,
                **self.context
            ).info(f"完成操作: {self.operation_name}")
        else:
            self.logger.bind(
                event_type="operation_failed",
                operation=self.operation_name,
                duration=duration,
                success=False,
                error_type=exc_type.__name__,
                error_message=str(exc_val),
                **self.context
            ).error(f"操作失败: {self.operation_name}")
