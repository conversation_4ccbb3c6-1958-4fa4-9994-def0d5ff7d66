import os
import re
from utils.logger import logger


class PromptManager:
    """
    管理从markdown文件加载和处理提示词。
    """

    def __init__(self, prompts_dir="prompts"):
        self.prompts_dir = prompts_dir
        self.system_prompts = self._load_prompts_from_file("system_prompts.md")
        self.templates = self._load_prompts_from_file("templates.md")

    def _load_prompts_from_file(self, filename: str) -> dict:
        """
        从单个markdown文件加载多个提示词。
        提示词由'##'标题分隔。
        """
        prompts = {}
        path = os.path.join(self.prompts_dir, filename)
        try:
            with open(path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Split by '##' headers
            sections = re.split(r'\n## ', content)
            for section in sections:
                if not section.strip():
                    continue

                lines = section.strip().split('\n')
                name = lines[0].strip()
                prompt_content = "\n".join(lines[1:]).strip()
                prompts[name] = prompt_content

            logger.info(f"从{filename}加载了{len(prompts)}个提示词。")
            return prompts
        except FileNotFoundError:
            logger.warning(f"未找到提示词文件: {path}")
            return {}

    def _replace_variables(self, text: str, variables: dict) -> str:
        """
        替换文本中像{{variable}}这样的占位符。
        还处理像{{#if var}}...{{/if}}这样的简单条件块。
        """
        if not variables:
            variables = {}

        # 处理条件块
        def handle_conditional(match):
            var_name = match.group(1)
            inner_text = match.group(2)
            return inner_text if variables.get(var_name) else ""

        text = re.sub(r'\{\{#if (\w+)\}\}(.*?)\{\{/if\}\}', handle_conditional, text, flags=re.DOTALL)

        # 处理简单变量替换
        for key, value in variables.items():
            text = text.replace(f"{{{{{key}}}}}", str(value))

        return text

    def get_system_prompt(self, name="default", variables=None) -> str:
        """
        通过名称获取系统提示词并替换变量。
        """
        prompt = self.system_prompts.get(name, self.system_prompts.get("default", ""))
        return self._replace_variables(prompt, variables)

    def get_template(self, name="simple") -> str:
        """
        通过名称获取对话模板。
        """
        return self.templates.get(name, self.templates.get("simple", ""))

    def build_final_prompt(self, template_name: str, system_prompt: str, history: str, user_input: str,
                           context: dict = None) -> str:
        """
        使用模板构建最终的提示词字符串。
        """
        template = self.get_template(template_name)

        replacements = {
            "system_prompt": system_prompt,
            "history": history,
            "user_input": user_input,
            "context": context or {}
        }

        return self._replace_variables(template, replacements)
