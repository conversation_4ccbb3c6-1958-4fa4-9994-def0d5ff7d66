from datetime import datetime
from utils.logger import logger
from model_manager import Model<PERSON>anager
from conversation_manager import ConversationManager
from prompt_manager import PromptManager


class ComparisonEngine:
    """
    编排模型比较过程。
    """

    def __init__(self, model_manager: <PERSON><PERSON>anager, conv_manager: Convers<PERSON><PERSON><PERSON>ger, prompt_manager: PromptManager):
        self.model_manager = model_manager
        self.conv_manager = conv_manager
        self.prompt_manager = prompt_manager

    def compare_models(self, user_input: str, system_prompt_name="default", variables=None):
        """
        比较所有活动模型对给定输入的响应。
        """
        active_models_config = self.model_manager.get_active_models_config()
        results = {}

        logger.info(f"开始比较{len(active_models_config)}个模型。")

        system_prompt = self.prompt_manager.get_system_prompt(system_prompt_name, variables)

        for model_config in active_models_config:
            model_name = model_config['name']

            try:
                # 获取模型实例
                try:
                    model_instance = self.model_manager.get_model_instance(model_name)
                except ValueError as config_error:
                    # 配置错误单独处理，提供更友好的错误信息
                    error_message = f"模型配置错误: {config_error}"
                    logger.error(f"模型{model_name}配置错误: {config_error}")
                    results[model_name] = {
                        "response": error_message,
                        "model_version": model_config.get('model', 'N/A'),
                        "status": "config_error",
                        "timestamp": datetime.now().isoformat()
                    }
                    continue

                # 获取独立的对话历史
                history = self.conv_manager.get_history(model_name, format_type="messages")

                # 构建消息载荷
                messages = [{"role": "system", "content": system_prompt}] if system_prompt else []
                messages.extend(history)
                messages.append({"role": "user", "content": user_input})

                # 获取模型特定参数
                global_settings = self.model_manager.config_manager.get_global_settings()
                params = {
                    "temperature": model_config.get("temperature", global_settings.get("temperature")),
                    "max_tokens": model_config.get("max_tokens", global_settings.get("max_tokens")),
                    "timeout": model_config.get("timeout", global_settings.get("timeout")),
                }

                # 调用模型API
                logger.info(f"查询{model_name}中...")
                response = model_instance.chat(messages, **params)

                # 更新历史记录
                self.conv_manager.add_message(model_name, "user", user_input)
                self.conv_manager.add_message(model_name, "assistant", response)

                results[model_name] = {
                    "response": response,
                    "model_version": model_config['model'],
                    "status": "success",
                    "timestamp": datetime.now().isoformat()
                }
                logger.info(f"已收到来自{model_name}的响应。")

            except Exception as e:
                logger.error(f"查询模型{model_name}时出错: {e}", exc_info=True)
                # 根据错误类型提供更友好的错误信息
                error_message = f"调用模型时出错: {e}"
                results[model_name] = {
                    "response": error_message,
                    "model_version": model_config.get('model', 'N/A'),
                    "status": "error",
                    "timestamp": datetime.now().isoformat()
                }

        # 保存所有对话
        self.conv_manager.save_all_conversations()

        # 保存详细对话信息
        self._save_detailed_conversation(user_input, system_prompt, results)

        return results

    def _save_detailed_conversation(self, user_input: str, system_prompt: str, results: dict):
        """保存详细的对话信息。"""
        try:
            # 构建模型响应数据
            model_responses = {}
            for model_name, result in results.items():
                model_responses[model_name] = {
                    "response": result["response"],
                    "model_version": result["model_version"],
                    "status": result["status"],
                    "timestamp": result["timestamp"]
                }

            # 添加详细对话记录
            self.conv_manager.add_detailed_conversation(
                user_input=user_input,
                prompt_used=system_prompt,
                model_responses=model_responses,
                metadata={
                    "total_models": len(results),
                    "successful_responses": len([r for r in results.values() if r["status"] == "success"]),
                    "failed_responses": len([r for r in results.values() if r["status"] != "success"])
                }
            )

            # 保存详细对话历史
            self.conv_manager.save_all_detailed_conversations()

        except Exception as e:
            logger.error(f"保存详细对话信息时出错: {e}", exc_info=True)
