import os
import yaml
from ..utils.config import ConfigManager
from loguru import logger
from ..models.base_model import BaseModel
from ..models.openai_model import OpenAIModel
from ..models.gemini_model import GeminiModel
from ..models.doubao_model import DoubaoModel
from ..models.qianwen_model import QianwenModel


class ModelManager:
    """
    管理模型的生命周期：加载、重新加载和提供实例。
    """
    MODEL_CLASSES = {
        "openai": OpenAIModel,
        "gemini": GeminiModel,
        "doubao": DoubaoModel,
        "qianwen": QianwenModel,
    }

    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.active_models_config_path = os.path.join(config_manager.config_dir, "active_models.yaml")
        self.active_models = []
        self.load_active_models()

    def load_active_models(self):
        """从配置中加载当前活动模型的列表。"""
        self.active_models = [
            model for model in self.config_manager.get_active_models()
            if model.get("enabled", False)
        ]
        logger.info(f"已加载{len(self.active_models)}个活动模型。")

    def get_active_models_config(self):
        """返回所有活动模型的配置。"""
        return self.active_models

    def get_all_models_config(self):
        """返回所有模型的配置，包括未激活的模型。"""
        return self.config_manager.get_active_models()

    def get_provider_configured_models(self, provider: str):
        """获取特定提供商的所有已配置模型。
        
        :param provider: 提供商名称（例如，'openai'）。
        :return: 该提供商的所有已配置模型列表。
        """
        return [model for model in self.config_manager.get_active_models()
                if model.get("provider") == provider]

    def get_model_instance(self, model_name: str) -> BaseModel:
        """
        创建模型的实例。
        
        :param model_name: 模型的配置名称（例如，'openai-gpt4'）。
        :return: 对应模型类的实例。
        :raises ValueError: 如果模型类型未知、配置不存在或API密钥缺失。
        """
        # 查找特定的活动模型配置
        active_model_config = next((m for m in self.active_models if m['name'] == model_name), None)
        if not active_model_config:
            raise ValueError(f"模型'{model_name}'未激活或未在active_models.yaml中配置")

        # 获取提供商和模型版本
        provider = active_model_config.get("provider")
        model_version = active_model_config.get("model")

        if provider not in self.MODEL_CLASSES:
            raise ValueError(f"未知的模型提供商: {provider}")

        base_config = self.config_manager.get_model_config(provider)
        if not base_config:
            raise ValueError(f"错误: 在models.yaml中未找到提供商'{provider}'的配置。请检查models.yaml文件或禁用此模型。")

        api_key = self.config_manager.get_api_key(provider)
        if not api_key:
            raise ValueError(f"未找到提供商'{provider}'的API密钥，请检查环境变量配置")

        base_url = base_config.get("base_url")

        # 验证模型版本是否在可用模型列表中
        available_models = base_config.get("available_models", [])
        if available_models and model_version not in available_models:
            raise ValueError(
                f"模型版本'{model_version}'不在'{provider}'的可用模型列表中。可用模型: {', '.join(available_models)}")

        model_class = self.MODEL_CLASSES[provider]
        return model_class(model_name=model_version, api_key=api_key, base_url=base_url)

    def _read_yaml_config(self):
        with open(self.active_models_config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
        return None

    def _write_yaml_config(self, data):
        with open(self.active_models_config_path, 'w', encoding='utf-8') as f:
            yaml.dump(data, f, allow_unicode=True)

    def _update_model_status(self, model_name: str, enabled: bool):
        config = self._read_yaml_config()
        model_found = False
        for model in config.get("active_models", []):
            if model['name'] == model_name:
                model['enabled'] = enabled
                model_found = True
                break
        if model_found:
            self._write_yaml_config(config)
            self.config_manager.active_models_config = config
            self.load_active_models()
            logger.info(f"模型'{model_name}'已被{'启用' if enabled else '禁用'}。")
        else:
            logger.warning(f"在active_models.yaml中未找到模型'{model_name}'。")

    def add_model_config(self, name: str, model_version: str, provider: str, enabled: bool = False):
        """
        添加新的模型配置。
        
        :param name: 模型配置名称（例如，'openai-gpt4-1106'）。
        :param model_version: 模型版本（例如，'gpt-4-1106-preview'）。
        :param provider: 提供商名称（例如，'openai'）。
        :param enabled: 是否启用该模型。
        :return: 是否成功添加。
        """
        # 验证提供商是否存在
        if provider not in self.MODEL_CLASSES:
            logger.warning(f"未知的模型提供商: {provider}")
            return False

        # 验证模型版本是否在可用模型列表中
        base_config = self.config_manager.get_model_config(provider)
        if not base_config:
            logger.warning(f"在models.yaml中未找到提供商'{provider}'的配置。")
            return False

        available_models = base_config.get("available_models", [])
        if available_models and model_version not in available_models:
            logger.warning(
                f"模型版本'{model_version}'不在'{provider}'的可用模型列表中。可用模型: {', '.join(available_models)}")
            return False

        # 检查名称是否已存在
        config = self._read_yaml_config()
        for model in config.get("active_models", []):
            if model['name'] == name:
                logger.warning(f"模型配置名称'{name}'已存在。")
                return False

        # 添加新配置
        new_model = {
            "name": name,
            "model": model_version,
            "enabled": enabled,
            "provider": provider
        }

        config["active_models"].append(new_model)
        self._write_yaml_config(config)
        self.config_manager.active_models_config = config
        self.load_active_models()
        logger.info(f"已添加新的模型配置: {name} ({provider}/{model_version})")
        return True

    def enable_model(self, model_name: str):
        self._update_model_status(model_name, True)

    def disable_model(self, model_name: str):
        self._update_model_status(model_name, False)

    def remove_model_config(self, model_name: str):
        """
        删除模型配置。
        
        :param model_name: 模型配置名称。
        :return: 是否成功删除。
        """
        config = self._read_yaml_config()
        model_found = False

        # 查找并删除模型配置
        active_models = config.get("active_models", [])
        for i, model in enumerate(active_models):
            if model['name'] == model_name:
                del active_models[i]
                model_found = True
                break

        if model_found:
            self._write_yaml_config(config)
            self.config_manager.active_models_config = config
            self.load_active_models()
            logger.info(f"已删除模型配置: {model_name}")
            return True
        else:
            logger.warning(f"在active_models.yaml中未找到模型'{model_name}'。")
            return False

    def switch_model_version(self, model_name: str, new_version: str):
        """
        切换特定模型的版本。
        
        :param model_name: 模型的配置名称（例如，'openai-gpt4'）。
        :param new_version: 新的模型版本。
        """
        config = self._read_yaml_config()
        model_found = False
        for model in config.get("active_models", []):
            if model['name'] == model_name:
                # 获取提供商
                provider = model.get("provider")
                # 验证新版本是否在可用模型列表中
                base_config = self.config_manager.get_model_config(provider)
                if base_config:
                    available_models = base_config.get("available_models", [])
                    if available_models and new_version not in available_models:
                        logger.warning(
                            f"模型版本'{new_version}'不在'{provider}'的可用模型列表中。可用模型: {', '.join(available_models)}")
                        return

                model['model'] = new_version
                model_found = True
                break
        if model_found:
            self._write_yaml_config(config)
            self.config_manager.active_models_config = config
            self.load_active_models()
            logger.info(f"已将模型'{model_name}'切换到版本'{new_version}'。")
        else:
            logger.warning(f"在active_models.yaml中未找到模型'{model_name}'。")

    def get_provider_models(self, provider: str):
        """
        获取特定提供商的所有可用模型。
        
        :param provider: 提供商名称（例如，'openai'）。
        :return: 该提供商的所有可用模型列表。
        """
        base_config = self.config_manager.get_model_config(provider)
        if not base_config:
            logger.warning(f"在models.yaml中未找到提供商'{provider}'的配置。")
            return []

        return base_config.get("available_models", [])
