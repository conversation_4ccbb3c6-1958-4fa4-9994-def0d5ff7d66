"""对话管理器模块

管理多个模型的对话历史和状态。
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path
from ..utils.config import ConfigManager
from utils.logger import logger


class ConversationFileManager:
    """对话文件管理工具类"""

    @staticmethod
    def load_conversation_file(file_path: str, model_name: str) -> List[Dict[str, Any]]:
        """加载对话文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                logger.info(f"已加载{model_name}的对话。")
                return data
        except FileNotFoundError:
            return []
        except json.JSONDecodeError:
            logger.warning(f"无法解码{model_name}的JSON，重新开始。")
            return []

    @staticmethod
    def save_conversation_file(file_path: str, conversations: List[Dict[str, Any]], model_name: str) -> None:
        """保存对话文件"""
        try:
            # 确保目录存在
            Path(file_path).parent.mkdir(parents=True, exist_ok=True)

            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(conversations, f, ensure_ascii=False, indent=2)
                logger.info(f"已保存{model_name}的对话到 {file_path}")
        except Exception as e:
            logger.error(f"保存{model_name}对话失败: {e}")

    @staticmethod
    def create_timestamped_directory(base_dir: str, timestamp_format: str = "%Y%m%d_%H%M%S") -> str:
        """创建时间戳目录"""
        timestamp = datetime.now().strftime(timestamp_format)
        session_dir = os.path.join(base_dir, timestamp)
        os.makedirs(session_dir, exist_ok=True)
        return session_dir


class ConversationManager:
    """
    独立管理每个模型的对话历史。
    """

    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        conv_settings = self.config_manager.get_conversation_settings()
        self.data_dir = conv_settings.get("data_dir", "data/conversations")
        self.max_history = conv_settings.get("max_history_per_model", 10)
        self.save_enabled = conv_settings.get("save_conversations", True)
        self.conversations = {}
        self.detailed_conversations = {}  # 存储详细的对话信息

        # 备份设置
        backup_settings = conv_settings.get("backup_settings", {})
        self.backup_enabled = backup_settings.get("enabled", True)
        self.backup_keep_count = backup_settings.get("keep_count", 5)
        self.backup_timestamp_format = backup_settings.get("timestamp_format", "%Y%m%d_%H%M%S")
        self.cleanup_on_startup = backup_settings.get("cleanup_on_startup", True)

        # 创建详细对话存储目录
        self.detailed_data_dir = os.path.join(self.data_dir, "detailed")

        if self.save_enabled:
            os.makedirs(self.data_dir, exist_ok=True)
            os.makedirs(self.detailed_data_dir, exist_ok=True)
            if self.cleanup_on_startup:
                self._cleanup_old_sessions()
            self.load_all_conversations()
            self.load_all_detailed_conversations()

    def _get_conv_path(self, model_name: str) -> str:
        """获取模型对话历史的文件路径。"""
        # 优先从current链接获取
        current_link = os.path.join(self.data_dir, "current")
        if os.path.islink(current_link):
            return os.path.join(current_link, f"{model_name}_conversation.json")

        # 如果没有current链接，从最新的时间戳文件夹获取
        import glob
        pattern = os.path.join(self.data_dir, "[0-9]*")
        session_dirs = [d for d in glob.glob(pattern) if os.path.isdir(d) and not os.path.islink(d)]
        if session_dirs:
            session_dirs.sort(key=os.path.getmtime, reverse=True)
            return os.path.join(session_dirs[0], f"{model_name}_conversation.json")

        # 兜底：返回旧的路径格式（向后兼容）
        return os.path.join(self.data_dir, f"{model_name}_conversation.json")

    def load_conversation(self, model_name: str):
        """加载单个模型的对话历史。"""
        if not self.save_enabled:
            self.conversations[model_name] = []
            return

        path = self._get_conv_path(model_name)
        self.conversations[model_name] = ConversationFileManager.load_conversation_file(path, model_name)

    def load_all_conversations(self):
        """从数据目录加载所有对话历史。"""
        logger.info("正在加载所有对话历史...")
        for filename in os.listdir(self.data_dir):
            if filename.endswith("_conversation.json"):
                model_name = filename.replace("_conversation.json", "")
                self.load_conversation(model_name)

    def add_message(self, model_name: str, role: str, content: str):
        """向模型的对话历史添加消息。"""
        if model_name not in self.conversations:
            self.conversations[model_name] = []

        message = {
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat()
        }
        self.conversations[model_name].append(message)

        # 如果历史记录超过最大长度，则进行裁剪
        if len(self.conversations[model_name]) > self.max_history * 2:
            self.conversations[model_name] = self.conversations[model_name][-self.max_history * 2:]

    def get_history(self, model_name: str, format_type="messages"):
        """获取模型的对话历史。"""
        history = self.conversations.get(model_name, [])
        if format_type == "text":
            return "\n".join([f"{m['role']}: {m['content']}" for m in history])
        return history

    def save_conversation(self, model_name: str):
        """将模型的对话历史保存到时间戳文件夹。"""
        if not self.save_enabled:
            return

        # 创建时间戳文件夹
        session_dir = ConversationFileManager.create_timestamped_directory(
            self.data_dir, self.backup_timestamp_format
        )

        # 直接保存到时间戳文件夹
        path = os.path.join(session_dir, f"{model_name}_conversation.json")
        ConversationFileManager.save_conversation_file(path, self.get_history(model_name), model_name)

        # 更新当前链接
        self._update_current_link(session_dir)
        logger.info(f"已保存{model_name}的对话历史到: {path}")

    def save_all_conversations(self):
        """保存所有对话历史。"""
        if not self.save_enabled:
            return
        for model_name in self.conversations:
            self.save_conversation(model_name)
        self._cleanup_old_sessions()
        logger.info("所有对话已保存。")

    def _cleanup_old_sessions(self, keep_count: Optional[int] = None):
        """清理旧的会话文件夹，只保留最新的几个会话。
        
        Args:
            keep_count: 保留的会话文件夹数量，如果为None则使用配置中的值
        """
        if keep_count is None:
            keep_count = self.backup_keep_count
        import glob
        import shutil

        # 获取所有时间戳文件夹（排除current链接和detailed目录）
        pattern = os.path.join(self.data_dir, "[0-9]*")
        session_dirs = [d for d in glob.glob(pattern) if os.path.isdir(d) and not os.path.islink(d)]
        session_dirs.sort(key=os.path.getmtime, reverse=True)

        for session_dir in session_dirs[keep_count:]:
            try:
                shutil.rmtree(session_dir)
                logger.info(f"已删除旧会话文件夹: {session_dir}")
            except OSError as e:
                logger.warning(f"删除会话文件夹失败: {session_dir}, 错误: {e}")

    def _update_current_link(self, session_dir: str):
        """更新current软链接指向最新的会话文件夹。
        
        Args:
            session_dir: 最新的会话文件夹路径
        """
        current_link = os.path.join(self.data_dir, "current")

        # 删除旧的链接
        if os.path.islink(current_link):
            os.unlink(current_link)
        elif os.path.exists(current_link):
            # 如果存在同名文件或目录，先删除
            import shutil
            if os.path.isdir(current_link):
                shutil.rmtree(current_link)
            else:
                os.remove(current_link)

        # 创建新的软链接
        try:
            os.symlink(os.path.basename(session_dir), current_link)
            logger.debug(f"已更新current链接指向: {session_dir}")
        except OSError as e:
            logger.warning(f"创建current链接失败: {e}")

    def clear_conversation(self, model_name: str):
        """清除模型的对话历史。"""
        self.conversations[model_name] = []
        self.detailed_conversations[model_name] = []
        if self.save_enabled:
            self.save_conversation(model_name)
            self.save_detailed_conversation(model_name)
        logger.info(f"已清除{model_name}的对话历史。")

    # === 详细对话存储功能 ===

    def _get_detailed_conv_path(self, model_name: str) -> str:
        """获取模型详细对话历史的文件路径。"""
        # 优先从current链接获取
        current_link = os.path.join(self.data_dir, "current")
        if os.path.islink(current_link):
            return os.path.join(current_link, f"{model_name}_detailed_conversation.json")

        # 如果没有current链接，从最新的时间戳文件夹获取
        import glob
        pattern = os.path.join(self.data_dir, "[0-9]*")
        session_dirs = [d for d in glob.glob(pattern) if os.path.isdir(d) and not os.path.islink(d)]
        if session_dirs:
            session_dirs.sort(key=os.path.getmtime, reverse=True)
            return os.path.join(session_dirs[0], f"{model_name}_detailed_conversation.json")

        # 兜底：返回旧的路径格式（向后兼容）
        return os.path.join(self.detailed_data_dir, f"{model_name}_detailed_conversation.json")

    def add_detailed_conversation(self, user_input: str, prompt_used: Optional[str],
                                  model_responses: Dict[str, Any], metadata: Optional[Dict] = None):
        """添加详细的对话信息，包括用户输入、使用的提示词和所有模型的响应。
        
        Args:
            user_input: 用户的原始输入
            prompt_used: 实际发送给模型的提示词（可能经过处理）
            model_responses: 各个模型的响应结果字典
            metadata: 额外的元数据信息
        """
        conversation_entry = {
            "conversation_id": self._generate_conversation_id(),
            "timestamp": datetime.now().isoformat(),
            "user_input": user_input,
            "prompt_used": prompt_used,
            "model_responses": model_responses,
            "metadata": metadata or {}
        }

        # 为每个响应的模型添加到对应的详细对话历史中
        for model_name in model_responses.keys():
            if model_name not in self.detailed_conversations:
                self.detailed_conversations[model_name] = []

            self.detailed_conversations[model_name].append(conversation_entry)

            # 限制历史记录长度
            if len(self.detailed_conversations[model_name]) > self.max_history:
                self.detailed_conversations[model_name] = self.detailed_conversations[model_name][-self.max_history:]

    def _generate_conversation_id(self) -> str:
        """生成唯一的对话ID。"""
        from uuid import uuid4
        return str(uuid4())

    def get_detailed_history(self, model_name: str, limit: Optional[int] = None) -> List[Dict]:
        """获取模型的详细对话历史。
        
        Args:
            model_name: 模型名称
            limit: 返回的记录数限制
            
        Returns:
            详细对话历史列表
        """
        history = self.detailed_conversations.get(model_name, [])
        if limit:
            return history[-limit:]
        return history

    def save_detailed_conversation(self, model_name: str):
        """保存模型的详细对话历史到时间戳文件夹。"""
        if not self.save_enabled:
            return

        # 创建时间戳文件夹
        timestamp = datetime.now().strftime(self.backup_timestamp_format)
        session_dir = os.path.join(self.data_dir, timestamp)
        os.makedirs(session_dir, exist_ok=True)

        # 直接保存到时间戳文件夹
        path = os.path.join(session_dir, f"{model_name}_detailed_conversation.json")
        detailed_history = self.get_detailed_history(model_name)

        with open(path, 'w', encoding='utf-8') as f:
            json.dump(detailed_history, f, ensure_ascii=False, indent=2)

        # 更新当前链接
        self._update_current_link(session_dir)
        logger.info(f"已保存{model_name}的详细对话历史到: {path}")

    def save_all_detailed_conversations(self):
        """保存所有详细对话历史。"""
        if not self.save_enabled:
            return
        for model_name in self.detailed_conversations:
            self.save_detailed_conversation(model_name)
        self._cleanup_old_sessions()
        logger.info("所有详细对话已保存。")

    def load_detailed_conversation(self, model_name: str):
        """加载单个模型的详细对话历史。"""
        if not self.save_enabled:
            self.detailed_conversations[model_name] = []
            return

        path = self._get_detailed_conv_path(model_name)
        try:
            with open(path, 'r', encoding='utf-8') as f:
                self.detailed_conversations[model_name] = json.load(f)
                logger.info(f"已加载{model_name}的详细对话历史。")
        except FileNotFoundError:
            self.detailed_conversations[model_name] = []
        except json.JSONDecodeError:
            logger.warning(f"无法解码{model_name}的详细对话JSON，重新开始。")
            self.detailed_conversations[model_name] = []

    def load_all_detailed_conversations(self):
        """加载所有详细对话历史。"""
        if not os.path.exists(self.detailed_data_dir):
            return

        logger.info("正在加载所有详细对话历史...")
        for filename in os.listdir(self.detailed_data_dir):
            if filename.endswith("_detailed_conversation.json"):
                model_name = filename.replace("_detailed_conversation.json", "")
                self.load_detailed_conversation(model_name)

    def save_all_detailed_conversations(self):
        """保存所有详细对话历史。"""
        if not self.save_enabled:
            return

        for model_name in self.detailed_conversations:
            self.save_detailed_conversation(model_name)
        logger.info("所有详细对话已保存。")

    def get_conversation_statistics(self, model_name: Optional[str] = None) -> Dict[str, Any]:
        """获取对话统计信息。
        
        Args:
            model_name: 特定模型名称，如果为None则返回所有模型的统计
            
        Returns:
            包含统计信息的字典
        """
        if model_name:
            detailed_history = self.get_detailed_history(model_name)
            return {
                "model_name": model_name,
                "total_conversations": len(detailed_history),
                "date_range": self._get_date_range(detailed_history),
                "average_response_time": self._calculate_avg_response_time(detailed_history)
            }
        else:
            stats = {}
            for model in self.detailed_conversations.keys():
                stats[model] = self.get_conversation_statistics(model)
            return stats

    def _get_date_range(self, history: List[Dict]) -> Dict[str, str]:
        """获取对话历史的日期范围。"""
        if not history:
            return {"start": None, "end": None}

        timestamps = [entry["timestamp"] for entry in history]
        return {
            "start": min(timestamps),
            "end": max(timestamps)
        }

    def _calculate_avg_response_time(self, history: List[Dict]) -> Optional[float]:
        """计算平均响应时间（如果元数据中包含响应时间信息）。"""
        response_times = []
        for entry in history:
            for model_name, response_data in entry.get("model_responses", {}).items():
                if isinstance(response_data, dict) and "response_time" in response_data:
                    response_times.append(response_data["response_time"])

        return sum(response_times) / len(response_times) if response_times else None

    def export_conversations(self, model_name: str, format_type: str = "json",
                             output_path: Optional[str] = None) -> str:
        """导出对话历史到指定格式。
        
        Args:
            model_name: 模型名称
            format_type: 导出格式 ('json', 'csv', 'txt')
            output_path: 输出文件路径，如果为None则自动生成
            
        Returns:
            导出文件的路径
        """
        if output_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"export_{model_name}_{timestamp}.{format_type}"

        detailed_history = self.get_detailed_history(model_name)

        if format_type == "json":
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(detailed_history, f, ensure_ascii=False, indent=2)
        elif format_type == "csv":
            import csv
            with open(output_path, 'w', newline='', encoding='utf-8') as f:
                if detailed_history:
                    writer = csv.DictWriter(f, fieldnames=detailed_history[0].keys())
                    writer.writeheader()
                    for entry in detailed_history:
                        # 将复杂对象转换为字符串
                        row = {k: json.dumps(v) if isinstance(v, (dict, list)) else v
                               for k, v in entry.items()}
                        writer.writerow(row)
        elif format_type == "txt":
            with open(output_path, 'w', encoding='utf-8') as f:
                for entry in detailed_history:
                    f.write(f"=== 对话 {entry['conversation_id']} ===\n")
                    f.write(f"时间: {entry['timestamp']}\n")
                    f.write(f"用户输入: {entry['user_input']}\n")
                    f.write(f"使用的提示词: {entry['prompt_used']}\n")
                    f.write(f"模型响应: {json.dumps(entry['model_responses'], ensure_ascii=False, indent=2)}\n")
                    f.write("\n" + "=" * 50 + "\n\n")

        logger.info(f"对话历史已导出到: {output_path}")
        return output_path
