import os
import requests
from loguru import logger
from typing import List, Dict, Any, Optional
from .base_model import BaseModel
from ..utils.retry import retry, RetryException
from ..utils.performance import get_performance_monitor


class OpenAIModel(BaseModel):
    """
    OpenAI GPT模型实现。
    """

    def __init__(self, model_name: str, api_key: str, base_url: str):
        super().__init__(model_name, api_key, base_url)
        if not self.api_key:
            raise ValueError("需要提供OpenAI API密钥。")

    @retry(
        exceptions=(requests.RequestException, requests.Timeout, requests.ConnectionError),
        max_retries=3,
        base_delay=1.0,
        backoff_factor=2.0
    )
    def _make_api_request(self, headers: Dict[str, str], data: Dict[str, Any], timeout: int) -> Dict[str, Any]:
        """
        使用重试逻辑发起实际的API请求。
        """
        response = requests.post(
            f"{self.base_url}/chat/completions",
            headers=headers,
            json=data,
            timeout=timeout
        )

        # 处理特定的HTTP状态码
        if response.status_code == 401:
            raise ValueError("无效的API密钥")
        elif response.status_code == 429:
            raise requests.RequestException("超出速率限制")
        elif response.status_code == 500:
            raise requests.RequestException("OpenAI服务器错误")

        response.raise_for_status()
        return response.json()

    def chat(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """
        向OpenAI API发送聊天请求，具有健壮的错误处理和性能监控。
        """
        # 验证输入
        if not messages:
            raise ValueError("消息列表不能为空")

        # 计算请求大小用于监控
        request_text = str(messages) + str(kwargs)
        request_size = len(request_text)

        # 开始性能监控
        monitor = get_performance_monitor()

        with monitor.track_api_call("OpenAI", self.model_name, request_size) as metrics:
            # 清理kwargs以移除None值并确保正确的类型
            clean_kwargs = {k: v for k, v in kwargs.items() if v is not None and k not in ['timeout']}

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
            }

            data = {
                "model": self.model_name,
                "messages": messages,
                **clean_kwargs
            }

            timeout = kwargs.get("timeout", 30)

            try:
                logger.info(f"正在为模型{self.model_name}发起OpenAI API请求")
                result = self._make_api_request(headers, data, timeout)

                # 验证响应结构
                if "choices" not in result or not result["choices"]:
                    raise ValueError("无效的响应结构：缺少choices")

                if "message" not in result["choices"][0] or "content" not in result["choices"][0]["message"]:
                    raise ValueError("无效的响应结构：缺少message content")

                content = result["choices"][0]["message"]["content"]

                # 使用响应大小更新指标
                metrics.response_size = len(content)

                logger.info(f"成功接收来自OpenAI的响应（长度：{len(content)}个字符）")
                return content

            except RetryException as e:
                error_msg = f"OpenAI API在所有重试尝试后失败：{e.original_exception}"
                logger.error(error_msg)
                metrics.retry_count = getattr(e, 'retry_count', 0)
                raise ValueError(error_msg)

            except ValueError as e:
                error_msg = f"OpenAI API验证错误：{e}"
                logger.error(error_msg)
                raise ValueError(error_msg)

            except Exception as e:
                error_msg = f"调用OpenAI API时发生意外错误：{e}"
                logger.error(error_msg, exc_info=True)
                raise ValueError(error_msg)
        return None
