import os
import requests
from loguru import logger
from typing import List, Dict, Any
from .base_model import BaseModel
from ..utils.retry import retry, RetryException
from ..utils.performance import get_performance_monitor


class DoubaoModel(BaseModel):
    """
    豆包（火山引擎）模型实现。
    """

    def __init__(self, model_name: str, api_key: str, base_url: str):
        super().__init__(model_name, api_key, base_url)
        if not self.api_key:
            raise ValueError("需要提供豆包API密钥。")

    @retry(
        exceptions=(requests.RequestException, requests.Timeout, requests.ConnectionError),
        max_retries=3,
        base_delay=1.0,
        backoff_factor=2.0
    )
    def _make_api_request(self, url: str, headers: Dict[str, str], data: Dict[str, Any], timeout: int) -> Dict[
        str, Any]:
        """
        使用重试逻辑发起实际的API请求。
        """
        response = requests.post(
            url,
            headers=headers,
            json=data,
            timeout=timeout
        )

        # 处理特定的HTTP状态码
        if response.status_code == 400:
            raise ValueError("无效的请求格式或参数")
        elif response.status_code == 401:
            raise ValueError("无效的API密钥")
        elif response.status_code == 429:
            raise requests.RequestException("超出速率限制")
        elif response.status_code == 500:
            raise requests.RequestException("豆包服务器错误")

        response.raise_for_status()
        return response.json()

    def chat(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """
        向豆包API发送聊天请求，具有健壮的错误处理和性能监控。
        """
        # 验证输入
        if not messages:
            raise ValueError("消息列表不能为空")

        # 计算请求大小用于监控
        request_text = str(messages) + str(kwargs)
        request_size = len(request_text)

        # 开始性能监控
        monitor = get_performance_monitor()

        with monitor.track_api_call("Doubao", self.model_name, request_size) as metrics:
            url = f"{self.base_url}/chat/completions"

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
            }

            # 准备请求数据
            data = {
                "model": self.model_name,
                "messages": messages,
            }

            # 如果提供了可选参数，则添加
            if "temperature" in kwargs and kwargs["temperature"] is not None:
                data["temperature"] = kwargs["temperature"]
            if "max_tokens" in kwargs and kwargs["max_tokens"] is not None:
                data["max_tokens"] = kwargs["max_tokens"]
            if "top_p" in kwargs and kwargs["top_p"] is not None:
                data["top_p"] = kwargs["top_p"]
            if "frequency_penalty" in kwargs and kwargs["frequency_penalty"] is not None:
                data["frequency_penalty"] = kwargs["frequency_penalty"]
            if "presence_penalty" in kwargs and kwargs["presence_penalty"] is not None:
                data["presence_penalty"] = kwargs["presence_penalty"]

            timeout = kwargs.get("timeout", 30)

            try:
                logger.info(f"正在为模型{self.model_name}发起豆包API请求")
                result = self._make_api_request(url, headers, data, timeout)

                # 验证响应结构
                if "choices" not in result or not result["choices"]:
                    raise ValueError("无效的响应结构：缺少choices")

                choice = result["choices"][0]

                # 检查完成原因
                if "finish_reason" in choice and choice["finish_reason"] not in ["stop", None]:
                    finish_reason = choice["finish_reason"]
                    if finish_reason == "length":
                        logger.warning("响应因max_tokens限制而被截断")
                    elif finish_reason == "content_filter":
                        return "错误：内容因违反政策而被过滤"
                    else:
                        return f"错误：生成因以下原因停止：{finish_reason}"

                if "message" not in choice or "content" not in choice["message"]:
                    raise ValueError("无效的响应结构：缺少message content")

                content = choice["message"]["content"]
                if content is None:
                    raise ValueError("响应内容为空")

                # 使用响应大小更新指标
                metrics.response_size = len(content)

                logger.info(f"成功接收来自豆包的响应（长度：{len(content)}个字符）")
                return content

            except RetryException as e:
                error_msg = f"豆包API在所有重试尝试后失败：{e.original_exception}"
                logger.error(error_msg)
                metrics.retry_count = getattr(e, 'retry_count', 0)
                return f"错误：{error_msg}"

            except ValueError as e:
                error_msg = f"豆包API验证错误：{e}"
                logger.error(error_msg)
                return f"错误：{error_msg}"

            except Exception as e:
                error_msg = f"调用豆包API时发生意外错误：{e}"
                logger.error(error_msg, exc_info=True)
                return f"错误：{error_msg}"
        return None
