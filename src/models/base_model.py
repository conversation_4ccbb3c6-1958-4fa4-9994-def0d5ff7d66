from abc import ABC, abstractmethod
from typing import List, Dict


class BaseModel(ABC):
    """
    所有语言模型的抽象基类。
    """

    def __init__(self, model_name: str, api_key: str, base_url: str):
        """
        初始化模型。

        :param model_name: 模型的名称。
        :param api_key: 模型的API密钥。
        :param base_url: 模型API的基础URL。
        """
        self.model_name = model_name
        self.api_key = api_key
        self.base_url = base_url

    @abstractmethod
    def chat(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """
        向模型发送聊天请求。

        :param messages: 对话中的消息列表。
        :param kwargs: 请求的附加参数。
        :return: 模型的响应，以字符串形式返回。
        """
        pass

    def __str__(self):
        return f"{self.__class__.__name__}(model_name='{self.model_name}')"
