import os
import requests
from loguru import logger
from typing import List, Dict, Any, Optional
from .base_model import BaseModel
from ..utils.retry import retry, RetryException
from ..utils.performance import get_performance_monitor


class GeminiModel(BaseModel):
    """
    Google Gemini模型实现。
    """

    def __init__(self, model_name: str, api_key: str, base_url: str):
        super().__init__(model_name, api_key, base_url)
        if not self.api_key:
            raise ValueError("需要提供Gemini API密钥。")

    @retry(
        exceptions=(requests.RequestException, requests.Timeout, requests.ConnectionError),
        max_retries=3,
        base_delay=1.0,
        backoff_factor=2.0
    )
    def _make_api_request(self, url: str, data: Dict[str, Any], timeout: int) -> Dict[str, Any]:
        """
        使用重试逻辑发起实际的API请求。
        """
        response = requests.post(
            url,
            json=data,
            timeout=timeout
        )

        # 处理特定的HTTP状态码
        if response.status_code == 400:
            raise ValueError("无效的请求格式或参数")
        elif response.status_code == 401:
            raise ValueError("无效的API密钥")
        elif response.status_code == 429:
            raise requests.RequestException("超出速率限制")
        elif response.status_code == 500:
            raise requests.RequestException("Gemini服务器错误")

        response.raise_for_status()
        return response.json()

    def _convert_messages_to_gemini_format(self, messages: List[Dict[str, str]]) -> List[Dict[str, Any]]:
        """
        将标准消息格式转换为Gemini API格式。
        """
        contents = []
        system_instruction = None

        for msg in messages:
            if msg['role'] == 'system':
                # 存储系统消息以供后续使用
                system_instruction = msg['content']
                continue

            # 将user/assistant角色转换为Gemini格式
            gemini_role = "user" if msg["role"] == "user" else "model"
            contents.append({
                "role": gemini_role,
                "parts": [{"text": msg["content"]}]
            })

        # 如果有系统指令，将其添加到第一条用户消息前面
        if system_instruction and contents and contents[0]["role"] == "user":
            original_text = contents[0]["parts"][0]["text"]
            contents[0]["parts"][0]["text"] = f"{system_instruction}\n\n{original_text}"

        return contents

    def chat(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """
        向Gemini API发送聊天请求，具有健壮的错误处理和性能监控。
        """
        # 验证输入
        if not messages:
            raise ValueError("消息列表不能为空")

        # 计算请求大小用于监控
        request_text = str(messages) + str(kwargs)
        request_size = len(request_text)

        # 开始性能监控
        monitor = get_performance_monitor()

        with monitor.track_api_call("Gemini", self.model_name, request_size) as metrics:
            url = f"{self.base_url}/models/{self.model_name}:generateContent?key={self.api_key}"

            # 将消息转换为Gemini格式
            try:
                contents = self._convert_messages_to_gemini_format(messages)
            except Exception as e:
                error_msg = f"将消息转换为Gemini格式时出错：{e}"
                logger.error(error_msg)
                return f"错误：{error_msg}"

            # 准备生成配置
            generation_config = {
                "temperature": kwargs.get("temperature", 0.7),
                "maxOutputTokens": kwargs.get("max_tokens", 2000),
            }

            # 移除None值
            generation_config = {k: v for k, v in generation_config.items() if v is not None}

            data = {
                "contents": contents,
                "generationConfig": generation_config
            }

            timeout = kwargs.get("timeout", 30)

            try:
                logger.info(f"正在为模型{self.model_name}发起Gemini API请求")
                result = self._make_api_request(url, data, timeout)

                # 验证响应结构
                if "candidates" not in result or not result["candidates"]:
                    raise ValueError("无效的响应结构：缺少candidates")

                candidate = result["candidates"][0]

                # 检查安全评级或完成原因
                if "finishReason" in candidate and candidate["finishReason"] != "STOP":
                    finish_reason = candidate["finishReason"]
                    if finish_reason == "SAFETY":
                        return "错误：内容因安全问题被阻止"
                    else:
                        return f"错误：生成因以下原因停止：{finish_reason}"

                if "content" not in candidate or "parts" not in candidate["content"]:
                    raise ValueError("无效的响应结构：缺少content parts")

                if not candidate["content"]["parts"] or "text" not in candidate["content"]["parts"][0]:
                    raise ValueError("无效的响应结构：缺少text content")

                content = candidate["content"]["parts"][0]["text"]

                # 使用响应大小更新指标
                metrics.response_size = len(content)

                logger.info(f"成功接收来自Gemini的响应（长度：{len(content)}个字符）")
                return content

            except RetryException as e:
                error_msg = f"Gemini API在所有重试尝试后失败：{e.original_exception}"
                logger.error(error_msg)
                metrics.retry_count = getattr(e, 'retry_count', 0)
                return f"错误：{error_msg}"

            except ValueError as e:
                error_msg = f"Gemini API验证错误：{e}"
                logger.error(error_msg)
                return f"错误：{error_msg}"

            except Exception as e:
                error_msg = f"调用Gemini API时发生意外错误：{e}"
                logger.error(error_msg, exc_info=True)
                return f"错误：{error_msg}"
        return None
