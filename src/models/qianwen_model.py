from typing import List, Dict, Any

import requests
from utils.logger import logger

from .base_model import BaseModel
from ..utils.performance import get_performance_monitor
from ..utils.retry import retry, RetryException


class QianwenModel(BaseModel):
    """
    阿里巴巴千问（Dashscope）模型实现。
    """

    def __init__(self, model_name: str, api_key: str, base_url: str):
        super().__init__(model_name, api_key, base_url)
        if not self.api_key:
            raise ValueError("需要提供千问API密钥。")

    @retry(
        exceptions=(requests.RequestException, requests.Timeout, requests.ConnectionError),
        max_retries=3,
        base_delay=1.0,
        backoff_factor=2.0
    )
    def _make_api_request(self, url: str, headers: Dict[str, str], data: Dict[str, Any], timeout: int) -> Dict[
        str, Any]:
        """
        使用重试逻辑发起实际的API请求。
        """
        response = requests.post(
            url,
            headers=headers,
            json=data,
            timeout=timeout
        )

        # 处理特定的HTTP状态码
        if response.status_code == 400:
            raise ValueError("无效的请求格式或参数")
        elif response.status_code == 401:
            raise ValueError("无效的API密钥")
        elif response.status_code == 429:
            raise requests.RequestException("超出速率限制")
        elif response.status_code == 500:
            raise requests.RequestException("千问服务器错误")

        response.raise_for_status()
        return response.json()

    def chat(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """
        向千问API发送聊天请求，具有健壮的错误处理和性能监控。
        """
        # 验证输入
        if not messages:
            raise ValueError("消息列表不能为空")

        # 计算请求大小用于监控
        request_text = str(messages) + str(kwargs)
        request_size = len(request_text)

        # 开始性能监控
        monitor = get_performance_monitor()

        with monitor.track_api_call("Qianwen", self.model_name, request_size) as metrics:
            url = f"{self.base_url}/services/aigc/text-generation/generation"

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
            }

            # 准备参数
            parameters = {}
            if "temperature" in kwargs and kwargs["temperature"] is not None:
                parameters["temperature"] = kwargs["temperature"]
            if "max_tokens" in kwargs and kwargs["max_tokens"] is not None:
                parameters["max_tokens"] = kwargs["max_tokens"]
            if "top_p" in kwargs and kwargs["top_p"] is not None:
                parameters["top_p"] = kwargs["top_p"]
            if "repetition_penalty" in kwargs and kwargs["repetition_penalty"] is not None:
                parameters["repetition_penalty"] = kwargs["repetition_penalty"]

            data = {
                "model": self.model_name,
                "input": {
                    "messages": messages
                }
            }

            # 仅在参数存在时添加
            if parameters:
                data["parameters"] = parameters

            timeout = kwargs.get("timeout", 30)

            try:
                logger.info(f"正在为模型{self.model_name}发起千问API请求")
                result = self._make_api_request(url, headers, data, timeout)

                # 验证响应结构
                if "output" not in result:
                    raise ValueError("无效的响应结构：缺少output")

                output = result["output"]

                # 检查完成原因
                if "finish_reason" in output and output["finish_reason"] not in ["stop", None]:
                    finish_reason = output["finish_reason"]
                    if finish_reason == "length":
                        logger.warning("响应因max_tokens限制而被截断")
                    elif finish_reason == "content_filter":
                        return "错误：内容因违反政策而被过滤"
                    else:
                        return f"错误：生成因以下原因停止：{finish_reason}"

                # 处理不同的响应格式
                if "text" in output:
                    content = output["text"]
                elif "choices" in output and output["choices"]:
                    # 替代格式
                    choice = output["choices"][0]
                    if "message" in choice and "content" in choice["message"]:
                        content = choice["message"]["content"]
                    else:
                        raise ValueError("无效的响应结构：缺少message content")
                else:
                    raise ValueError("无效的响应结构：缺少text或choices")

                if content is None:
                    raise ValueError("响应内容为空")

                # 使用响应大小更新指标
                metrics.response_size = len(content)

                logger.info(f"成功接收来自千问的响应（长度：{len(content)}个字符）")
                return content

            except RetryException as e:
                error_msg = f"千问API在所有重试尝试后失败：{e.original_exception}"
                logger.error(error_msg)
                metrics.retry_count = getattr(e, 'retry_count', 0)
                return f"错误：{error_msg}"

            except ValueError as e:
                error_msg = f"千问API验证错误：{e}"
                logger.error(error_msg)
                return f"错误：{error_msg}"

            except Exception as e:
                error_msg = f"调用千问API时发生意外错误：{e}"
                logger.error(error_msg, exc_info=True)
                return f"错误：{error_msg}"
        return None
