#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
LLM比较工具启动脚本
使用uv run命令启动程序
"""

import subprocess
import sys


def main():
    """
    使用uv run启动main.py
    """
    print("正在使用uv run启动LLM比较工具...")
    
    # 构建命令
    cmd = ["uv", "run", "python", "main.py"]
    
    # 添加命令行参数
    if len(sys.argv) > 1:
        cmd.extend(sys.argv[1:])
    
    try:
        # 执行命令
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"启动失败: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"发生意外错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()