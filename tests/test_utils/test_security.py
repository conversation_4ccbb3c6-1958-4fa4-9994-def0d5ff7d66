import time
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

import jwt
import pytest
from src.utils.security import (
    PasswordManager,
    TokenManager,
    APIKeyManager,
    RateLimiter,
    SecurityValidator,
    EncryptionManager,
    AuditLogger,
    SecurityMiddleware,
    CSRFProtection,
    InputSanitizer,
    SecurityError,
    AuthenticationError,
    AuthorizationError,
    RateLimitError
)


class TestPasswordManager:
    """密码管理器测试类"""
    
    @pytest.fixture
    def password_manager(self):
        """创建密码管理器实例"""
        return PasswordManager()
    
    def test_hash_password(self, password_manager):
        """测试密码哈希"""
        password = "test_password_123"
        hashed = password_manager.hash_password(password)
        
        # 哈希后的密码应该不同于原密码
        assert hashed != password
        # 哈希应该包含盐值
        assert len(hashed) > len(password)
        # 每次哈希应该产生不同结果（因为盐值不同）
        hashed2 = password_manager.hash_password(password)
        assert hashed != hashed2
    
    def test_verify_password(self, password_manager):
        """测试密码验证"""
        password = "test_password_123"
        hashed = password_manager.hash_password(password)
        
        # 正确密码应该验证成功
        assert password_manager.verify_password(password, hashed) is True
        
        # 错误密码应该验证失败
        assert password_manager.verify_password("wrong_password", hashed) is False
        assert password_manager.verify_password("", hashed) is False
    
    def test_generate_password(self, password_manager):
        """测试密码生成"""
        # 默认长度
        password = password_manager.generate_password()
        assert len(password) == 12  # 默认长度
        
        # 自定义长度
        password = password_manager.generate_password(length=20)
        assert len(password) == 20
        
        # 包含特殊字符
        password = password_manager.generate_password(include_symbols=True)
        assert any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)
        
        # 不包含特殊字符
        password = password_manager.generate_password(include_symbols=False)
        assert not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)
    
    def test_password_strength(self, password_manager):
        """测试密码强度检查"""
        # 弱密码
        weak_passwords = [
            "123456",
            "password",
            "abc123",
            "qwerty",
            "12345678"
        ]
        
        for password in weak_passwords:
            strength = password_manager.check_password_strength(password)
            assert strength["score"] < 3  # 弱密码分数应该较低
            assert not strength["is_strong"]
        
        # 强密码
        strong_passwords = [
            "MyStr0ng!P@ssw0rd",
            "C0mpl3x#P@ssw0rd123",
            "S3cur3!T3st#P@ss"
        ]
        
        for password in strong_passwords:
            strength = password_manager.check_password_strength(password)
            assert strength["score"] >= 3  # 强密码分数应该较高
            assert strength["is_strong"]
    
    def test_password_history(self, password_manager):
        """测试密码历史记录"""
        user_id = "user123"
        passwords = ["password1", "password2", "password3"]
        
        # 添加密码历史
        for password in passwords:
            hashed = password_manager.hash_password(password)
            password_manager.add_to_history(user_id, hashed)
        
        # 检查是否在历史中
        for password in passwords:
            assert password_manager.is_in_history(user_id, password) is True
        
        # 新密码不应该在历史中
        assert password_manager.is_in_history(user_id, "new_password") is False
    
    def test_password_expiry(self, password_manager):
        """测试密码过期"""
        user_id = "user123"
        
        # 设置密码过期时间
        password_manager.set_password_expiry(user_id, days=30)
        
        # 新设置的密码不应该过期
        assert password_manager.is_password_expired(user_id) is False
        
        # 模拟过期密码
        expired_date = datetime.now() - timedelta(days=31)
        password_manager.password_dates[user_id] = expired_date
        
        assert password_manager.is_password_expired(user_id) is True


class TestTokenManager:
    """令牌管理器测试类"""
    
    @pytest.fixture
    def token_manager(self):
        """创建令牌管理器实例"""
        return TokenManager(secret_key="test_secret_key_123")
    
    def test_generate_jwt_token(self, token_manager):
        """测试JWT令牌生成"""
        payload = {
            "user_id": 123,
            "username": "testuser",
            "role": "user"
        }
        
        token = token_manager.generate_jwt_token(payload, expires_in=3600)
        
        # 令牌应该是字符串
        assert isinstance(token, str)
        # 令牌应该包含三个部分（header.payload.signature）
        assert len(token.split('.')) == 3
    
    def test_verify_jwt_token(self, token_manager):
        """测试JWT令牌验证"""
        payload = {
            "user_id": 123,
            "username": "testuser"
        }
        
        token = token_manager.generate_jwt_token(payload, expires_in=3600)
        
        # 验证有效令牌
        decoded = token_manager.verify_jwt_token(token)
        assert decoded["user_id"] == 123
        assert decoded["username"] == "testuser"
        
        # 验证无效令牌
        invalid_token = token + "invalid"
        with pytest.raises(AuthenticationError):
            token_manager.verify_jwt_token(invalid_token)
    
    def test_token_expiry(self, token_manager):
        """测试令牌过期"""
        payload = {"user_id": 123}
        
        # 生成短期令牌
        token = token_manager.generate_jwt_token(payload, expires_in=1)
        
        # 立即验证应该成功
        decoded = token_manager.verify_jwt_token(token)
        assert decoded["user_id"] == 123
        
        # 等待过期
        time.sleep(2)
        
        # 过期令牌应该验证失败
        with pytest.raises(AuthenticationError, match="令牌已过期"):
            token_manager.verify_jwt_token(token)
    
    def test_refresh_token(self, token_manager):
        """测试刷新令牌"""
        user_id = 123
        
        # 生成刷新令牌
        refresh_token = token_manager.generate_refresh_token(user_id)
        assert isinstance(refresh_token, str)
        
        # 验证刷新令牌
        assert token_manager.verify_refresh_token(refresh_token, user_id) is True
        
        # 错误的用户ID应该验证失败
        assert token_manager.verify_refresh_token(refresh_token, 456) is False
        
        # 撤销刷新令牌
        token_manager.revoke_refresh_token(refresh_token)
        assert token_manager.verify_refresh_token(refresh_token, user_id) is False
    
    def test_token_blacklist(self, token_manager):
        """测试令牌黑名单"""
        payload = {"user_id": 123}
        token = token_manager.generate_jwt_token(payload, expires_in=3600)
        
        # 令牌应该有效
        decoded = token_manager.verify_jwt_token(token)
        assert decoded["user_id"] == 123
        
        # 将令牌加入黑名单
        token_manager.blacklist_token(token)
        
        # 黑名单中的令牌应该验证失败
        with pytest.raises(AuthenticationError, match="令牌已被撤销"):
            token_manager.verify_jwt_token(token)


class TestAPIKeyManager:
    """API密钥管理器测试类"""
    
    @pytest.fixture
    def api_key_manager(self):
        """创建API密钥管理器实例"""
        return APIKeyManager()
    
    def test_generate_api_key(self, api_key_manager):
        """测试API密钥生成"""
        api_key = api_key_manager.generate_api_key()
        
        # API密钥应该是字符串
        assert isinstance(api_key, str)
        # 应该有足够的长度
        assert len(api_key) >= 32
        # 每次生成应该不同
        api_key2 = api_key_manager.generate_api_key()
        assert api_key != api_key2
    
    def test_validate_api_key(self, api_key_manager):
        """测试API密钥验证"""
        user_id = "user123"
        api_key = api_key_manager.generate_api_key()
        
        # 注册API密钥
        api_key_manager.register_api_key(user_id, api_key)
        
        # 验证有效密钥
        assert api_key_manager.validate_api_key(api_key) == user_id
        
        # 验证无效密钥
        invalid_key = "invalid_key_123"
        assert api_key_manager.validate_api_key(invalid_key) is None
    
    def test_api_key_permissions(self, api_key_manager):
        """测试API密钥权限"""
        user_id = "user123"
        api_key = api_key_manager.generate_api_key()
        permissions = ["read", "write"]
        
        # 注册带权限的API密钥
        api_key_manager.register_api_key(user_id, api_key, permissions=permissions)
        
        # 检查权限
        assert api_key_manager.has_permission(api_key, "read") is True
        assert api_key_manager.has_permission(api_key, "write") is True
        assert api_key_manager.has_permission(api_key, "delete") is False
    
    def test_api_key_rate_limit(self, api_key_manager):
        """测试API密钥速率限制"""
        user_id = "user123"
        api_key = api_key_manager.generate_api_key()
        
        # 注册带速率限制的API密钥
        api_key_manager.register_api_key(
            user_id, 
            api_key, 
            rate_limit={"requests_per_minute": 10}
        )
        
        # 模拟请求
        for i in range(10):
            assert api_key_manager.check_rate_limit(api_key) is True
        
        # 第11个请求应该被限制
        assert api_key_manager.check_rate_limit(api_key) is False
    
    def test_revoke_api_key(self, api_key_manager):
        """测试撤销API密钥"""
        user_id = "user123"
        api_key = api_key_manager.generate_api_key()
        
        # 注册API密钥
        api_key_manager.register_api_key(user_id, api_key)
        assert api_key_manager.validate_api_key(api_key) == user_id
        
        # 撤销API密钥
        api_key_manager.revoke_api_key(api_key)
        assert api_key_manager.validate_api_key(api_key) is None


class TestRateLimiter:
    """速率限制器测试类"""
    
    @pytest.fixture
    def rate_limiter(self):
        """创建速率限制器实例"""
        return RateLimiter()
    
    def test_basic_rate_limiting(self, rate_limiter):
        """测试基本速率限制"""
        client_id = "client123"
        
        # 设置速率限制：每分钟5个请求
        rate_limiter.set_limit(client_id, requests=5, window=60)
        
        # 前5个请求应该通过
        for i in range(5):
            assert rate_limiter.is_allowed(client_id) is True
        
        # 第6个请求应该被拒绝
        assert rate_limiter.is_allowed(client_id) is False
    
    def test_sliding_window_rate_limiting(self, rate_limiter):
        """测试滑动窗口速率限制"""
        client_id = "client123"
        
        # 设置滑动窗口限制
        rate_limiter.set_sliding_window_limit(client_id, requests=3, window=5)
        
        # 在窗口内发送请求
        assert rate_limiter.is_allowed(client_id) is True
        assert rate_limiter.is_allowed(client_id) is True
        assert rate_limiter.is_allowed(client_id) is True
        
        # 超出限制
        assert rate_limiter.is_allowed(client_id) is False
        
        # 等待窗口滑动
        time.sleep(2)
        
        # 应该可以发送新请求
        assert rate_limiter.is_allowed(client_id) is True
    
    def test_different_client_limits(self, rate_limiter):
        """测试不同客户端的限制"""
        client1 = "client1"
        client2 = "client2"
        
        # 为不同客户端设置不同限制
        rate_limiter.set_limit(client1, requests=3, window=60)
        rate_limiter.set_limit(client2, requests=5, window=60)
        
        # 客户端1：3个请求后被限制
        for i in range(3):
            assert rate_limiter.is_allowed(client1) is True
        assert rate_limiter.is_allowed(client1) is False
        
        # 客户端2：5个请求后被限制
        for i in range(5):
            assert rate_limiter.is_allowed(client2) is True
        assert rate_limiter.is_allowed(client2) is False
    
    def test_rate_limit_reset(self, rate_limiter):
        """测试速率限制重置"""
        client_id = "client123"
        
        # 设置短期限制便于测试
        rate_limiter.set_limit(client_id, requests=2, window=1)
        
        # 用完配额
        assert rate_limiter.is_allowed(client_id) is True
        assert rate_limiter.is_allowed(client_id) is True
        assert rate_limiter.is_allowed(client_id) is False
        
        # 等待窗口重置
        time.sleep(1.1)
        
        # 应该可以再次发送请求
        assert rate_limiter.is_allowed(client_id) is True
    
    def test_rate_limit_info(self, rate_limiter):
        """测试速率限制信息"""
        client_id = "client123"
        
        rate_limiter.set_limit(client_id, requests=10, window=60)
        
        # 发送几个请求
        for i in range(3):
            rate_limiter.is_allowed(client_id)
        
        # 获取限制信息
        info = rate_limiter.get_limit_info(client_id)
        
        assert info["limit"] == 10
        assert info["remaining"] == 7
        assert info["used"] == 3
        assert "reset_time" in info


class TestSecurityValidator:
    """安全验证器测试类"""
    
    @pytest.fixture
    def security_validator(self):
        """创建安全验证器实例"""
        return SecurityValidator()
    
    def test_validate_input_length(self, security_validator):
        """测试输入长度验证"""
        # 有效长度
        assert security_validator.validate_input_length("test", max_length=10) is True
        
        # 超长输入
        with pytest.raises(SecurityError, match="输入长度超过限制"):
            security_validator.validate_input_length("a" * 100, max_length=10)
    
    def test_validate_sql_injection(self, security_validator):
        """测试SQL注入验证"""
        # 安全输入
        safe_inputs = [
            "normal text",
            "<EMAIL>",
            "123456",
            "hello world"
        ]
        
        for input_text in safe_inputs:
            assert security_validator.validate_sql_injection(input_text) is True
        
        # 可疑输入
        suspicious_inputs = [
            "'; DROP TABLE users; --",
            "1' OR '1'='1",
            "admin'--",
            "1; DELETE FROM users"
        ]
        
        for input_text in suspicious_inputs:
            with pytest.raises(SecurityError, match="检测到潜在的SQL注入"):
                security_validator.validate_sql_injection(input_text)
    
    def test_validate_xss(self, security_validator):
        """测试XSS验证"""
        # 安全输入
        safe_inputs = [
            "normal text",
            "hello world",
            "user input without scripts"
        ]
        
        for input_text in safe_inputs:
            assert security_validator.validate_xss(input_text) is True
        
        # 可疑输入
        suspicious_inputs = [
            "<script>alert('xss')</script>",
            "<img src=x onerror=alert('xss')>",
            "javascript:alert('xss')",
            "<iframe src='javascript:alert(1)'></iframe>"
        ]
        
        for input_text in suspicious_inputs:
            with pytest.raises(SecurityError, match="检测到潜在的XSS攻击"):
                security_validator.validate_xss(input_text)
    
    def test_validate_file_upload(self, security_validator):
        """测试文件上传验证"""
        # 模拟文件对象
        safe_file = Mock()
        safe_file.filename = "document.pdf"
        safe_file.content_type = "application/pdf"
        safe_file.size = 1024 * 1024  # 1MB
        
        allowed_types = ["application/pdf", "text/plain"]
        max_size = 5 * 1024 * 1024  # 5MB
        
        assert security_validator.validate_file_upload(
            safe_file, allowed_types, max_size
        ) is True
        
        # 不允许的文件类型
        unsafe_file = Mock()
        unsafe_file.filename = "script.exe"
        unsafe_file.content_type = "application/x-executable"
        unsafe_file.size = 1024
        
        with pytest.raises(SecurityError, match="文件类型不被允许"):
            security_validator.validate_file_upload(
                unsafe_file, allowed_types, max_size
            )
        
        # 文件过大
        large_file = Mock()
        large_file.filename = "large.pdf"
        large_file.content_type = "application/pdf"
        large_file.size = 10 * 1024 * 1024  # 10MB
        
        with pytest.raises(SecurityError, match="文件大小超过限制"):
            security_validator.validate_file_upload(
                large_file, allowed_types, max_size
            )
    
    def test_validate_ip_address(self, security_validator):
        """测试IP地址验证"""
        # 有效IP地址
        valid_ips = [
            "***********",
            "********",
            "127.0.0.1",
            "*******"
        ]
        
        for ip in valid_ips:
            assert security_validator.validate_ip_address(ip) is True
        
        # 无效IP地址
        invalid_ips = [
            "256.256.256.256",
            "192.168.1",
            "not.an.ip.address",
            "***********.1"
        ]
        
        for ip in invalid_ips:
            with pytest.raises(SecurityError, match="无效的IP地址"):
                security_validator.validate_ip_address(ip)
    
    def test_validate_url(self, security_validator):
        """测试URL验证"""
        # 安全URL
        safe_urls = [
            "https://example.com",
            "https://api.example.com/v1/users",
            "http://localhost:8000"
        ]
        
        for url in safe_urls:
            assert security_validator.validate_url(url) is True
        
        # 可疑URL
        suspicious_urls = [
            "javascript:alert('xss')",
            "data:text/html,<script>alert('xss')</script>",
            "file:///etc/passwd"
        ]
        
        for url in suspicious_urls:
            with pytest.raises(SecurityError, match="URL协议不被允许"):
                security_validator.validate_url(url)


class TestEncryptionManager:
    """加密管理器测试类"""
    
    @pytest.fixture
    def encryption_manager(self):
        """创建加密管理器实例"""
        return EncryptionManager(key="test_encryption_key_32_bytes_long")
    
    def test_encrypt_decrypt_text(self, encryption_manager):
        """测试文本加密解密"""
        original_text = "这是需要加密的敏感信息"
        
        # 加密
        encrypted = encryption_manager.encrypt(original_text)
        assert encrypted != original_text
        assert isinstance(encrypted, str)
        
        # 解密
        decrypted = encryption_manager.decrypt(encrypted)
        assert decrypted == original_text
    
    def test_encrypt_decrypt_data(self, encryption_manager):
        """测试数据加密解密"""
        original_data = {
            "user_id": 123,
            "email": "<EMAIL>",
            "sensitive_info": "secret data"
        }
        
        # 加密
        encrypted = encryption_manager.encrypt_data(original_data)
        assert encrypted != original_data
        
        # 解密
        decrypted = encryption_manager.decrypt_data(encrypted)
        assert decrypted == original_data
    
    def test_hash_data(self, encryption_manager):
        """测试数据哈希"""
        data = "sensitive information"
        
        # 生成哈希
        hash1 = encryption_manager.hash_data(data)
        hash2 = encryption_manager.hash_data(data)
        
        # 相同数据应该产生相同哈希
        assert hash1 == hash2
        
        # 不同数据应该产生不同哈希
        different_hash = encryption_manager.hash_data("different data")
        assert hash1 != different_hash
    
    def test_generate_salt(self, encryption_manager):
        """测试盐值生成"""
        salt1 = encryption_manager.generate_salt()
        salt2 = encryption_manager.generate_salt()
        
        # 每次生成的盐值应该不同
        assert salt1 != salt2
        assert len(salt1) > 0
        assert len(salt2) > 0
    
    def test_key_derivation(self, encryption_manager):
        """测试密钥派生"""
        password = "user_password"
        salt = encryption_manager.generate_salt()
        
        # 派生密钥
        key1 = encryption_manager.derive_key(password, salt)
        key2 = encryption_manager.derive_key(password, salt)
        
        # 相同密码和盐值应该产生相同密钥
        assert key1 == key2
        
        # 不同盐值应该产生不同密钥
        different_salt = encryption_manager.generate_salt()
        key3 = encryption_manager.derive_key(password, different_salt)
        assert key1 != key3


class TestInputSanitizer:
    """输入清理器测试类"""
    
    @pytest.fixture
    def sanitizer(self):
        """创建输入清理器实例"""
        return InputSanitizer()
    
    def test_sanitize_html(self, sanitizer):
        """测试HTML清理"""
        # 包含恶意脚本的HTML
        dirty_html = """
        <div>正常内容</div>
        <script>alert('xss')</script>
        <img src="x" onerror="alert('xss')">
        <p>更多正常内容</p>
        """
        
        clean_html = sanitizer.sanitize_html(dirty_html)
        
        # 应该保留安全内容
        assert "正常内容" in clean_html
        assert "更多正常内容" in clean_html
        
        # 应该移除恶意脚本
        assert "<script>" not in clean_html
        assert "alert('xss')" not in clean_html
        assert "onerror" not in clean_html
    
    def test_sanitize_sql(self, sanitizer):
        """测试SQL清理"""
        # 包含SQL注入的输入
        dirty_inputs = [
            "'; DROP TABLE users; --",
            "1' OR '1'='1",
            "admin'--",
            "normal input"
        ]
        
        for dirty_input in dirty_inputs:
            clean_input = sanitizer.sanitize_sql(dirty_input)
            
            # 应该移除或转义危险字符
            assert "DROP TABLE" not in clean_input
            assert "';" not in clean_input or "\\';" in clean_input
    
    def test_sanitize_filename(self, sanitizer):
        """测试文件名清理"""
        # 危险文件名
        dangerous_filenames = [
            "../../../etc/passwd",
            "..\\..\\windows\\system32",
            "file<script>.txt",
            "normal_file.txt"
        ]
        
        for filename in dangerous_filenames:
            clean_filename = sanitizer.sanitize_filename(filename)
            
            # 应该移除路径遍历字符
            assert "../" not in clean_filename
            assert "..\\" not in clean_filename
            assert "<script>" not in clean_filename
    
    def test_sanitize_email(self, sanitizer):
        """测试邮箱清理"""
        emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<script>alert('xss')</script>@example.com",
            "user@<script>example.com"
        ]
        
        for email in emails:
            clean_email = sanitizer.sanitize_email(email)
            
            # 应该移除脚本标签
            assert "<script>" not in clean_email
            assert "alert" not in clean_email
    
    def test_sanitize_phone(self, sanitizer):
        """测试电话号码清理"""
        phones = [
            "+86 138-0013-8000",
            "138 0013 8000",
            "13800138000<script>",
            "(138) 0013-8000"
        ]
        
        for phone in phones:
            clean_phone = sanitizer.sanitize_phone(phone)
            
            # 应该只保留数字、空格、连字符和括号
            assert "<script>" not in clean_phone
            # 应该保留有效的电话号码字符
            assert any(c.isdigit() for c in clean_phone)


class TestCSRFProtection:
    """CSRF保护测试类"""
    
    @pytest.fixture
    def csrf_protection(self):
        """创建CSRF保护实例"""
        return CSRFProtection(secret_key="csrf_secret_key")
    
    def test_generate_csrf_token(self, csrf_protection):
        """测试CSRF令牌生成"""
        session_id = "session123"
        
        token = csrf_protection.generate_token(session_id)
        
        assert isinstance(token, str)
        assert len(token) > 0
        
        # 每次生成应该不同（包含时间戳）
        token2 = csrf_protection.generate_token(session_id)
        assert token != token2
    
    def test_validate_csrf_token(self, csrf_protection):
        """测试CSRF令牌验证"""
        session_id = "session123"
        
        # 生成令牌
        token = csrf_protection.generate_token(session_id)
        
        # 验证有效令牌
        assert csrf_protection.validate_token(token, session_id) is True
        
        # 验证无效令牌
        assert csrf_protection.validate_token("invalid_token", session_id) is False
        
        # 验证错误的会话ID
        assert csrf_protection.validate_token(token, "wrong_session") is False
    
    def test_csrf_token_expiry(self, csrf_protection):
        """测试CSRF令牌过期"""
        session_id = "session123"
        
        # 生成短期令牌
        token = csrf_protection.generate_token(session_id, expires_in=1)
        
        # 立即验证应该成功
        assert csrf_protection.validate_token(token, session_id) is True
        
        # 等待过期
        time.sleep(2)
        
        # 过期令牌应该验证失败
        assert csrf_protection.validate_token(token, session_id) is False


class TestAuditLogger:
    """审计日志器测试类"""
    
    @pytest.fixture
    def audit_logger(self):
        """创建审计日志器实例"""
        return AuditLogger()
    
    def test_log_user_action(self, audit_logger):
        """测试用户操作日志"""
        with patch.object(audit_logger, 'log_event') as mock_log:
            audit_logger.log_user_action(
                user_id="user123",
                action="login",
                resource="system",
                ip_address="***********",
                user_agent="Mozilla/5.0..."
            )
            
            mock_log.assert_called_once()
            call_args = mock_log.call_args[1]
            assert call_args["event_type"] == "user_action"
            assert call_args["user_id"] == "user123"
            assert call_args["action"] == "login"
    
    def test_log_security_event(self, audit_logger):
        """测试安全事件日志"""
        with patch.object(audit_logger, 'log_event') as mock_log:
            audit_logger.log_security_event(
                event_type="failed_login",
                user_id="user123",
                ip_address="***********",
                details={"attempts": 3}
            )
            
            mock_log.assert_called_once()
            call_args = mock_log.call_args[1]
            assert call_args["event_type"] == "failed_login"
            assert call_args["severity"] == "medium"
    
    def test_log_data_access(self, audit_logger):
        """测试数据访问日志"""
        with patch.object(audit_logger, 'log_event') as mock_log:
            audit_logger.log_data_access(
                user_id="user123",
                resource_type="user_profile",
                resource_id="profile456",
                action="read",
                sensitive=True
            )
            
            mock_log.assert_called_once()
            call_args = mock_log.call_args[1]
            assert call_args["event_type"] == "data_access"
            assert call_args["sensitive"] is True
    
    def test_log_system_event(self, audit_logger):
        """测试系统事件日志"""
        with patch.object(audit_logger, 'log_event') as mock_log:
            audit_logger.log_system_event(
                event_type="service_start",
                service="api_server",
                details={"port": 8000}
            )
            
            mock_log.assert_called_once()
            call_args = mock_log.call_args[1]
            assert call_args["event_type"] == "service_start"
            assert call_args["service"] == "api_server"


class TestSecurityIntegration:
    """安全集成测试类"""
    
    def test_complete_authentication_flow(self):
        """测试完整认证流程"""
        password_manager = PasswordManager()
        token_manager = TokenManager(secret_key="test_secret")
        
        # 1. 用户注册
        username = "testuser"
        password = "SecureP@ssw0rd123"
        
        # 检查密码强度
        strength = password_manager.check_password_strength(password)
        assert strength["is_strong"] is True
        
        # 哈希密码
        hashed_password = password_manager.hash_password(password)
        
        # 2. 用户登录
        # 验证密码
        assert password_manager.verify_password(password, hashed_password) is True
        
        # 生成JWT令牌
        payload = {"user_id": 123, "username": username}
        access_token = token_manager.generate_jwt_token(payload, expires_in=3600)
        refresh_token = token_manager.generate_refresh_token(123)
        
        # 3. 验证令牌
        decoded = token_manager.verify_jwt_token(access_token)
        assert decoded["user_id"] == 123
        assert decoded["username"] == username
        
        # 4. 刷新令牌
        assert token_manager.verify_refresh_token(refresh_token, 123) is True
        
        # 5. 登出（撤销令牌）
        token_manager.blacklist_token(access_token)
        token_manager.revoke_refresh_token(refresh_token)
        
        # 验证令牌已被撤销
        with pytest.raises(AuthenticationError):
            token_manager.verify_jwt_token(access_token)
        
        assert token_manager.verify_refresh_token(refresh_token, 123) is False
    
    def test_api_security_middleware(self):
        """测试API安全中间件"""
        rate_limiter = RateLimiter()
        api_key_manager = APIKeyManager()
        security_validator = SecurityValidator()
        
        # 设置API密钥
        user_id = "user123"
        api_key = api_key_manager.generate_api_key()
        api_key_manager.register_api_key(user_id, api_key, permissions=["read", "write"])
        
        # 设置速率限制
        rate_limiter.set_limit(api_key, requests=100, window=3600)
        
        # 模拟API请求
        def process_api_request(api_key, endpoint, data):
            # 1. 验证API密钥
            user_id = api_key_manager.validate_api_key(api_key)
            if not user_id:
                raise AuthenticationError("无效的API密钥")
            
            # 2. 检查权限
            required_permission = "write" if endpoint.startswith("/api/write") else "read"
            if not api_key_manager.has_permission(api_key, required_permission):
                raise AuthorizationError("权限不足")
            
            # 3. 检查速率限制
            if not rate_limiter.is_allowed(api_key):
                raise RateLimitError("请求过于频繁")
            
            # 4. 验证输入数据
            if data:
                security_validator.validate_sql_injection(str(data))
                security_validator.validate_xss(str(data))
            
            return {"status": "success", "user_id": user_id}
        
        # 测试成功请求
        result = process_api_request(api_key, "/api/read/users", "normal data")
        assert result["status"] == "success"
        assert result["user_id"] == user_id
        
        # 测试无效API密钥
        with pytest.raises(AuthenticationError):
            process_api_request("invalid_key", "/api/read/users", None)
        
        # 测试权限不足
        read_only_key = api_key_manager.generate_api_key()
        api_key_manager.register_api_key("user456", read_only_key, permissions=["read"])
        
        with pytest.raises(AuthorizationError):
            process_api_request(read_only_key, "/api/write/users", "data")
        
        # 测试恶意输入
        with pytest.raises(SecurityError):
            process_api_request(api_key, "/api/read/users", "'; DROP TABLE users; --")
    
    def test_data_protection_workflow(self):
        """测试数据保护工作流"""
        encryption_manager = EncryptionManager(key="test_key_32_bytes_long_enough")
        sanitizer = InputSanitizer()
        audit_logger = AuditLogger()
        
        # 模拟敏感数据处理
        def process_sensitive_data(user_id, raw_data):
            # 1. 清理输入数据
            clean_data = {
                "email": sanitizer.sanitize_email(raw_data.get("email", "")),
                "phone": sanitizer.sanitize_phone(raw_data.get("phone", "")),
                "notes": sanitizer.sanitize_html(raw_data.get("notes", ""))
            }
            
            # 2. 加密敏感信息
            encrypted_data = encryption_manager.encrypt_data(clean_data)
            
            # 3. 记录审计日志
            with patch.object(audit_logger, 'log_event'):
                audit_logger.log_data_access(
                    user_id=user_id,
                    resource_type="user_data",
                    resource_id="data123",
                    action="create",
                    sensitive=True
                )
            
            return encrypted_data
        
        # 测试数据处理
        raw_data = {
            "email": "<EMAIL><script>alert('xss')</script>",
            "phone": "+86 138-0013-8000<script>",
            "notes": "<p>正常内容</p><script>alert('xss')</script>"
        }
        
        encrypted_result = process_sensitive_data("user123", raw_data)
        
        # 验证数据已被加密
        assert encrypted_result != raw_data
        
        # 解密并验证清理效果
        decrypted_data = encryption_manager.decrypt_data(encrypted_result)
        
        assert "<script>" not in decrypted_data["email"]
        assert "<script>" not in decrypted_data["phone"]
        assert "<script>" not in decrypted_data["notes"]
        assert "正常内容" in decrypted_data["notes"]