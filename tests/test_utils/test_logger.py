import json
import logging
import os
import tempfile
from datetime import datetime
from unittest.mock import Mock, patch

import pytest
from src.utils.logger import (
    Lo<PERSON>,
    StructuredLogger,
    PerformanceLogger,
    SecurityLogger,
    LogFormatter,
    JSONFormatter,
    ColoredFormatter,
    Log<PERSON>ilter,
    SensitiveDataFilter,
    LogRotationHandler,
    LogAggregator,
    LogAnalyzer,
    setup_logging,
    get_logger
)


class TestLogger:
    """基础日志器测试类"""
    
    @pytest.fixture
    def temp_log_file(self):
        """创建临时日志文件"""
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.log') as f:
            temp_file = f.name
        yield temp_file
        # 清理
        if os.path.exists(temp_file):
            os.unlink(temp_file)
    
    @pytest.fixture
    def logger(self, temp_log_file):
        """创建日志器实例"""
        return Logger(
            name="test_logger",
            level=logging.DEBUG,
            log_file=temp_log_file
        )
    
    def test_logger_initialization(self, logger):
        """测试日志器初始化"""
        assert logger.name == "test_logger"
        assert logger.logger.level == logging.DEBUG
        assert len(logger.logger.handlers) > 0
    
    def test_basic_logging_methods(self, logger, temp_log_file):
        """测试基本日志方法"""
        logger.debug("Debug message")
        logger.info("Info message")
        logger.warning("Warning message")
        logger.error("Error message")
        logger.critical("Critical message")
        
        # 检查日志文件内容
        with open(temp_log_file, 'r', encoding='utf-8') as f:
            log_content = f.read()
        
        assert "Debug message" in log_content
        assert "Info message" in log_content
        assert "Warning message" in log_content
        assert "Error message" in log_content
        assert "Critical message" in log_content
    
    def test_logging_with_extra_data(self, logger, temp_log_file):
        """测试带额外数据的日志"""
        extra_data = {
            "user_id": 123,
            "action": "login",
            "ip_address": "***********"
        }
        
        logger.info("User login", extra=extra_data)
        
        with open(temp_log_file, 'r', encoding='utf-8') as f:
            log_content = f.read()
        
        assert "User login" in log_content
        # 注意：extra数据的格式取决于formatter
    
    def test_exception_logging(self, logger, temp_log_file):
        """测试异常日志"""
        try:
            raise ValueError("Test exception")
        except ValueError:
            logger.exception("An error occurred")
        
        with open(temp_log_file, 'r', encoding='utf-8') as f:
            log_content = f.read()
        
        assert "An error occurred" in log_content
        assert "ValueError" in log_content
        assert "Test exception" in log_content
        assert "Traceback" in log_content
    
    def test_log_level_filtering(self, temp_log_file):
        """测试日志级别过滤"""
        # 创建INFO级别的日志器
        info_logger = Logger(
            name="info_logger",
            level=logging.INFO,
            log_file=temp_log_file
        )
        
        info_logger.debug("Debug message")  # 不应该记录
        info_logger.info("Info message")    # 应该记录
        info_logger.error("Error message")  # 应该记录
        
        with open(temp_log_file, 'r', encoding='utf-8') as f:
            log_content = f.read()
        
        assert "Debug message" not in log_content
        assert "Info message" in log_content
        assert "Error message" in log_content
    
    def test_multiple_handlers(self, temp_log_file):
        """测试多个处理器"""
        # 创建带控制台和文件处理器的日志器
        logger = Logger(
            name="multi_handler_logger",
            level=logging.INFO,
            log_file=temp_log_file,
            console_output=True
        )
        
        # 应该有文件和控制台两个处理器
        assert len(logger.logger.handlers) >= 2
        
        logger.info("Test message")
        
        # 检查文件中是否有日志
        with open(temp_log_file, 'r', encoding='utf-8') as f:
            log_content = f.read()
        
        assert "Test message" in log_content


class TestStructuredLogger:
    """结构化日志器测试类"""
    
    @pytest.fixture
    def temp_log_file(self):
        """创建临时日志文件"""
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.log') as f:
            temp_file = f.name
        yield temp_file
        if os.path.exists(temp_file):
            os.unlink(temp_file)
    
    @pytest.fixture
    def structured_logger(self, temp_log_file):
        """创建结构化日志器实例"""
        return StructuredLogger(
            name="structured_test",
            log_file=temp_log_file
        )
    
    def test_structured_logging(self, structured_logger, temp_log_file):
        """测试结构化日志"""
        log_data = {
            "event": "user_action",
            "user_id": 123,
            "action": "create_session",
            "session_id": "sess_456",
            "timestamp": datetime.now().isoformat(),
            "metadata": {
                "ip_address": "***********",
                "user_agent": "Mozilla/5.0..."
            }
        }
        
        structured_logger.log_structured("info", "User created session", log_data)
        
        with open(temp_log_file, 'r', encoding='utf-8') as f:
            log_content = f.read()
        
        # 检查是否包含结构化数据
        assert "user_action" in log_content
        assert "123" in log_content
        assert "create_session" in log_content
    
    def test_json_output(self, structured_logger, temp_log_file):
        """测试JSON输出"""
        structured_logger.set_json_output(True)
        
        log_data = {
            "event": "api_request",
            "method": "POST",
            "endpoint": "/api/sessions",
            "status_code": 201
        }
        
        structured_logger.log_structured("info", "API request", log_data)
        
        with open(temp_log_file, 'r', encoding='utf-8') as f:
            log_lines = f.readlines()
        
        # 最后一行应该是有效的JSON
        if log_lines:
            last_line = log_lines[-1].strip()
            try:
                parsed_json = json.loads(last_line)
                assert "event" in parsed_json
                assert parsed_json["event"] == "api_request"
            except json.JSONDecodeError:
                pytest.fail("Log output is not valid JSON")
    
    def test_correlation_id(self, structured_logger):
        """测试关联ID"""
        correlation_id = "corr_123456"
        structured_logger.set_correlation_id(correlation_id)
        
        # 模拟日志记录
        with patch.object(structured_logger.logger, 'info') as mock_info:
            structured_logger.info("Test message")
            
            # 检查是否包含关联ID
            call_args = mock_info.call_args
            assert correlation_id in str(call_args)
    
    def test_context_manager(self, structured_logger):
        """测试上下文管理器"""
        context = {
            "request_id": "req_789",
            "user_id": 456
        }
        
        with structured_logger.context(**context):
            with patch.object(structured_logger.logger, 'info') as mock_info:
                structured_logger.info("Message with context")
                
                # 检查上下文是否被包含
                call_args = str(mock_info.call_args)
                assert "req_789" in call_args
                assert "456" in call_args


class TestPerformanceLogger:
    """性能日志器测试类"""
    
    @pytest.fixture
    def perf_logger(self):
        """创建性能日志器实例"""
        return PerformanceLogger(name="perf_test")
    
    def test_timing_decorator(self, perf_logger):
        """测试计时装饰器"""
        @perf_logger.time_it
        def slow_function():
            import time
            time.sleep(0.1)
            return "result"
        
        with patch.object(perf_logger, 'log_timing') as mock_log:
            result = slow_function()
            
            assert result == "result"
            mock_log.assert_called_once()
            
            # 检查记录的时间
            call_args = mock_log.call_args[0]
            assert call_args[0] == "slow_function"  # 函数名
            assert call_args[1] >= 0.1  # 执行时间应该至少0.1秒
    
    def test_timing_context_manager(self, perf_logger):
        """测试计时上下文管理器"""
        with patch.object(perf_logger, 'log_timing') as mock_log:
            with perf_logger.timer("test_operation"):
                import time
                time.sleep(0.05)
            
            mock_log.assert_called_once()
            call_args = mock_log.call_args[0]
            assert call_args[0] == "test_operation"
            assert call_args[1] >= 0.05
    
    def test_memory_usage_logging(self, perf_logger):
        """测试内存使用日志"""
        with patch.object(perf_logger, 'log_memory_usage') as mock_log:
            perf_logger.log_current_memory("test_checkpoint")
            
            mock_log.assert_called_once()
            call_args = mock_log.call_args[0]
            assert call_args[0] == "test_checkpoint"
            assert isinstance(call_args[1], (int, float))  # 内存使用量
    
    def test_performance_metrics(self, perf_logger):
        """测试性能指标"""
        # 记录一些性能数据
        perf_logger.log_timing("operation_a", 0.1)
        perf_logger.log_timing("operation_a", 0.2)
        perf_logger.log_timing("operation_b", 0.3)
        
        metrics = perf_logger.get_metrics()
        
        assert "operation_a" in metrics
        assert "operation_b" in metrics
        assert metrics["operation_a"]["count"] == 2
        assert metrics["operation_a"]["avg_time"] == 0.15
        assert metrics["operation_b"]["count"] == 1


class TestSecurityLogger:
    """安全日志器测试类"""
    
    @pytest.fixture
    def security_logger(self):
        """创建安全日志器实例"""
        return SecurityLogger(name="security_test")
    
    def test_authentication_logging(self, security_logger):
        """测试认证日志"""
        with patch.object(security_logger, 'log_security_event') as mock_log:
            security_logger.log_authentication(
                user_id="user123",
                success=True,
                ip_address="***********",
                user_agent="Mozilla/5.0..."
            )
            
            mock_log.assert_called_once()
            call_args = mock_log.call_args[1]  # kwargs
            assert call_args["event_type"] == "authentication"
            assert call_args["user_id"] == "user123"
            assert call_args["success"] is True
    
    def test_authorization_logging(self, security_logger):
        """测试授权日志"""
        with patch.object(security_logger, 'log_security_event') as mock_log:
            security_logger.log_authorization(
                user_id="user123",
                resource="/api/admin",
                action="read",
                granted=False,
                reason="insufficient_permissions"
            )
            
            mock_log.assert_called_once()
            call_args = mock_log.call_args[1]
            assert call_args["event_type"] == "authorization"
            assert call_args["granted"] is False
            assert call_args["reason"] == "insufficient_permissions"
    
    def test_suspicious_activity_logging(self, security_logger):
        """测试可疑活动日志"""
        with patch.object(security_logger, 'log_security_event') as mock_log:
            security_logger.log_suspicious_activity(
                event_type="multiple_failed_logins",
                user_id="user123",
                ip_address="***********",
                details={"failed_attempts": 5, "time_window": "5min"}
            )
            
            mock_log.assert_called_once()
            call_args = mock_log.call_args[1]
            assert call_args["event_type"] == "multiple_failed_logins"
            assert call_args["severity"] == "high"
    
    def test_data_access_logging(self, security_logger):
        """测试数据访问日志"""
        with patch.object(security_logger, 'log_security_event') as mock_log:
            security_logger.log_data_access(
                user_id="user123",
                resource_type="user_data",
                resource_id="user456",
                action="read",
                sensitive=True
            )
            
            mock_log.assert_called_once()
            call_args = mock_log.call_args[1]
            assert call_args["event_type"] == "data_access"
            assert call_args["sensitive"] is True


class TestLogFormatters:
    """日志格式化器测试类"""
    
    def test_json_formatter(self):
        """测试JSON格式化器"""
        formatter = JSONFormatter()
        
        # 创建日志记录
        record = logging.LogRecord(
            name="test",
            level=logging.INFO,
            pathname="test.py",
            lineno=10,
            msg="Test message",
            args=(),
            exc_info=None
        )
        record.user_id = 123
        record.action = "test_action"
        
        formatted = formatter.format(record)
        
        # 解析JSON
        try:
            parsed = json.loads(formatted)
            assert parsed["message"] == "Test message"
            assert parsed["level"] == "INFO"
            assert parsed["user_id"] == 123
            assert parsed["action"] == "test_action"
        except json.JSONDecodeError:
            pytest.fail("Formatted output is not valid JSON")
    
    def test_colored_formatter(self):
        """测试彩色格式化器"""
        formatter = ColoredFormatter()
        
        # 测试不同级别的颜色
        levels = [
            (logging.DEBUG, "DEBUG message"),
            (logging.INFO, "INFO message"),
            (logging.WARNING, "WARNING message"),
            (logging.ERROR, "ERROR message"),
            (logging.CRITICAL, "CRITICAL message")
        ]
        
        for level, message in levels:
            record = logging.LogRecord(
                name="test",
                level=level,
                pathname="test.py",
                lineno=10,
                msg=message,
                args=(),
                exc_info=None
            )
            
            formatted = formatter.format(record)
            assert message in formatted
            # 彩色格式化器应该包含ANSI转义序列
            assert "\033[" in formatted or message in formatted
    
    def test_custom_log_formatter(self):
        """测试自定义日志格式化器"""
        custom_format = "[{asctime}] {name} - {levelname}: {message}"
        formatter = LogFormatter(fmt=custom_format, style='{')
        
        record = logging.LogRecord(
            name="test_logger",
            level=logging.INFO,
            pathname="test.py",
            lineno=10,
            msg="Custom format test",
            args=(),
            exc_info=None
        )
        
        formatted = formatter.format(record)
        assert "test_logger" in formatted
        assert "INFO" in formatted
        assert "Custom format test" in formatted


class TestLogFilters:
    """日志过滤器测试类"""
    
    def test_sensitive_data_filter(self):
        """测试敏感数据过滤器"""
        sensitive_patterns = [r'password=\w+', r'api_key=\w+']
        filter_obj = SensitiveDataFilter(sensitive_patterns)
        
        # 创建包含敏感数据的日志记录
        record = logging.LogRecord(
            name="test",
            level=logging.INFO,
            pathname="test.py",
            lineno=10,
            msg="Login attempt with password=secret123 and api_key=abc123",
            args=(),
            exc_info=None
        )
        
        # 应用过滤器
        result = filter_obj.filter(record)
        
        # 过滤器应该返回True（允许记录），但消息应该被清理
        assert result is True
        assert "password=secret123" not in record.msg
        assert "api_key=abc123" not in record.msg
        assert "password=***" in record.msg
        assert "api_key=***" in record.msg
    
    def test_level_filter(self):
        """测试级别过滤器"""
        # 只允许ERROR及以上级别
        filter_obj = LogFilter(min_level=logging.ERROR)
        
        # 测试不同级别
        levels = [
            (logging.DEBUG, False),
            (logging.INFO, False),
            (logging.WARNING, False),
            (logging.ERROR, True),
            (logging.CRITICAL, True)
        ]
        
        for level, should_pass in levels:
            record = logging.LogRecord(
                name="test",
                level=level,
                pathname="test.py",
                lineno=10,
                msg="Test message",
                args=(),
                exc_info=None
            )
            
            result = filter_obj.filter(record)
            assert result == should_pass
    
    def test_custom_filter(self):
        """测试自定义过滤器"""
        def custom_filter_func(record):
            # 只允许包含"important"的消息
            return "important" in record.msg.lower()
        
        filter_obj = LogFilter(custom_filter=custom_filter_func)
        
        # 测试消息
        messages = [
            ("This is important", True),
            ("IMPORTANT update", True),
            ("Regular message", False),
            ("Nothing special", False)
        ]
        
        for message, should_pass in messages:
            record = logging.LogRecord(
                name="test",
                level=logging.INFO,
                pathname="test.py",
                lineno=10,
                msg=message,
                args=(),
                exc_info=None
            )
            
            result = filter_obj.filter(record)
            assert result == should_pass


class TestLogRotationHandler:
    """日志轮转处理器测试类"""
    
    @pytest.fixture
    def temp_log_dir(self):
        """创建临时日志目录"""
        import tempfile
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        # 清理
        import shutil
        shutil.rmtree(temp_dir, ignore_errors=True)
    
    def test_size_based_rotation(self, temp_log_dir):
        """测试基于大小的日志轮转"""
        log_file = os.path.join(temp_log_dir, "test.log")
        
        # 创建小的最大文件大小以便测试
        handler = LogRotationHandler(
            filename=log_file,
            max_bytes=1024,  # 1KB
            backup_count=3
        )
        
        # 写入大量日志以触发轮转
        logger = logging.getLogger("rotation_test")
        logger.addHandler(handler)
        logger.setLevel(logging.INFO)
        
        # 写入足够的数据触发轮转
        for i in range(100):
            logger.info(f"This is log message number {i} with some additional text to make it longer")
        
        # 检查是否创建了轮转文件
        log_files = [f for f in os.listdir(temp_log_dir) if f.startswith("test.log")]
        assert len(log_files) > 1  # 应该有原文件和至少一个轮转文件
    
    def test_time_based_rotation(self, temp_log_dir):
        """测试基于时间的日志轮转"""
        log_file = os.path.join(temp_log_dir, "time_test.log")
        
        # 创建基于时间的轮转处理器
        handler = LogRotationHandler(
            filename=log_file,
            when='S',  # 每秒轮转（仅用于测试）
            interval=1,
            backup_count=3
        )
        
        logger = logging.getLogger("time_rotation_test")
        logger.addHandler(handler)
        logger.setLevel(logging.INFO)
        
        # 写入日志
        logger.info("First message")
        
        # 等待一秒以触发时间轮转
        import time
        time.sleep(1.1)
        
        logger.info("Second message")
        
        # 检查文件
        log_files = [f for f in os.listdir(temp_log_dir) if f.startswith("time_test.log")]
        # 注意：时间轮转可能需要更长时间才能看到效果
        assert len(log_files) >= 1


class TestLogAggregator:
    """日志聚合器测试类"""
    
    @pytest.fixture
    def log_aggregator(self):
        """创建日志聚合器实例"""
        return LogAggregator()
    
    def test_add_log_source(self, log_aggregator):
        """测试添加日志源"""
        mock_logger = Mock()
        log_aggregator.add_source("test_source", mock_logger)
        
        assert "test_source" in log_aggregator.sources
        assert log_aggregator.sources["test_source"] == mock_logger
    
    def test_aggregate_logs(self, log_aggregator):
        """测试日志聚合"""
        # 创建模拟日志源
        source1 = Mock()
        source1.get_logs.return_value = [
            {"timestamp": "2023-01-01T10:00:00", "level": "INFO", "message": "Log 1"},
            {"timestamp": "2023-01-01T10:01:00", "level": "ERROR", "message": "Log 2"}
        ]
        
        source2 = Mock()
        source2.get_logs.return_value = [
            {"timestamp": "2023-01-01T10:00:30", "level": "WARNING", "message": "Log 3"}
        ]
        
        log_aggregator.add_source("source1", source1)
        log_aggregator.add_source("source2", source2)
        
        # 聚合日志
        aggregated = log_aggregator.aggregate_logs(
            start_time="2023-01-01T10:00:00",
            end_time="2023-01-01T10:02:00"
        )
        
        assert len(aggregated) == 3
        # 检查是否按时间排序
        timestamps = [log["timestamp"] for log in aggregated]
        assert timestamps == sorted(timestamps)
    
    def test_filter_aggregated_logs(self, log_aggregator):
        """测试过滤聚合日志"""
        logs = [
            {"timestamp": "2023-01-01T10:00:00", "level": "INFO", "message": "Info log"},
            {"timestamp": "2023-01-01T10:01:00", "level": "ERROR", "message": "Error log"},
            {"timestamp": "2023-01-01T10:02:00", "level": "WARNING", "message": "Warning log"}
        ]
        
        # 只过滤ERROR级别
        filtered = log_aggregator.filter_logs(logs, level="ERROR")
        assert len(filtered) == 1
        assert filtered[0]["level"] == "ERROR"
        
        # 按消息内容过滤
        filtered = log_aggregator.filter_logs(logs, message_pattern="Info")
        assert len(filtered) == 1
        assert "Info" in filtered[0]["message"]


class TestLogAnalyzer:
    """日志分析器测试类"""
    
    @pytest.fixture
    def log_analyzer(self):
        """创建日志分析器实例"""
        return LogAnalyzer()
    
    def test_analyze_log_levels(self, log_analyzer):
        """测试日志级别分析"""
        logs = [
            {"level": "INFO", "message": "Info 1"},
            {"level": "INFO", "message": "Info 2"},
            {"level": "ERROR", "message": "Error 1"},
            {"level": "WARNING", "message": "Warning 1"},
            {"level": "ERROR", "message": "Error 2"}
        ]
        
        analysis = log_analyzer.analyze_levels(logs)
        
        assert analysis["INFO"] == 2
        assert analysis["ERROR"] == 2
        assert analysis["WARNING"] == 1
        assert analysis["total"] == 5
    
    def test_analyze_error_patterns(self, log_analyzer):
        """测试错误模式分析"""
        logs = [
            {"level": "ERROR", "message": "Database connection failed"},
            {"level": "ERROR", "message": "Database timeout error"},
            {"level": "ERROR", "message": "API rate limit exceeded"},
            {"level": "ERROR", "message": "Database connection failed"},
            {"level": "INFO", "message": "Normal operation"}
        ]
        
        patterns = log_analyzer.analyze_error_patterns(logs)
        
        # 应该识别出数据库相关错误模式
        assert any("database" in pattern.lower() for pattern in patterns)
        assert patterns["Database connection failed"] == 2
    
    def test_analyze_time_distribution(self, log_analyzer):
        """测试时间分布分析"""
        logs = [
            {"timestamp": "2023-01-01T10:00:00", "level": "INFO"},
            {"timestamp": "2023-01-01T10:15:00", "level": "INFO"},
            {"timestamp": "2023-01-01T10:30:00", "level": "ERROR"},
            {"timestamp": "2023-01-01T10:45:00", "level": "INFO"},
            {"timestamp": "2023-01-01T11:00:00", "level": "WARNING"}
        ]
        
        distribution = log_analyzer.analyze_time_distribution(logs, interval="hour")
        
        assert "2023-01-01T10" in distribution
        assert "2023-01-01T11" in distribution
        assert distribution["2023-01-01T10"] == 4
        assert distribution["2023-01-01T11"] == 1
    
    def test_detect_anomalies(self, log_analyzer):
        """测试异常检测"""
        # 正常日志模式
        normal_logs = [
            {"timestamp": f"2023-01-01T10:{i:02d}:00", "level": "INFO", "message": "Normal operation"}
            for i in range(30)
        ]
        
        # 异常日志（大量错误）
        anomaly_logs = [
            {"timestamp": f"2023-01-01T10:{30+i:02d}:00", "level": "ERROR", "message": "System error"}
            for i in range(10)
        ]
        
        all_logs = normal_logs + anomaly_logs
        
        anomalies = log_analyzer.detect_anomalies(all_logs)
        
        # 应该检测到错误率异常
        assert len(anomalies) > 0
        assert any("error_rate" in anomaly["type"] for anomaly in anomalies)
    
    def test_generate_report(self, log_analyzer):
        """测试生成报告"""
        logs = [
            {"timestamp": "2023-01-01T10:00:00", "level": "INFO", "message": "Info message"},
            {"timestamp": "2023-01-01T10:01:00", "level": "ERROR", "message": "Error message"},
            {"timestamp": "2023-01-01T10:02:00", "level": "WARNING", "message": "Warning message"}
        ]
        
        report = log_analyzer.generate_report(logs)
        
        assert "summary" in report
        assert "level_distribution" in report
        assert "time_distribution" in report
        assert "error_patterns" in report
        assert "anomalies" in report
        
        assert report["summary"]["total_logs"] == 3
        assert report["summary"]["error_count"] == 1


class TestLoggingSetup:
    """日志设置测试类"""
    
    def test_setup_logging_basic(self):
        """测试基本日志设置"""
        config = {
            "level": "INFO",
            "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            "handlers": {
                "console": {
                    "class": "logging.StreamHandler",
                    "level": "INFO"
                }
            }
        }
        
        setup_logging(config)
        
        # 测试日志器是否正确配置
        logger = get_logger("test_setup")
        assert logger.level <= logging.INFO
    
    def test_get_logger(self):
        """测试获取日志器"""
        logger1 = get_logger("test_logger_1")
        logger2 = get_logger("test_logger_1")  # 相同名称
        logger3 = get_logger("test_logger_2")  # 不同名称
        
        # 相同名称应该返回同一个实例
        assert logger1 is logger2
        # 不同名称应该返回不同实例
        assert logger1 is not logger3
    
    @pytest.mark.parametrize("level_name,level_value", [
        ("DEBUG", logging.DEBUG),
        ("INFO", logging.INFO),
        ("WARNING", logging.WARNING),
        ("ERROR", logging.ERROR),
        ("CRITICAL", logging.CRITICAL)
    ])
    def test_logging_levels(self, level_name, level_value):
        """测试不同日志级别"""
        config = {
            "level": level_name,
            "handlers": {
                "console": {
                    "class": "logging.StreamHandler",
                    "level": level_name
                }
            }
        }
        
        setup_logging(config)
        logger = get_logger(f"test_{level_name.lower()}")
        
        # 检查日志级别是否正确设置
        assert logger.level <= level_value