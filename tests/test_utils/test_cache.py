import asyncio
import time
from unittest.mock import Mock, patch

import pytest
from src.utils.cache import (
    MemoryCache,
    RedisCache,
    CacheManager,
    cache_decorator,
    async_cache_decorator,
    CacheStats,
    CacheError
)


class TestMemoryCache:
    """内存缓存测试类"""
    
    @pytest.fixture
    def memory_cache(self):
        """创建内存缓存实例"""
        return MemoryCache(max_size=100, default_ttl=300)
    
    def test_cache_initialization(self, memory_cache):
        """测试缓存初始化"""
        assert memory_cache.max_size == 100
        assert memory_cache.default_ttl == 300
        assert memory_cache.size() == 0
        assert memory_cache.is_empty() is True
    
    def test_set_and_get(self, memory_cache):
        """测试设置和获取缓存"""
        # 设置缓存
        memory_cache.set("key1", "value1")
        assert memory_cache.get("key1") == "value1"
        
        # 获取不存在的键
        assert memory_cache.get("nonexistent") is None
        assert memory_cache.get("nonexistent", "default") == "default"
    
    def test_set_with_ttl(self, memory_cache):
        """测试带TTL的缓存设置"""
        # 设置短TTL
        memory_cache.set("temp_key", "temp_value", ttl=1)
        assert memory_cache.get("temp_key") == "temp_value"
        
        # 等待过期
        time.sleep(1.1)
        assert memory_cache.get("temp_key") is None
    
    def test_delete(self, memory_cache):
        """测试删除缓存"""
        memory_cache.set("key1", "value1")
        assert memory_cache.exists("key1") is True
        
        memory_cache.delete("key1")
        assert memory_cache.exists("key1") is False
        assert memory_cache.get("key1") is None
    
    def test_clear(self, memory_cache):
        """测试清空缓存"""
        memory_cache.set("key1", "value1")
        memory_cache.set("key2", "value2")
        assert memory_cache.size() == 2
        
        memory_cache.clear()
        assert memory_cache.size() == 0
        assert memory_cache.is_empty() is True
    
    def test_keys_and_values(self, memory_cache):
        """测试获取键和值"""
        memory_cache.set("key1", "value1")
        memory_cache.set("key2", "value2")
        
        keys = memory_cache.keys()
        assert "key1" in keys
        assert "key2" in keys
        
        values = memory_cache.values()
        assert "value1" in values
        assert "value2" in values
        
        items = memory_cache.items()
        assert ("key1", "value1") in items
        assert ("key2", "value2") in items
    
    def test_max_size_limit(self):
        """测试最大大小限制"""
        small_cache = MemoryCache(max_size=2)
        
        small_cache.set("key1", "value1")
        small_cache.set("key2", "value2")
        assert small_cache.size() == 2
        
        # 添加第三个项目，应该移除最旧的
        small_cache.set("key3", "value3")
        assert small_cache.size() == 2
        assert small_cache.get("key1") is None  # 最旧的被移除
        assert small_cache.get("key2") == "value2"
        assert small_cache.get("key3") == "value3"
    
    def test_update_access_time(self, memory_cache):
        """测试访问时间更新"""
        memory_cache.set("key1", "value1")
        memory_cache.set("key2", "value2")
        
        # 访问key1以更新其访问时间
        memory_cache.get("key1")
        
        # 添加新项目触发LRU淘汰
        cache_with_limit = MemoryCache(max_size=2)
        cache_with_limit.set("key1", "value1")
        cache_with_limit.set("key2", "value2")
        cache_with_limit.get("key1")  # 更新访问时间
        cache_with_limit.set("key3", "value3")
        
        # key2应该被淘汰，因为key1最近被访问
        assert cache_with_limit.get("key1") == "value1"
        assert cache_with_limit.get("key2") is None
        assert cache_with_limit.get("key3") == "value3"
    
    def test_cache_stats(self, memory_cache):
        """测试缓存统计"""
        # 初始统计
        stats = memory_cache.get_stats()
        assert stats.hits == 0
        assert stats.misses == 0
        assert stats.hit_rate == 0.0
        
        # 设置和命中
        memory_cache.set("key1", "value1")
        memory_cache.get("key1")  # 命中
        memory_cache.get("key2")  # 未命中
        
        stats = memory_cache.get_stats()
        assert stats.hits == 1
        assert stats.misses == 1
        assert stats.hit_rate == 0.5
    
    def test_expire_cleanup(self, memory_cache):
        """测试过期清理"""
        # 设置一些过期的项目
        memory_cache.set("key1", "value1", ttl=0.1)
        memory_cache.set("key2", "value2", ttl=10)
        
        time.sleep(0.2)
        
        # 手动清理过期项目
        memory_cache.cleanup_expired()
        
        assert memory_cache.get("key1") is None
        assert memory_cache.get("key2") == "value2"
        assert memory_cache.size() == 1


class TestRedisCache:
    """Redis缓存测试类"""
    
    @pytest.fixture
    def mock_redis(self):
        """模拟Redis客户端"""
        mock_redis = Mock()
        mock_redis.get.return_value = None
        mock_redis.set.return_value = True
        mock_redis.delete.return_value = 1
        mock_redis.exists.return_value = False
        mock_redis.flushdb.return_value = True
        mock_redis.keys.return_value = []
        mock_redis.ping.return_value = True
        return mock_redis
    
    @pytest.fixture
    def redis_cache(self, mock_redis):
        """创建Redis缓存实例"""
        with patch('redis.Redis', return_value=mock_redis):
            return RedisCache(host='localhost', port=6379, db=0)
        return None

    def test_redis_initialization(self, redis_cache, mock_redis):
        """测试Redis缓存初始化"""
        assert redis_cache.redis_client == mock_redis
        mock_redis.ping.assert_called_once()
    
    def test_redis_set_and_get(self, redis_cache, mock_redis):
        """测试Redis设置和获取"""
        # 模拟设置成功
        mock_redis.set.return_value = True
        redis_cache.set("key1", "value1")
        mock_redis.set.assert_called_with("key1", '"value1"', ex=None)
        
        # 模拟获取
        mock_redis.get.return_value = '"value1"'
        result = redis_cache.get("key1")
        assert result == "value1"
        mock_redis.get.assert_called_with("key1")
    
    def test_redis_set_with_ttl(self, redis_cache, mock_redis):
        """测试Redis带TTL设置"""
        redis_cache.set("key1", "value1", ttl=300)
        mock_redis.set.assert_called_with("key1", '"value1"', ex=300)
    
    def test_redis_delete(self, redis_cache, mock_redis):
        """测试Redis删除"""
        mock_redis.delete.return_value = 1
        result = redis_cache.delete("key1")
        assert result is True
        mock_redis.delete.assert_called_with("key1")
    
    def test_redis_exists(self, redis_cache, mock_redis):
        """测试Redis存在检查"""
        mock_redis.exists.return_value = 1
        result = redis_cache.exists("key1")
        assert result is True
        mock_redis.exists.assert_called_with("key1")
    
    def test_redis_clear(self, redis_cache, mock_redis):
        """测试Redis清空"""
        redis_cache.clear()
        mock_redis.flushdb.assert_called_once()
    
    def test_redis_connection_error(self, mock_redis):
        """测试Redis连接错误"""
        mock_redis.ping.side_effect = Exception("Connection failed")
        
        with patch('redis.Redis', return_value=mock_redis):
            with pytest.raises(CacheError, match="Redis连接失败"):
                RedisCache(host='localhost', port=6379)
    
    def test_redis_serialization_error(self, redis_cache, mock_redis):
        """测试Redis序列化错误"""
        # 不可序列化的对象
        class UnserializableObject:
            def __reduce__(self):
                raise TypeError("Cannot serialize")
        
        obj = UnserializableObject()
        with pytest.raises(CacheError, match="序列化失败"):
            redis_cache.set("key1", obj)


class TestCacheManager:
    """缓存管理器测试类"""
    
    @pytest.fixture
    def cache_manager(self):
        """创建缓存管理器实例"""
        return CacheManager()
    
    def test_register_cache_backend(self, cache_manager):
        """测试注册缓存后端"""
        memory_cache = MemoryCache()
        cache_manager.register_backend("memory", memory_cache)
        
        assert "memory" in cache_manager.backends
        assert cache_manager.backends["memory"] == memory_cache
    
    def test_set_default_backend(self, cache_manager):
        """测试设置默认后端"""
        memory_cache = MemoryCache()
        cache_manager.register_backend("memory", memory_cache)
        cache_manager.set_default_backend("memory")
        
        assert cache_manager.default_backend == "memory"
    
    def test_cache_operations_with_backend(self, cache_manager):
        """测试指定后端的缓存操作"""
        memory_cache = MemoryCache()
        cache_manager.register_backend("memory", memory_cache)
        
        # 使用指定后端
        cache_manager.set("key1", "value1", backend="memory")
        result = cache_manager.get("key1", backend="memory")
        assert result == "value1"
    
    def test_cache_operations_with_default_backend(self, cache_manager):
        """测试默认后端的缓存操作"""
        memory_cache = MemoryCache()
        cache_manager.register_backend("memory", memory_cache)
        cache_manager.set_default_backend("memory")
        
        # 使用默认后端
        cache_manager.set("key1", "value1")
        result = cache_manager.get("key1")
        assert result == "value1"
    
    def test_multi_backend_operations(self, cache_manager):
        """测试多后端操作"""
        memory_cache1 = MemoryCache()
        memory_cache2 = MemoryCache()
        
        cache_manager.register_backend("cache1", memory_cache1)
        cache_manager.register_backend("cache2", memory_cache2)
        
        # 在不同后端设置不同值
        cache_manager.set("key1", "value1", backend="cache1")
        cache_manager.set("key1", "value2", backend="cache2")
        
        assert cache_manager.get("key1", backend="cache1") == "value1"
        assert cache_manager.get("key1", backend="cache2") == "value2"
    
    def test_cache_invalidation(self, cache_manager):
        """测试缓存失效"""
        memory_cache = MemoryCache()
        cache_manager.register_backend("memory", memory_cache)
        
        cache_manager.set("key1", "value1", backend="memory")
        cache_manager.set("key2", "value2", backend="memory")
        
        # 按模式失效
        cache_manager.invalidate_pattern("key*", backend="memory")
        
        assert cache_manager.get("key1", backend="memory") is None
        assert cache_manager.get("key2", backend="memory") is None
    
    def test_cache_warming(self, cache_manager):
        """测试缓存预热"""
        memory_cache = MemoryCache()
        cache_manager.register_backend("memory", memory_cache)
        
        # 预热数据
        warm_data = {
            "key1": "value1",
            "key2": "value2",
            "key3": "value3"
        }
        
        cache_manager.warm_cache(warm_data, backend="memory")
        
        for key, value in warm_data.items():
            assert cache_manager.get(key, backend="memory") == value
    
    def test_cache_health_check(self, cache_manager):
        """测试缓存健康检查"""
        memory_cache = MemoryCache()
        cache_manager.register_backend("memory", memory_cache)
        
        health_status = cache_manager.health_check()
        
        assert "memory" in health_status
        assert health_status["memory"]["status"] == "healthy"
        assert "response_time" in health_status["memory"]


class TestCacheDecorators:
    """缓存装饰器测试类"""
    
    @pytest.fixture
    def memory_cache(self):
        """创建内存缓存实例"""
        return MemoryCache()
    
    def test_sync_cache_decorator(self, memory_cache):
        """测试同步缓存装饰器"""
        call_count = 0
        
        @cache_decorator(cache=memory_cache, ttl=300)
        def expensive_function(x, y):
            nonlocal call_count
            call_count += 1
            return x + y
        
        # 第一次调用
        result1 = expensive_function(1, 2)
        assert result1 == 3
        assert call_count == 1
        
        # 第二次调用，应该从缓存获取
        result2 = expensive_function(1, 2)
        assert result2 == 3
        assert call_count == 1  # 没有增加
        
        # 不同参数，应该重新计算
        result3 = expensive_function(2, 3)
        assert result3 == 5
        assert call_count == 2
    
    @pytest.mark.asyncio
    async def test_async_cache_decorator(self, memory_cache):
        """测试异步缓存装饰器"""
        call_count = 0
        
        @async_cache_decorator(cache=memory_cache, ttl=300)
        async def async_expensive_function(x, y):
            nonlocal call_count
            call_count += 1
            await asyncio.sleep(0.01)  # 模拟异步操作
            return x * y
        
        # 第一次调用
        result1 = await async_expensive_function(2, 3)
        assert result1 == 6
        assert call_count == 1
        
        # 第二次调用，应该从缓存获取
        result2 = await async_expensive_function(2, 3)
        assert result2 == 6
        assert call_count == 1  # 没有增加
    
    def test_cache_decorator_with_key_generator(self, memory_cache):
        """测试带自定义键生成器的缓存装饰器"""
        def custom_key_generator(func, args, kwargs):
            return f"custom_{func.__name__}_{args[0]}"
        
        @cache_decorator(cache=memory_cache, key_generator=custom_key_generator)
        def test_function(value):
            return value * 2
        
        result = test_function(5)
        assert result == 10
        
        # 检查缓存中是否使用了自定义键
        assert memory_cache.exists("custom_test_function_5")
    
    def test_cache_decorator_with_condition(self, memory_cache):
        """测试带条件的缓存装饰器"""
        @cache_decorator(
            cache=memory_cache,
            condition=lambda result: result > 0  # 只缓存正数结果
        )
        def conditional_function(x):
            return x
        
        # 正数结果应该被缓存
        conditional_function(5)
        assert memory_cache.exists("conditional_function_5")
        
        # 负数结果不应该被缓存
        conditional_function(-5)
        assert not memory_cache.exists("conditional_function_-5")
    
    def test_cache_decorator_error_handling(self, memory_cache):
        """测试缓存装饰器错误处理"""
        @cache_decorator(cache=memory_cache)
        def error_function(should_error):
            if should_error:
                raise ValueError("Test error")
            return "success"
        
        # 正常情况
        result = error_function(False)
        assert result == "success"
        
        # 错误情况，不应该缓存错误
        with pytest.raises(ValueError):
            error_function(True)
        
        # 错误结果不应该被缓存
        assert not memory_cache.exists("error_function_True")


class TestCacheStats:
    """缓存统计测试类"""
    
    def test_cache_stats_initialization(self):
        """测试缓存统计初始化"""
        stats = CacheStats()
        assert stats.hits == 0
        assert stats.misses == 0
        assert stats.sets == 0
        assert stats.deletes == 0
        assert stats.hit_rate == 0.0
        assert stats.miss_rate == 0.0
    
    def test_cache_stats_operations(self):
        """测试缓存统计操作"""
        stats = CacheStats()
        
        # 记录操作
        stats.record_hit()
        stats.record_hit()
        stats.record_miss()
        stats.record_set()
        stats.record_delete()
        
        assert stats.hits == 2
        assert stats.misses == 1
        assert stats.sets == 1
        assert stats.deletes == 1
        assert stats.hit_rate == 2/3  # 2 hits out of 3 total accesses
        assert stats.miss_rate == 1/3  # 1 miss out of 3 total accesses
    
    def test_cache_stats_reset(self):
        """测试缓存统计重置"""
        stats = CacheStats()
        
        stats.record_hit()
        stats.record_miss()
        stats.record_set()
        
        stats.reset()
        
        assert stats.hits == 0
        assert stats.misses == 0
        assert stats.sets == 0
        assert stats.deletes == 0
    
    def test_cache_stats_to_dict(self):
        """测试缓存统计转字典"""
        stats = CacheStats()
        stats.record_hit()
        stats.record_miss()
        
        stats_dict = stats.to_dict()
        
        expected_keys = ['hits', 'misses', 'sets', 'deletes', 'hit_rate', 'miss_rate', 'total_accesses']
        for key in expected_keys:
            assert key in stats_dict
        
        assert stats_dict['hits'] == 1
        assert stats_dict['misses'] == 1
        assert stats_dict['total_accesses'] == 2


class TestCacheIntegration:
    """缓存集成测试类"""
    
    def test_cache_with_database_fallback(self):
        """测试缓存与数据库回退"""
        cache = MemoryCache()
        mock_db = Mock()
        mock_db.get_user.return_value = {"id": 1, "name": "张三"}
        
        def get_user_with_cache(user_id):
            # 先尝试从缓存获取
            cache_key = f"user_{user_id}"
            user = cache.get(cache_key)
            
            if user is None:
                # 缓存未命中，从数据库获取
                user = mock_db.get_user(user_id)
                if user:
                    cache.set(cache_key, user, ttl=300)
            
            return user
        
        # 第一次调用，应该查询数据库
        user1 = get_user_with_cache(1)
        assert user1 == {"id": 1, "name": "张三"}
        mock_db.get_user.assert_called_once_with(1)
        
        # 第二次调用，应该从缓存获取
        mock_db.reset_mock()
        user2 = get_user_with_cache(1)
        assert user2 == {"id": 1, "name": "张三"}
        mock_db.get_user.assert_not_called()
    
    def test_cache_invalidation_on_update(self):
        """测试更新时的缓存失效"""
        cache = MemoryCache()
        mock_db = Mock()
        
        def update_user_with_cache_invalidation(user_id, data):
            # 更新数据库
            mock_db.update_user(user_id, data)
            
            # 失效相关缓存
            cache_key = f"user_{user_id}"
            cache.delete(cache_key)
            
            return True
        
        # 设置初始缓存
        cache.set("user_1", {"id": 1, "name": "张三"})
        assert cache.exists("user_1")
        
        # 更新用户，应该失效缓存
        update_user_with_cache_invalidation(1, {"name": "李四"})
        assert not cache.exists("user_1")
        mock_db.update_user.assert_called_once_with(1, {"name": "李四"})
    
    def test_distributed_cache_consistency(self):
        """测试分布式缓存一致性"""
        # 模拟两个缓存实例
        cache1 = MemoryCache()
        cache2 = MemoryCache()
        
        def sync_caches(key, value, ttl=None):
            """同步多个缓存实例"""
            cache1.set(key, value, ttl)
            cache2.set(key, value, ttl)
        
        def invalidate_caches(key):
            """失效多个缓存实例"""
            cache1.delete(key)
            cache2.delete(key)
        
        # 同步设置
        sync_caches("shared_key", "shared_value")
        assert cache1.get("shared_key") == "shared_value"
        assert cache2.get("shared_key") == "shared_value"
        
        # 同步失效
        invalidate_caches("shared_key")
        assert cache1.get("shared_key") is None
        assert cache2.get("shared_key") is None
    
    @pytest.mark.asyncio
    async def test_concurrent_cache_access(self):
        """测试并发缓存访问"""
        cache = MemoryCache()
        call_count = 0
        
        async def expensive_operation(key):
            nonlocal call_count
            call_count += 1
            await asyncio.sleep(0.1)  # 模拟耗时操作
            return f"result_for_{key}"
        
        async def cached_operation(key):
            # 检查缓存
            result = cache.get(key)
            if result is None:
                result = await expensive_operation(key)
                cache.set(key, result)
            return result
        
        # 并发访问同一个键
        tasks = [cached_operation("test_key") for _ in range(5)]
        results = await asyncio.gather(*tasks)
        
        # 所有结果应该相同
        assert all(result == "result_for_test_key" for result in results)
        
        # 但是昂贵操作可能被调用多次（竞态条件）
        # 在实际应用中，需要使用锁来避免这种情况
        assert call_count >= 1
    
    def test_cache_memory_usage_monitoring(self):
        """测试缓存内存使用监控"""
        cache = MemoryCache(max_size=1000)
        
        # 添加大量数据
        for i in range(100):
            cache.set(f"key_{i}", f"value_{i}" * 100)  # 较大的值
        
        # 检查内存使用情况
        memory_info = cache.get_memory_info()
        
        assert "total_items" in memory_info
        assert "estimated_size_bytes" in memory_info
        assert memory_info["total_items"] <= 100
        assert memory_info["estimated_size_bytes"] > 0
    
    def test_cache_performance_benchmark(self):
        """测试缓存性能基准"""
        cache = MemoryCache()
        
        # 性能测试
        import time
        
        # 写入性能
        start_time = time.time()
        for i in range(1000):
            cache.set(f"key_{i}", f"value_{i}")
        write_time = time.time() - start_time
        
        # 读取性能
        start_time = time.time()
        for i in range(1000):
            cache.get(f"key_{i}")
        read_time = time.time() - start_time
        
        # 性能应该在合理范围内
        assert write_time < 1.0  # 写入1000项应该在1秒内
        assert read_time < 0.5   # 读取1000项应该在0.5秒内
        
        print(f"写入性能: {1000/write_time:.0f} ops/sec")
        print(f"读取性能: {1000/read_time:.0f} ops/sec")