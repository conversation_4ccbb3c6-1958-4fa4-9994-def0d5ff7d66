import os
from unittest.mock import patch

import pytest

from src.utils.config import Config<PERSON>anager, load_config, validate_config


class TestConfigManager:
    """配置管理器测试类"""
    
    @pytest.fixture
    def config_manager(self):
        """创建配置管理器实例"""
        return ConfigManager()
    
    @pytest.fixture
    def sample_config(self):
        """示例配置数据"""
        return {
            "app": {
                "name": "LLM Comparison Tool",
                "version": "1.0.0",
                "debug": False,
                "host": "0.0.0.0",
                "port": 8000
            },
            "database": {
                "url": "sqlite:///./data/database/llm_comparison.db",
                "echo": False,
                "pool_size": 10,
                "max_overflow": 20
            },
            "models": {
                "openai": {
                    "api_key": "sk-test-key",
                    "base_url": "https://api.openai.com/v1",
                    "timeout": 30
                },
                "anthropic": {
                    "api_key": "claude-test-key",
                    "base_url": "https://api.anthropic.com",
                    "timeout": 30
                }
            },
            "logging": {
                "level": "INFO",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "file_enabled": True,
                "console_enabled": True
            }
        }
    
    def test_init_default(self, config_manager):
        """测试默认初始化"""
        assert config_manager.config == {}
        assert config_manager.config_file is None
        assert config_manager.env_prefix == "LLM_"
    
    def test_init_with_file(self, tmp_path, sample_config):
        """测试使用配置文件初始化"""
        import json
        config_file = tmp_path / "config.json"
        with open(config_file, 'w') as f:
            json.dump(sample_config, f)
        
        config_manager = ConfigManager(config_file=str(config_file))
        
        assert config_manager.config == sample_config
        assert config_manager.config_file == str(config_file)
    
    def test_load_from_file_json(self, config_manager, tmp_path, sample_config):
        """测试从JSON文件加载配置"""
        import json
        config_file = tmp_path / "config.json"
        with open(config_file, 'w') as f:
            json.dump(sample_config, f)
        
        config_manager.load_from_file(str(config_file))
        
        assert config_manager.config == sample_config
        assert config_manager.config_file == str(config_file)
    
    def test_load_from_file_yaml(self, config_manager, tmp_path, sample_config):
        """测试从YAML文件加载配置"""
        import yaml
        config_file = tmp_path / "config.yaml"
        with open(config_file, 'w') as f:
            yaml.dump(sample_config, f)
        
        config_manager.load_from_file(str(config_file))
        
        assert config_manager.config == sample_config
    
    def test_load_from_file_not_found(self, config_manager):
        """测试加载不存在的文件"""
        with pytest.raises(FileNotFoundError):
            config_manager.load_from_file("nonexistent.json")
    
    def test_load_from_file_invalid_format(self, config_manager, tmp_path):
        """测试加载无效格式的文件"""
        config_file = tmp_path / "config.txt"
        config_file.write_text("invalid content")
        
        with pytest.raises(ValueError, match="不支持的配置文件格式"):
            config_manager.load_from_file(str(config_file))
    
    def test_load_from_env(self, config_manager):
        """测试从环境变量加载配置"""
        env_vars = {
            "LLM_APP_NAME": "Test App",
            "LLM_APP_DEBUG": "true",
            "LLM_APP_PORT": "9000",
            "LLM_DATABASE_URL": "postgresql://test",
            "OTHER_VAR": "should be ignored"
        }
        
        with patch.dict(os.environ, env_vars):
            config_manager.load_from_env()
        
        assert config_manager.config["app"]["name"] == "Test App"
        assert config_manager.config["app"]["debug"] is True
        assert config_manager.config["app"]["port"] == 9000
        assert config_manager.config["database"]["url"] == "postgresql://test"
        assert "other" not in config_manager.config
    
    def test_get_config_value(self, config_manager, sample_config):
        """测试获取配置值"""
        config_manager.config = sample_config
        
        # 获取嵌套值
        assert config_manager.get("app.name") == "LLM Comparison Tool"
        assert config_manager.get("app.port") == 8000
        assert config_manager.get("database.url") == "sqlite:///./data/database/llm_comparison.db"
        
        # 获取不存在的值
        assert config_manager.get("nonexistent.key") is None
        
        # 获取不存在的值，使用默认值
        assert config_manager.get("nonexistent.key", "default") == "default"
    
    def test_set_config_value(self, config_manager):
        """测试设置配置值"""
        # 设置新值
        config_manager.set("app.name", "New App Name")
        assert config_manager.get("app.name") == "New App Name"
        
        # 设置嵌套值
        config_manager.set("new.nested.value", "test")
        assert config_manager.get("new.nested.value") == "test"
        
        # 覆盖现有值
        config_manager.set("app.name", "Updated Name")
        assert config_manager.get("app.name") == "Updated Name"
    
    def test_update_config(self, config_manager, sample_config):
        """测试更新配置"""
        config_manager.config = sample_config.copy()
        
        updates = {
            "app": {
                "name": "Updated App",
                "new_field": "new value"
            },
            "new_section": {
                "key": "value"
            }
        }
        
        config_manager.update(updates)
        
        assert config_manager.get("app.name") == "Updated App"
        assert config_manager.get("app.new_field") == "new value"
        assert config_manager.get("app.port") == 8000  # 保持原有值
        assert config_manager.get("new_section.key") == "value"
    
    def test_save_to_file(self, config_manager, sample_config, tmp_path):
        """测试保存配置到文件"""
        config_manager.config = sample_config
        
        # 保存为JSON
        json_file = tmp_path / "output.json"
        config_manager.save_to_file(str(json_file))
        
        assert json_file.exists()
        
        # 验证保存的内容
        import json
        with open(json_file, 'r') as f:
            saved_config = json.load(f)
        
        assert saved_config == sample_config
    
    def test_save_to_yaml_file(self, config_manager, sample_config, tmp_path):
        """测试保存配置到YAML文件"""
        config_manager.config = sample_config
        
        yaml_file = tmp_path / "output.yaml"
        config_manager.save_to_file(str(yaml_file))
        
        assert yaml_file.exists()
        
        # 验证保存的内容
        import yaml
        with open(yaml_file, 'r') as f:
            saved_config = yaml.safe_load(f)
        
        assert saved_config == sample_config
    
    def test_validate_config(self, config_manager, sample_config):
        """测试配置验证"""
        config_manager.config = sample_config
        
        # 有效配置
        assert config_manager.validate() is True
        
        # 缺少必需字段
        invalid_config = sample_config.copy()
        del invalid_config["app"]["name"]
        config_manager.config = invalid_config
        
        assert config_manager.validate() is False
    
    def test_get_section(self, config_manager, sample_config):
        """测试获取配置段"""
        config_manager.config = sample_config
        
        app_config = config_manager.get_section("app")
        assert app_config["name"] == "LLM Comparison Tool"
        assert app_config["port"] == 8000
        
        # 获取不存在的段
        nonexistent = config_manager.get_section("nonexistent")
        assert nonexistent == {}
    
    def test_has_key(self, config_manager, sample_config):
        """测试检查键是否存在"""
        config_manager.config = sample_config
        
        assert config_manager.has("app.name") is True
        assert config_manager.has("app.nonexistent") is False
        assert config_manager.has("nonexistent.key") is False
    
    def test_delete_key(self, config_manager, sample_config):
        """测试删除配置键"""
        config_manager.config = sample_config.copy()
        
        # 删除存在的键
        assert config_manager.delete("app.debug") is True
        assert config_manager.has("app.debug") is False
        
        # 删除不存在的键
        assert config_manager.delete("nonexistent.key") is False
    
    def test_reload_config(self, config_manager, tmp_path, sample_config):
        """测试重新加载配置"""
        import json
        config_file = tmp_path / "config.json"
        with open(config_file, 'w') as f:
            json.dump(sample_config, f)
        
        config_manager.load_from_file(str(config_file))
        original_name = config_manager.get("app.name")
        
        # 修改文件内容
        updated_config = sample_config.copy()
        updated_config["app"]["name"] = "Updated App Name"
        with open(config_file, 'w') as f:
            json.dump(updated_config, f)
        
        # 重新加载
        config_manager.reload()
        
        assert config_manager.get("app.name") == "Updated App Name"
        assert config_manager.get("app.name") != original_name
    
    def test_merge_configs(self, config_manager):
        """测试合并配置"""
        base_config = {
            "app": {
                "name": "Base App",
                "port": 8000
            },
            "database": {
                "url": "sqlite:///:memory:"
            }
        }
        
        override_config = {
            "app": {
                "name": "Override App",
                "debug": True
            },
            "logging": {
                "level": "DEBUG"
            }
        }
        
        merged = config_manager.merge_configs(base_config, override_config)
        
        assert merged["app"]["name"] == "Override App"  # 覆盖
        assert merged["app"]["port"] == 8000  # 保持
        assert merged["app"]["debug"] is True  # 新增
        assert merged["database"]["url"] == "sqlite:///:memory:"  # 保持
        assert merged["logging"]["level"] == "DEBUG"  # 新增
    
    def test_export_env_template(self, config_manager, sample_config, tmp_path):
        """测试导出环境变量模板"""
        config_manager.config = sample_config
        
        env_file = tmp_path / ".env.template"
        config_manager.export_env_template(str(env_file))
        
        assert env_file.exists()
        
        content = env_file.read_text()
        assert "LLM_APP_NAME=" in content
        assert "LLM_APP_PORT=" in content
        assert "LLM_DATABASE_URL=" in content


class TestConfigUtilities:
    """配置工具函数测试类"""
    
    def test_load_config_function(self, tmp_path, sample_config):
        """测试load_config函数"""
        import json
        config_file = tmp_path / "config.json"
        with open(config_file, 'w') as f:
            json.dump(sample_config, f)
        
        config = load_config(str(config_file))
        
        assert config == sample_config
    
    def test_load_config_with_env_override(self, tmp_path, sample_config):
        """测试load_config函数与环境变量覆盖"""
        import json
        config_file = tmp_path / "config.json"
        with open(config_file, 'w') as f:
            json.dump(sample_config, f)
        
        env_vars = {
            "LLM_APP_NAME": "Env Override App",
            "LLM_APP_DEBUG": "true"
        }
        
        with patch.dict(os.environ, env_vars):
            config = load_config(str(config_file), env_override=True)
        
        assert config["app"]["name"] == "Env Override App"
        assert config["app"]["debug"] is True
        assert config["app"]["port"] == 8000  # 保持原值
    
    def test_validate_config_function(self, sample_config):
        """测试validate_config函数"""
        # 有效配置
        assert validate_config(sample_config) is True
        
        # 无效配置 - 缺少必需字段
        invalid_config = sample_config.copy()
        del invalid_config["app"]
        
        assert validate_config(invalid_config) is False
    
    def test_validate_config_with_schema(self, sample_config):
        """测试使用模式验证配置"""
        schema = {
            "type": "object",
            "properties": {
                "app": {
                    "type": "object",
                    "properties": {
                        "name": {"type": "string"},
                        "port": {"type": "integer", "minimum": 1, "maximum": 65535}
                    },
                    "required": ["name", "port"]
                }
            },
            "required": ["app"]
        }
        
        # 有效配置
        assert validate_config(sample_config, schema) is True
        
        # 无效配置 - 端口超出范围
        invalid_config = sample_config.copy()
        invalid_config["app"]["port"] = 70000
        
        assert validate_config(invalid_config, schema) is False
    
    @pytest.mark.parametrize("env_value,expected", [
        ("true", True),
        ("True", True),
        ("TRUE", True),
        ("false", False),
        ("False", False),
        ("FALSE", False),
        ("1", True),
        ("0", False),
        ("yes", True),
        ("no", False),
        ("invalid", "invalid"),  # 非布尔值保持原样
    ])
    def test_parse_env_value_boolean(self, env_value, expected):
        """测试环境变量布尔值解析"""
        from src.utils.config import _parse_env_value
        
        result = _parse_env_value(env_value)
        assert result == expected
    
    @pytest.mark.parametrize("env_value,expected", [
        ("123", 123),
        ("0", 0),
        ("-456", -456),
        ("123.45", 123.45),
        ("-67.89", -67.89),
        ("not_a_number", "not_a_number"),  # 非数字保持原样
    ])
    def test_parse_env_value_numeric(self, env_value, expected):
        """测试环境变量数值解析"""
        from src.utils.config import _parse_env_value
        
        result = _parse_env_value(env_value)
        assert result == expected
    
    def test_config_backup_and_restore(self, config_manager, sample_config, tmp_path):
        """测试配置备份和恢复"""
        config_manager.config = sample_config
        
        # 创建备份
        backup_file = tmp_path / "config_backup.json"
        config_manager.create_backup(str(backup_file))
        
        assert backup_file.exists()
        
        # 修改配置
        config_manager.set("app.name", "Modified App")
        assert config_manager.get("app.name") == "Modified App"
        
        # 从备份恢复
        config_manager.restore_from_backup(str(backup_file))
        assert config_manager.get("app.name") == "LLM Comparison Tool"
    
    def test_config_diff(self, config_manager):
        """测试配置差异比较"""
        config1 = {
            "app": {
                "name": "App 1",
                "port": 8000
            },
            "database": {
                "url": "sqlite:///:memory:"
            }
        }
        
        config2 = {
            "app": {
                "name": "App 2",  # 不同
                "port": 8000,  # 相同
                "debug": True  # 新增
            },
            "database": {
                "url": "postgresql://localhost"  # 不同
            },
            "logging": {  # 新增段
                "level": "INFO"
            }
        }
        
        diff = config_manager.compare_configs(config1, config2)
        
        assert "changed" in diff
        assert "added" in diff
        assert "removed" in diff
        
        assert "app.name" in diff["changed"]
        assert "database.url" in diff["changed"]
        assert "app.debug" in diff["added"]
        assert "logging.level" in diff["added"]