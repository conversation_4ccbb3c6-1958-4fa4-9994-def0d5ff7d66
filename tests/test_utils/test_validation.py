from datetime import datetime, timedelta
from unittest.mock import Mock

import pytest

from src.utils.validation import (
    validate_email,
    validate_url,
    validate_api_key,
    validate_model_config,
    validate_conversation_data,
    validate_session_data,
    validate_message_data,
    ValidationError,
    DataValidator
)


class TestValidationFunctions:
    """验证函数测试类"""
    
    @pytest.mark.parametrize("email,expected", [
        ("<EMAIL>", True),
        ("<EMAIL>", True),
        ("<EMAIL>", True),
        ("invalid.email", False),
        ("@example.com", False),
        ("test@", False),
        ("", False),
        (None, False),
        ("test@.com", False),
        ("<EMAIL>", False),
    ])
    def test_validate_email(self, email, expected):
        """测试邮箱验证"""
        result = validate_email(email)
        assert result == expected
    
    @pytest.mark.parametrize("url,expected", [
        ("https://example.com", True),
        ("http://localhost:8000", True),
        ("https://api.openai.com/v1", True),
        ("ftp://files.example.com", True),
        ("invalid-url", False),
        ("http://", False),
        ("", False),
        (None, False),
        ("https://", False),
        ("example.com", False),  # 缺少协议
    ])
    def test_validate_url(self, url, expected):
        """测试URL验证"""
        result = validate_url(url)
        assert result == expected
    
    @pytest.mark.parametrize("api_key,provider,expected", [
        ("sk-1234567890abcdef", "openai", True),
        ("claude-api-key-123", "anthropic", True),
        ("AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI", "google", True),
        ("", "openai", False),
        (None, "openai", False),
        ("short", "openai", False),
        ("sk-invalid", "openai", False),
    ])
    def test_validate_api_key(self, api_key, provider, expected):
        """测试API密钥验证"""
        result = validate_api_key(api_key, provider)
        assert result == expected
    
    def test_validate_model_config_valid(self):
        """测试有效模型配置验证"""
        valid_config = {
            "id": "gpt-4",
            "name": "GPT-4",
            "provider": "openai",
            "api_key": "sk-1234567890abcdef",
            "base_url": "https://api.openai.com/v1",
            "max_tokens": 4096,
            "temperature": 0.7,
            "timeout": 30
        }
        
        result = validate_model_config(valid_config)
        assert result is True
    
    def test_validate_model_config_invalid(self):
        """测试无效模型配置验证"""
        # 缺少必需字段
        invalid_config1 = {
            "name": "GPT-4",
            "provider": "openai"
        }
        
        with pytest.raises(ValidationError, match="缺少必需字段"):
            validate_model_config(invalid_config1)
        
        # 无效的温度值
        invalid_config2 = {
            "id": "gpt-4",
            "name": "GPT-4",
            "provider": "openai",
            "api_key": "sk-test",
            "temperature": 2.5  # 超出范围
        }
        
        with pytest.raises(ValidationError, match="temperature必须在0到2之间"):
            validate_model_config(invalid_config2)
    
    def test_validate_conversation_data_valid(self):
        """测试有效对话数据验证"""
        valid_data = {
            "title": "测试对话",
            "session_id": 1,
            "messages": [
                {
                    "role": "user",
                    "content": "你好"
                },
                {
                    "role": "assistant",
                    "content": "你好！我是AI助手",
                    "model_id": "gpt-4"
                }
            ]
        }
        
        result = validate_conversation_data(valid_data)
        assert result is True
    
    def test_validate_conversation_data_invalid(self):
        """测试无效对话数据验证"""
        # 缺少标题
        invalid_data1 = {
            "session_id": 1,
            "messages": []
        }
        
        with pytest.raises(ValidationError, match="标题不能为空"):
            validate_conversation_data(invalid_data1)
        
        # 无效的消息角色
        invalid_data2 = {
            "title": "测试对话",
            "session_id": 1,
            "messages": [
                {
                    "role": "invalid_role",
                    "content": "测试消息"
                }
            ]
        }
        
        with pytest.raises(ValidationError, match="无效的消息角色"):
            validate_conversation_data(invalid_data2)
    
    def test_validate_session_data_valid(self):
        """测试有效会话数据验证"""
        valid_data = {
            "name": "测试会话",
            "models": ["gpt-4", "claude-3"],
            "description": "这是一个测试会话"
        }
        
        result = validate_session_data(valid_data)
        assert result is True
    
    def test_validate_session_data_invalid(self):
        """测试无效会话数据验证"""
        # 缺少名称
        invalid_data1 = {
            "models": ["gpt-4"]
        }
        
        with pytest.raises(ValidationError, match="会话名称不能为空"):
            validate_session_data(invalid_data1)
        
        # 模型列表为空
        invalid_data2 = {
            "name": "测试会话",
            "models": []
        }
        
        with pytest.raises(ValidationError, match="至少需要选择一个模型"):
            validate_session_data(invalid_data2)
    
    def test_validate_message_data_valid(self):
        """测试有效消息数据验证"""
        valid_data = {
            "role": "user",
            "content": "这是一条测试消息",
            "conversation_id": 1
        }
        
        result = validate_message_data(valid_data)
        assert result is True
    
    def test_validate_message_data_invalid(self):
        """测试无效消息数据验证"""
        # 内容为空
        invalid_data1 = {
            "role": "user",
            "content": "",
            "conversation_id": 1
        }
        
        with pytest.raises(ValidationError, match="消息内容不能为空"):
            validate_message_data(invalid_data1)
        
        # 无效角色
        invalid_data2 = {
            "role": "invalid",
            "content": "测试消息",
            "conversation_id": 1
        }
        
        with pytest.raises(ValidationError, match="无效的消息角色"):
            validate_message_data(invalid_data2)


class TestDataValidator:
    """数据验证器测试类"""
    
    @pytest.fixture
    def validator(self):
        """创建数据验证器实例"""
        return DataValidator()
    
    def test_validate_string_field(self, validator):
        """测试字符串字段验证"""
        # 有效字符串
        assert validator.validate_string("test", min_length=1, max_length=10) is True
        
        # 字符串太短
        with pytest.raises(ValidationError, match="长度不能少于"):
            validator.validate_string("a", min_length=5)
        
        # 字符串太长
        with pytest.raises(ValidationError, match="长度不能超过"):
            validator.validate_string("a" * 20, max_length=10)
        
        # 空字符串
        with pytest.raises(ValidationError, match="不能为空"):
            validator.validate_string("", required=True)
    
    def test_validate_integer_field(self, validator):
        """测试整数字段验证"""
        # 有效整数
        assert validator.validate_integer(5, min_value=1, max_value=10) is True
        
        # 整数太小
        with pytest.raises(ValidationError, match="不能小于"):
            validator.validate_integer(0, min_value=1)
        
        # 整数太大
        with pytest.raises(ValidationError, match="不能大于"):
            validator.validate_integer(15, max_value=10)
        
        # 非整数
        with pytest.raises(ValidationError, match="必须是整数"):
            validator.validate_integer("not_int")
    
    def test_validate_float_field(self, validator):
        """测试浮点数字段验证"""
        # 有效浮点数
        assert validator.validate_float(3.14, min_value=0.0, max_value=10.0) is True
        
        # 浮点数太小
        with pytest.raises(ValidationError, match="不能小于"):
            validator.validate_float(-1.0, min_value=0.0)
        
        # 浮点数太大
        with pytest.raises(ValidationError, match="不能大于"):
            validator.validate_float(15.5, max_value=10.0)
        
        # 非浮点数
        with pytest.raises(ValidationError, match="必须是数字"):
            validator.validate_float("not_float")
    
    def test_validate_list_field(self, validator):
        """测试列表字段验证"""
        # 有效列表
        assert validator.validate_list([1, 2, 3], min_length=1, max_length=5) is True
        
        # 列表太短
        with pytest.raises(ValidationError, match="长度不能少于"):
            validator.validate_list([], min_length=1)
        
        # 列表太长
        with pytest.raises(ValidationError, match="长度不能超过"):
            validator.validate_list([1, 2, 3, 4, 5, 6], max_length=5)
        
        # 非列表
        with pytest.raises(ValidationError, match="必须是列表"):
            validator.validate_list("not_list")
    
    def test_validate_dict_field(self, validator):
        """测试字典字段验证"""
        # 有效字典
        test_dict = {"key1": "value1", "key2": "value2"}
        assert validator.validate_dict(test_dict, required_keys=["key1"]) is True
        
        # 缺少必需键
        with pytest.raises(ValidationError, match="缺少必需键"):
            validator.validate_dict({"key2": "value2"}, required_keys=["key1"])
        
        # 非字典
        with pytest.raises(ValidationError, match="必须是字典"):
            validator.validate_dict("not_dict")
    
    def test_validate_datetime_field(self, validator):
        """测试日期时间字段验证"""
        now = datetime.now()
        past = now - timedelta(days=1)
        future = now + timedelta(days=1)
        
        # 有效日期时间
        assert validator.validate_datetime(now) is True
        
        # 日期时间太早
        with pytest.raises(ValidationError, match="不能早于"):
            validator.validate_datetime(past, min_datetime=now)
        
        # 日期时间太晚
        with pytest.raises(ValidationError, match="不能晚于"):
            validator.validate_datetime(future, max_datetime=now)
        
        # 非日期时间
        with pytest.raises(ValidationError, match="必须是有效的日期时间"):
            validator.validate_datetime("not_datetime")
    
    def test_validate_choice_field(self, validator):
        """测试选择字段验证"""
        choices = ["option1", "option2", "option3"]
        
        # 有效选择
        assert validator.validate_choice("option1", choices) is True
        
        # 无效选择
        with pytest.raises(ValidationError, match="必须是以下选项之一"):
            validator.validate_choice("invalid_option", choices)
    
    def test_validate_regex_field(self, validator):
        """测试正则表达式字段验证"""
        pattern = r"^[a-zA-Z0-9]+$"  # 只允许字母和数字
        
        # 匹配模式
        assert validator.validate_regex("abc123", pattern) is True
        
        # 不匹配模式
        with pytest.raises(ValidationError, match="格式不正确"):
            validator.validate_regex("abc-123", pattern)
    
    def test_validate_custom_field(self, validator):
        """测试自定义验证"""
        def custom_validator(value):
            if value % 2 != 0:
                raise ValidationError("值必须是偶数")
            return True
        
        # 通过自定义验证
        assert validator.validate_custom(4, custom_validator) is True
        
        # 未通过自定义验证
        with pytest.raises(ValidationError, match="值必须是偶数"):
            validator.validate_custom(3, custom_validator)
    
    def test_validate_nested_data(self, validator):
        """测试嵌套数据验证"""
        schema = {
            "name": {"type": "string", "required": True, "min_length": 1},
            "age": {"type": "integer", "min_value": 0, "max_value": 150},
            "email": {"type": "email"},
            "tags": {"type": "list", "max_length": 5}
        }
        
        # 有效数据
        valid_data = {
            "name": "张三",
            "age": 25,
            "email": "<EMAIL>",
            "tags": ["developer", "python"]
        }
        
        assert validator.validate_schema(valid_data, schema) is True
        
        # 无效数据
        invalid_data = {
            "name": "",  # 空名称
            "age": -5,  # 负年龄
            "email": "invalid-email",  # 无效邮箱
            "tags": ["tag1", "tag2", "tag3", "tag4", "tag5", "tag6"]  # 标签太多
        }
        
        with pytest.raises(ValidationError):
            validator.validate_schema(invalid_data, schema)
    
    def test_validate_file_upload(self, validator):
        """测试文件上传验证"""
        # 模拟文件对象
        mock_file = Mock()
        mock_file.filename = "test.json"
        mock_file.content_type = "application/json"
        mock_file.size = 1024  # 1KB
        
        allowed_types = ["application/json", "text/plain"]
        max_size = 2048  # 2KB
        
        # 有效文件
        assert validator.validate_file(
            mock_file, 
            allowed_types=allowed_types, 
            max_size=max_size
        ) is True
        
        # 文件类型不允许
        mock_file.content_type = "image/jpeg"
        with pytest.raises(ValidationError, match="文件类型不被允许"):
            validator.validate_file(mock_file, allowed_types=allowed_types)
        
        # 文件太大
        mock_file.content_type = "application/json"
        mock_file.size = 3072  # 3KB
        with pytest.raises(ValidationError, match="文件大小超过限制"):
            validator.validate_file(mock_file, max_size=max_size)
    
    def test_sanitize_input(self, validator):
        """测试输入清理"""
        # HTML标签清理
        dirty_html = "<script>alert('xss')</script>Hello <b>World</b>"
        clean_html = validator.sanitize_html(dirty_html)
        assert "<script>" not in clean_html
        assert "Hello" in clean_html
        
        # SQL注入清理
        dirty_sql = "'; DROP TABLE users; --"
        clean_sql = validator.sanitize_sql(dirty_sql)
        assert "DROP TABLE" not in clean_sql
        
        # 路径遍历清理
        dirty_path = "../../../etc/passwd"
        clean_path = validator.sanitize_path(dirty_path)
        assert "../" not in clean_path
    
    def test_validate_batch_data(self, validator):
        """测试批量数据验证"""
        schema = {
            "name": {"type": "string", "required": True},
            "value": {"type": "integer", "min_value": 0}
        }
        
        batch_data = [
            {"name": "item1", "value": 10},
            {"name": "item2", "value": 20},
            {"name": "", "value": -5},  # 无效项
            {"name": "item4", "value": 40}
        ]
        
        results = validator.validate_batch(batch_data, schema)
        
        assert len(results["valid"]) == 3
        assert len(results["invalid"]) == 1
        assert results["invalid"][0]["index"] == 2
    
    def test_performance_validation(self, validator):
        """测试验证性能"""
        import time
        
        # 大量数据验证性能测试
        large_data = [{"name": f"item{i}", "value": i} for i in range(1000)]
        schema = {
            "name": {"type": "string", "required": True},
            "value": {"type": "integer", "min_value": 0}
        }
        
        start_time = time.time()
        results = validator.validate_batch(large_data, schema)
        end_time = time.time()
        
        # 验证应该在合理时间内完成（比如1秒）
        assert end_time - start_time < 1.0
        assert len(results["valid"]) == 1000
    
    def test_validation_error_details(self, validator):
        """测试验证错误详情"""
        try:
            validator.validate_string("", required=True, field_name="用户名")
        except ValidationError as e:
            assert "用户名" in str(e)
            assert "不能为空" in str(e)
            assert e.field_name == "用户名"
            assert e.error_code == "REQUIRED_FIELD_EMPTY"
    
    def test_conditional_validation(self, validator):
        """测试条件验证"""
        def conditional_validator(data):
            # 如果类型是'premium'，则价格必须大于100
            if data.get("type") == "premium" and data.get("price", 0) <= 100:
                raise ValidationError("高级产品价格必须大于100")
            return True
        
        # 普通产品，价格可以低
        normal_product = {"type": "normal", "price": 50}
        assert validator.validate_custom(normal_product, conditional_validator) is True
        
        # 高级产品，价格太低
        premium_product = {"type": "premium", "price": 80}
        with pytest.raises(ValidationError, match="高级产品价格必须大于100"):
            validator.validate_custom(premium_product, conditional_validator)
        
        # 高级产品，价格合适
        valid_premium = {"type": "premium", "price": 150}
        assert validator.validate_custom(valid_premium, conditional_validator) is True