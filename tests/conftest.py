"""pytest配置文件"""

import asyncio
import os
import shutil
import sys
import tempfile
from pathlib import Path
from typing import Generator, Dict, Any
from unittest.mock import Mock, AsyncMock, patch

import pytest

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool

try:
    from database.models import Base
    from database.connection import get_db
    from api.main import create_app
    from api.config.unified_config import UnifiedConfig
    from api.dependencies.container import DependencyContainer
except ImportError:
    # 如果模块不存在，创建模拟对象
    Base = Mock()
    get_db = Mock()
    create_app = Mock()
    UnifiedConfig = Mock()
    DependencyContainer = Mock()

# 导入测试工具
from tests.utils.test_helpers import (
    AsyncTestCase, DatabaseTestCase, APITestCase, MockTestCase,
    cleanup_test_data, PerformanceTimer, MemoryMonitor
)
from tests.utils.test_fixtures import (
    TestDataBuilder, TestScenarioManager, TestEnvironmentManager,
    PerformanceTestHelper, SecurityTestHelper, IntegrationTestHelper
)
from tests.utils.mock_managers import (
    MockModelManager,
    MockComparisonEngine,
    MockConversationManager,
    MockPromptManager,
    MockCacheManager,
    MockSecurityManager,
    MockNotificationManager,
    MockStorageManager,
    MockMonitoringManager
)
from tests.fixtures.sample_data import (
    SAMPLE_USER, SAMPLE_MODEL, SAMPLE_API_KEY
)
from tests.fixtures.mock_responses import (
    MOCK_OPENAI_RESPONSE, MOCK_ANTHROPIC_RESPONSE, MOCK_COMPARISON_RESULT
)


# 测试数据库配置
TEST_DATABASE_URL = "sqlite:///:memory:"


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
def test_engine():
    """创建测试数据库引擎"""
    engine = create_engine(
        TEST_DATABASE_URL,
        connect_args={
            "check_same_thread": False,
        },
        poolclass=StaticPool,
    )
    Base.metadata.create_all(bind=engine)
    yield engine
    Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def test_db_session(test_engine) -> Generator[Session, None, None]:
    """创建测试数据库会话"""
    TestingSessionLocal = sessionmaker(
        autocommit=False, autoflush=False, bind=test_engine
    )
    
    session = TestingSessionLocal()
    try:
        yield session
    finally:
        session.rollback()
        session.close()


@pytest.fixture(scope="function")
def test_config() -> UnifiedConfig:
    """创建测试配置"""
    return UnifiedConfig(
        # 数据库配置
        database_url=TEST_DATABASE_URL,
        database_echo=False,
        
        # 服务器配置
        host="127.0.0.1",
        port=8000,
        debug=True,
        
        # 安全配置
        secret_key="test-secret-key",
        cors_origins=["http://localhost:3000"],
        trusted_hosts=["localhost", "127.0.0.1"],
        
        # 日志配置
        log_level="DEBUG",
        log_dir="test_logs",
        console_log_enabled=False,
        
        # 缓存配置
        cache_enabled=False,
        
        # 存储配置
        storage_dir="test_storage",
        
        # 模型配置
        openai_api_key="test-openai-key",
        anthropic_api_key="test-anthropic-key",
        
        # 监控配置
        monitoring_enabled=False
    )


@pytest.fixture(scope="function")
def test_container(test_config: UnifiedConfig) -> DependencyContainer:
    """创建测试依赖容器"""
    container = DependencyContainer()
    # 使用测试配置覆盖默认配置
    container._singletons['unified_config'] = test_config
    container._singletons['api_settings'] = test_config
    container._singletons['config_manager'] = test_config
    return container


@pytest.fixture(scope="function")
def test_app(test_db_session: Session, test_container: DependencyContainer):
    """创建测试应用"""
    app = create_app()
    
    # 覆盖数据库依赖
    def override_get_db():
        try:
            yield test_db_session
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    
    # 覆盖依赖容器
    from api.dependencies.container import get_container
    app.dependency_overrides[get_container] = lambda: test_container
    
    return app


@pytest.fixture(scope="function")
def test_client(test_app) -> TestClient:
    """创建测试客户端"""
    return TestClient(test_app)


@pytest.fixture(scope="function")
def auth_headers() -> dict:
    """创建认证头"""
    return {
        "Authorization": "Bearer test-api-key",
        "Content-Type": "application/json"
    }


@pytest.fixture(scope="function")
def sample_session_data() -> dict:
    """示例会话数据"""
    return {
        "name": "测试会话",
        "description": "这是一个测试会话",
        "models": ["gpt-3.5-turbo", "claude-3-sonnet"]
    }


@pytest.fixture(scope="function")
def sample_conversation_data() -> dict:
    """示例对话数据"""
    return {
        "message": "你好，请介绍一下人工智能",
        "models": ["gpt-3.5-turbo", "claude-3-sonnet"],
        "parameters": {
            "temperature": 0.7,
            "max_tokens": 1000
        }
    }


@pytest.fixture(scope="function")
def mock_model_responses() -> dict:
    """模拟模型响应"""
    return {
        "gpt-3.5-turbo": {
            "content": "人工智能（AI）是计算机科学的一个分支...",
            "usage": {
                "prompt_tokens": 20,
                "completion_tokens": 100,
                "total_tokens": 120
            },
            "model": "gpt-3.5-turbo",
            "finish_reason": "stop"
        },
        "claude-3-sonnet": {
            "content": "人工智能是一门致力于创建智能机器的科学...",
            "usage": {
                "input_tokens": 20,
                "output_tokens": 95,
                "total_tokens": 115
            },
            "model": "claude-3-sonnet",
            "finish_reason": "end_turn"
        }
    }


# 测试标记
pytest_plugins = []


def pytest_configure(config):
    """pytest配置"""
    config.addinivalue_line(
        "markers", "unit: 标记单元测试"
    )
    config.addinivalue_line(
        "markers", "integration: 标记集成测试"
    )
    config.addinivalue_line(
        "markers", "e2e: 标记端到端测试"
    )
    config.addinivalue_line(
        "markers", "slow: 标记慢速测试"
    )
    config.addinivalue_line(
        "markers", "api: 标记API测试"
    )
    config.addinivalue_line(
        "markers", "database: 标记数据库测试"
    )
    config.addinivalue_line(
        "markers", "model: 标记模型测试"
    )
    config.addinivalue_line(
        "markers", "performance: 标记性能测试"
    )
    config.addinivalue_line(
        "markers", "security: 标记安全测试"
    )
    config.addinivalue_line(
        "markers", "smoke: 标记冒烟测试"
    )
    config.addinivalue_line(
        "markers", "regression: 标记回归测试"
    )


def pytest_collection_modifyitems(config, items):
    """修改测试收集项"""
    # 为慢速测试添加标记
    for item in items:
        if "slow" in item.keywords:
            item.add_marker(pytest.mark.slow)
        
        # 根据文件路径自动添加标记
        if "unit" in str(item.fspath):
            item.add_marker(pytest.mark.unit)
        elif "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        elif "e2e" in str(item.fspath):
            item.add_marker(pytest.mark.e2e)
        elif "performance" in str(item.fspath):
            item.add_marker(pytest.mark.performance)
        elif "security" in str(item.fspath):
            item.add_marker(pytest.mark.security)


@pytest.fixture(scope="session", autouse=True)
def setup_test_environment():
    """设置测试环境"""
    # 设置测试环境变量
    os.environ["TESTING"] = "true"
    os.environ["LOG_LEVEL"] = "DEBUG"
    
    # 禁用外部服务调用
    os.environ["DISABLE_EXTERNAL_APIS"] = "true"
    
    yield
    
    # 清理环境变量
    os.environ.pop("TESTING", None)
    os.environ.pop("DISABLE_EXTERNAL_APIS", None)


@pytest.fixture(autouse=True)
def isolate_tests(test_db_session):
    """隔离测试"""
    # 在每个测试前重置全局状态
    yield
    # 在每个测试后清理全局状态和测试数据
    try:
        cleanup_test_data(test_db_session)
    except Exception:
        pass  # 忽略清理错误


@pytest.fixture(autouse=True)
def cleanup_test_files():
    """自动清理测试文件"""
    yield
    
    # 清理测试日志目录
    import shutil
    test_log_dir = Path("test_logs")
    if test_log_dir.exists():
        shutil.rmtree(test_log_dir)
    
    # 清理测试存储目录
    test_storage_dir = Path("test_storage")
    if test_storage_dir.exists():
        shutil.rmtree(test_storage_dir)


class MockModelManager:
    """模拟模型管理器"""
    
    def __init__(self, responses: dict):
        self.responses = responses
    
    async def generate_response(self, model: str, messages: list, **kwargs):
        """模拟生成响应"""
        if model in self.responses:
            return self.responses[model]
        else:
            raise ValueError(f"未知模型: {model}")
    
    async def validate_api_key(self, provider: str, api_key: str) -> bool:
        """验证API密钥"""
        # 模拟API密钥验证逻辑
        return api_key.startswith(f"{provider}_") and len(api_key) > 10
    
    def get_available_models(self) -> list:
        """获取可用模型列表"""
        return list(self.responses.keys())


class MockComparisonEngine:
    """模拟比较引擎"""
    
    def __init__(self, model_manager: MockModelManager):
        self.model_manager = model_manager
    
    async def compare_models(self, message: str, models: list, **kwargs):
        """模拟模型比较"""
        results = {}
        for model in models:
            try:
                response = await self.model_manager.generate_response(
                    model, [{"role": "user", "content": message}], **kwargs
                )
                results[model] = response
            except Exception as e:
                results[model] = {"error": str(e)}
        
        return {
            "results": results,
            "comparison": {
                "best_model": models[0] if models else None,
                "metrics": {
                    "response_time": 1.5,
                    "token_efficiency": 0.85
                }
            }
        }


@pytest.fixture(scope="function")
def mock_model_manager(mock_model_responses) -> MockModelManager:
    """创建模拟模型管理器"""
    return MockModelManager(mock_model_responses)


@pytest.fixture(scope="function")
def mock_conv_manager() -> MockConversationManager:
    """创建模拟对话管理器"""
    return MockConversationManager()


@pytest.fixture(scope="function")
def mock_prompt_manager() -> MockPromptManager:
    """创建模拟提示词管理器"""
    return MockPromptManager()


@pytest.fixture(scope="function")
def mock_comparison_engine(mock_model_manager, mock_conv_manager, mock_prompt_manager) -> MockComparisonEngine:
    """创建模拟比较引擎"""
    return MockComparisonEngine(mock_model_manager, mock_conv_manager, mock_prompt_manager)


# 新增的测试工具fixtures
@pytest.fixture
def test_data_builder(test_db_session) -> TestDataBuilder:
    """创建测试数据构建器"""
    return TestDataBuilder(test_db_session)


@pytest.fixture
def test_scenario_manager() -> TestScenarioManager:
    """创建测试场景管理器"""
    return TestScenarioManager()


@pytest.fixture
def test_env_manager() -> Generator[TestEnvironmentManager, None, None]:
    """创建测试环境管理器"""
    manager = TestEnvironmentManager()
    yield manager
    manager.cleanup()


@pytest.fixture
def performance_helper() -> PerformanceTestHelper:
    """创建性能测试助手"""
    return PerformanceTestHelper()


@pytest.fixture
def security_helper(test_client) -> SecurityTestHelper:
    """创建安全测试助手"""
    return SecurityTestHelper(test_client)


@pytest.fixture
def integration_helper(test_client) -> IntegrationTestHelper:
    """创建集成测试助手"""
    return IntegrationTestHelper(test_client)


@pytest.fixture
def performance_timer() -> PerformanceTimer:
    """创建性能计时器"""
    return PerformanceTimer()


@pytest.fixture
def memory_monitor() -> MemoryMonitor:
    """创建内存监控器"""
    return MemoryMonitor()


# Mock管理器fixtures
@pytest.fixture
def mock_cache_manager() -> MockCacheManager:
    """创建模拟缓存管理器"""
    return MockCacheManager()


@pytest.fixture
def mock_security_manager() -> MockSecurityManager:
    """创建模拟安全管理器"""
    return MockSecurityManager()


@pytest.fixture
def mock_notification_manager() -> MockNotificationManager:
    """创建模拟通知管理器"""
    return MockNotificationManager()


@pytest.fixture
def mock_storage_manager() -> MockStorageManager:
    """创建模拟存储管理器"""
    return MockStorageManager()


@pytest.fixture
def mock_monitoring_manager() -> MockMonitoringManager:
    """创建模拟监控管理器"""
    return MockMonitoringManager()


# 测试基类fixtures
@pytest.fixture
def async_test_case() -> AsyncTestCase:
    """创建异步测试基类"""
    return AsyncTestCase()


@pytest.fixture
def database_test_case(test_db_session) -> DatabaseTestCase:
    """创建数据库测试基类"""
    return DatabaseTestCase(test_db_session)


@pytest.fixture
def api_test_case(test_client) -> APITestCase:
    """创建API测试基类"""
    return APITestCase(test_client)


@pytest.fixture
def mock_test_case() -> MockTestCase:
    """创建模拟测试基类"""
    return MockTestCase()


# 扩展的示例数据fixtures
# 使用工厂方法替代重复的fixture
from tests.utils.base_test import TestDataFactory

@pytest.fixture
def sample_user_data() -> Dict[str, Any]:
    """示例用户数据"""
    return SAMPLE_USER.copy()

@pytest.fixture
def sample_message_data() -> Dict[str, Any]:
    """示例消息数据"""
    return TestDataFactory.get_sample_message_data()

@pytest.fixture
def sample_model_data() -> Dict[str, Any]:
    """示例模型数据"""
    return SAMPLE_MODEL.copy()

@pytest.fixture
def sample_api_key_data() -> Dict[str, Any]:
    """示例API密钥数据"""
    return SAMPLE_API_KEY.copy()


# 模拟响应fixtures
# 使用工厂方法替代重复的mock fixture
from tests.utils.base_test import MockFactory

@pytest.fixture
def mock_openai_response() -> Dict[str, Any]:
    """模拟OpenAI响应"""
    return MOCK_OPENAI_RESPONSE.copy()

@pytest.fixture
def mock_anthropic_response() -> Dict[str, Any]:
    """模拟Anthropic响应"""
    return MOCK_ANTHROPIC_RESPONSE.copy()

@pytest.fixture
def mock_model_manager():
    """模拟模型管理器"""
    return MockFactory.create_mock_model_manager()

@pytest.fixture
def mock_comparison_engine():
    """模拟比较引擎"""
    return MockFactory.create_mock_comparison_engine()


@pytest.fixture
def mock_comparison_result() -> Dict[str, Any]:
    """模拟比较结果"""
    return MOCK_COMPARISON_RESULT.copy()


# 环境变量fixtures
@pytest.fixture
def test_env_vars() -> Generator[Dict[str, str], None, None]:
    """设置测试环境变量"""
    original_env = os.environ.copy()
    
    test_vars = {
        "ENVIRONMENT": "test",
        "DATABASE_URL": "sqlite:///:memory:",
        "SECRET_KEY": "test-secret-key",
        "OPENAI_API_KEY": "test-openai-key",
        "ANTHROPIC_API_KEY": "test-anthropic-key",
        "REDIS_URL": "redis://localhost:6379/1",
        "LOG_LEVEL": "DEBUG"
    }
    
    os.environ.update(test_vars)
    
    yield test_vars
    
    # 恢复原始环境变量
    os.environ.clear()
    os.environ.update(original_env)


# 临时目录和文件fixtures
@pytest.fixture
def temp_dir() -> Generator[Path, None, None]:
    """创建临时目录"""
    temp_path = Path(tempfile.mkdtemp())
    yield temp_path
    shutil.rmtree(temp_path, ignore_errors=True)


@pytest.fixture
def temp_file() -> Generator[Path, None, None]:
    """创建临时文件"""
    fd, path = tempfile.mkstemp()
    os.close(fd)
    temp_path = Path(path)
    yield temp_path
    temp_path.unlink(missing_ok=True)


# 模拟外部服务fixtures
@pytest.fixture
def mock_openai_api():
    """模拟OpenAI API"""
    with patch('openai.ChatCompletion.acreate') as mock:
        mock.return_value = AsyncMock(return_value=MOCK_OPENAI_RESPONSE)
        yield mock


@pytest.fixture
def mock_anthropic_api():
    """模拟Anthropic API"""
    with patch('anthropic.AsyncAnthropic') as mock:
        mock_client = AsyncMock()
        mock_client.messages.create.return_value = AsyncMock(return_value=MOCK_ANTHROPIC_RESPONSE)
        mock.return_value = mock_client
        yield mock


@pytest.fixture
def mock_redis():
    """模拟Redis"""
    with patch('redis.asyncio.Redis') as mock:
        mock_redis = AsyncMock()
        mock_redis.get.return_value = None
        mock_redis.set.return_value = True
        mock_redis.delete.return_value = 1
        mock_redis.exists.return_value = False
        mock.return_value = mock_redis
        yield mock_redis


@pytest.fixture
def mock_celery():
    """模拟Celery"""
    with patch('celery.Celery') as mock:
        mock_celery = Mock()
        mock_task = Mock()
        mock_task.delay.return_value = Mock(id="test-task-id")
        mock_celery.task.return_value = mock_task
        mock.return_value = mock_celery
        yield mock_celery


# 参数化测试数据fixtures
@pytest.fixture(params=[
    {"provider": "openai", "model": "gpt-3.5-turbo"},
    {"provider": "openai", "model": "gpt-4"},
    {"provider": "anthropic", "model": "claude-3-sonnet"},
    {"provider": "anthropic", "model": "claude-3-haiku"}
])
def model_configs(request):
    """模型配置参数化数据"""
    return request.param


@pytest.fixture(params=[1, 5, 10, 50])
def batch_sizes(request):
    """批量大小参数化数据"""
    return request.param


@pytest.fixture(params=["user", "assistant", "system"])
def message_roles(request):
    """消息角色参数化数据"""
    return request.param


# 自定义断言fixtures
@pytest.fixture
def assert_response_time():
    """响应时间断言"""
    def _assert_response_time(response_time: float, max_time: float = 1.0):
        assert response_time <= max_time, f"响应时间 {response_time}s 超过最大限制 {max_time}s"
    
    return _assert_response_time


@pytest.fixture
def assert_memory_usage():
    """内存使用断言"""
    def _assert_memory_usage(memory_usage: float, max_memory: float = 100.0):
        assert memory_usage <= max_memory, f"内存使用 {memory_usage}MB 超过最大限制 {max_memory}MB"
    
    return _assert_memory_usage


@pytest.fixture
def assert_api_response():
    """API响应断言"""
    def _assert_api_response(response, expected_status: int = 200, expected_keys: list = None):
        assert response.status_code == expected_status
        
        if expected_keys:
            data = response.json()
            for key in expected_keys:
                assert key in data, f"响应中缺少字段: {key}"
    
    return _assert_api_response