"""模型管理器测试"""

from unittest.mock import Mock, patch, AsyncMock

import pytest
from core.model_manager import ModelManager
from core.models.anthropic_model import AnthropicModel
from core.models.openai_model import OpenAIModel

from api.exceptions.handlers import ValidationAPIError


@pytest.mark.core
@pytest.mark.unit
class TestModelManager:
    """模型管理器测试类"""
    
    @pytest.fixture
    def model_manager(self, unified_config):
        """创建模型管理器实例"""
        return ModelManager(unified_config)
    
    @pytest.fixture
    def mock_openai_model(self):
        """模拟OpenAI模型"""
        model = Mock(spec=OpenAIModel)
        model.model_id = "gpt-3.5-turbo"
        model.provider = "openai"
        model.is_available.return_value = True
        model.generate_response = AsyncMock(return_value={
            "content": "这是GPT的回复",
            "usage": {"prompt_tokens": 10, "completion_tokens": 20, "total_tokens": 30},
            "model": "gpt-3.5-turbo",
            "finish_reason": "stop"
        })
        return model
    
    @pytest.fixture
    def mock_anthropic_model(self):
        """模拟Anthropic模型"""
        model = Mock(spec=AnthropicModel)
        model.model_id = "claude-3-sonnet"
        model.provider = "anthropic"
        model.is_available.return_value = True
        model.generate_response = AsyncMock(return_value={
            "content": "这是Claude的回复",
            "usage": {"prompt_tokens": 12, "completion_tokens": 25, "total_tokens": 37},
            "model": "claude-3-sonnet",
            "finish_reason": "stop"
        })
        return model
    
    def test_model_manager_initialization(self, model_manager: ModelManager):
        """测试模型管理器初始化"""
        assert model_manager is not None
        assert hasattr(model_manager, '_models')
        assert hasattr(model_manager, '_config')
        assert isinstance(model_manager._models, dict)
    
    @patch('core.model_manager.OpenAIModel')
    @patch('core.model_manager.AnthropicModel')
    def test_initialize_models(self, mock_anthropic, mock_openai, model_manager: ModelManager):
        """测试模型初始化"""
        # 模拟模型实例
        mock_openai_instance = Mock()
        mock_openai_instance.model_id = "gpt-3.5-turbo"
        mock_openai_instance.is_available.return_value = True
        mock_openai.return_value = mock_openai_instance
        
        mock_anthropic_instance = Mock()
        mock_anthropic_instance.model_id = "claude-3-sonnet"
        mock_anthropic_instance.is_available.return_value = True
        mock_anthropic.return_value = mock_anthropic_instance
        
        # 初始化模型
        model_manager._initialize_models()
        
        # 验证模型被正确初始化
        assert "gpt-3.5-turbo" in model_manager._models
        assert "claude-3-sonnet" in model_manager._models
        assert mock_openai.called
        assert mock_anthropic.called
    
    def test_get_available_models(self, model_manager: ModelManager, mock_openai_model, mock_anthropic_model):
        """测试获取可用模型列表"""
        # 设置模型
        model_manager._models = {
            "gpt-3.5-turbo": mock_openai_model,
            "claude-3-sonnet": mock_anthropic_model
        }
        
        available_models = model_manager.get_available_models()
        
        assert len(available_models) == 2
        assert any(model["id"] == "gpt-3.5-turbo" for model in available_models)
        assert any(model["id"] == "claude-3-sonnet" for model in available_models)
        
        # 验证模型信息结构
        gpt_model = next(m for m in available_models if m["id"] == "gpt-3.5-turbo")
        assert "name" in gpt_model
        assert "provider" in gpt_model
        assert "description" in gpt_model
    
    def test_get_model_info_success(self, model_manager: ModelManager, mock_openai_model):
        """测试成功获取模型信息"""
        # 设置模型
        model_manager._models = {"gpt-3.5-turbo": mock_openai_model}
        
        # 模拟模型信息
        mock_openai_model.get_model_info.return_value = {
            "id": "gpt-3.5-turbo",
            "name": "GPT-3.5 Turbo",
            "provider": "openai",
            "max_tokens": 4096,
            "supports_streaming": True
        }
        
        model_info = model_manager.get_model_info("gpt-3.5-turbo")
        
        assert model_info is not None
        assert model_info["id"] == "gpt-3.5-turbo"
        assert model_info["provider"] == "openai"
        mock_openai_model.get_model_info.assert_called_once()
    
    def test_get_model_info_not_found(self, model_manager: ModelManager):
        """测试获取不存在的模型信息"""
        model_info = model_manager.get_model_info("non-existent-model")
        assert model_info is None
    
    @pytest.mark.asyncio
    async def test_generate_response_single_model(self, model_manager: ModelManager, mock_openai_model):
        """测试单个模型生成响应"""
        # 设置模型
        model_manager._models = {"gpt-3.5-turbo": mock_openai_model}
        
        message = "你好，请介绍一下人工智能"
        models = ["gpt-3.5-turbo"]
        
        responses = await model_manager.generate_response(message, models)
        
        assert "gpt-3.5-turbo" in responses
        assert responses["gpt-3.5-turbo"]["content"] == "这是GPT的回复"
        assert responses["gpt-3.5-turbo"]["model"] == "gpt-3.5-turbo"
        mock_openai_model.generate_response.assert_called_once_with(message, {})
    
    @pytest.mark.asyncio
    async def test_generate_response_multiple_models(self, model_manager: ModelManager, 
                                                   mock_openai_model, mock_anthropic_model):
        """测试多个模型生成响应"""
        # 设置模型
        model_manager._models = {
            "gpt-3.5-turbo": mock_openai_model,
            "claude-3-sonnet": mock_anthropic_model
        }
        
        message = "你好，请介绍一下人工智能"
        models = ["gpt-3.5-turbo", "claude-3-sonnet"]
        
        responses = await model_manager.generate_response(message, models)
        
        assert len(responses) == 2
        assert "gpt-3.5-turbo" in responses
        assert "claude-3-sonnet" in responses
        assert responses["gpt-3.5-turbo"]["content"] == "这是GPT的回复"
        assert responses["claude-3-sonnet"]["content"] == "这是Claude的回复"
        
        mock_openai_model.generate_response.assert_called_once()
        mock_anthropic_model.generate_response.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_generate_response_with_parameters(self, model_manager: ModelManager, mock_openai_model):
        """测试带参数的响应生成"""
        # 设置模型
        model_manager._models = {"gpt-3.5-turbo": mock_openai_model}
        
        message = "你好"
        models = ["gpt-3.5-turbo"]
        parameters = {
            "temperature": 0.8,
            "max_tokens": 1024,
            "top_p": 0.9
        }
        
        responses = await model_manager.generate_response(message, models, parameters)
        
        assert "gpt-3.5-turbo" in responses
        mock_openai_model.generate_response.assert_called_once_with(message, parameters)
    
    @pytest.mark.asyncio
    async def test_generate_response_model_error(self, model_manager: ModelManager, mock_openai_model):
        """测试模型生成响应时出错"""
        # 设置模型
        model_manager._models = {"gpt-3.5-turbo": mock_openai_model}
        
        # 模拟模型抛出异常
        mock_openai_model.generate_response.side_effect = Exception("API错误")
        
        message = "你好"
        models = ["gpt-3.5-turbo"]
        
        responses = await model_manager.generate_response(message, models)
        
        assert "gpt-3.5-turbo" in responses
        assert "error" in responses["gpt-3.5-turbo"]
        assert "API错误" in responses["gpt-3.5-turbo"]["error"]
    
    @pytest.mark.asyncio
    async def test_generate_response_invalid_model(self, model_manager: ModelManager):
        """测试使用无效模型生成响应"""
        message = "你好"
        models = ["invalid-model"]
        
        with pytest.raises(ValidationAPIError) as exc_info:
            await model_manager.generate_response(message, models)
        
        assert "模型" in str(exc_info.value) and "不可用" in str(exc_info.value)
    
    def test_test_model_connection_success(self, model_manager: ModelManager, mock_openai_model):
        """测试模型连接测试成功"""
        # 设置模型
        model_manager._models = {"gpt-3.5-turbo": mock_openai_model}
        
        # 模拟连接测试
        mock_openai_model.test_connection.return_value = {
            "status": "success",
            "response_time_ms": 250,
            "test_response": "连接正常"
        }
        
        result = model_manager.test_model_connection("gpt-3.5-turbo")
        
        assert result["model_id"] == "gpt-3.5-turbo"
        assert result["status"] == "success"
        assert result["response_time_ms"] == 250
        assert "timestamp" in result
        mock_openai_model.test_connection.assert_called_once()
    
    def test_test_model_connection_failure(self, model_manager: ModelManager, mock_openai_model):
        """测试模型连接测试失败"""
        # 设置模型
        model_manager._models = {"gpt-3.5-turbo": mock_openai_model}
        
        # 模拟连接测试失败
        mock_openai_model.test_connection.side_effect = Exception("连接失败")
        
        result = model_manager.test_model_connection("gpt-3.5-turbo")
        
        assert result["model_id"] == "gpt-3.5-turbo"
        assert result["status"] == "error"
        assert "连接失败" in result["error_message"]
        assert "timestamp" in result
    
    def test_test_model_connection_invalid_model(self, model_manager: ModelManager):
        """测试无效模型的连接测试"""
        with pytest.raises(ValidationAPIError) as exc_info:
            model_manager.test_model_connection("invalid-model")
        
        assert "模型" in str(exc_info.value) and "不存在" in str(exc_info.value)
    
    def test_get_usage_stats(self, model_manager: ModelManager, mock_openai_model):
        """测试获取模型使用统计"""
        # 设置模型
        model_manager._models = {"gpt-3.5-turbo": mock_openai_model}
        
        # 模拟使用统计
        mock_openai_model.get_usage_stats.return_value = {
            "total_requests": 1250,
            "total_tokens": 125000,
            "total_cost": 25.50,
            "average_response_time_ms": 850,
            "success_rate": 0.98
        }
        
        stats = model_manager.get_usage_stats("gpt-3.5-turbo")
        
        assert stats["model_id"] == "gpt-3.5-turbo"
        assert stats["total_requests"] == 1250
        assert stats["success_rate"] == 0.98
        mock_openai_model.get_usage_stats.assert_called_once()
    
    def test_get_usage_stats_invalid_model(self, model_manager: ModelManager):
        """测试获取无效模型的使用统计"""
        with pytest.raises(ValidationAPIError) as exc_info:
            model_manager.get_usage_stats("invalid-model")
        
        assert "模型" in str(exc_info.value) and "不存在" in str(exc_info.value)
    
    def test_get_models_by_provider(self, model_manager: ModelManager, mock_openai_model, mock_anthropic_model):
        """测试按提供商获取模型"""
        # 设置模型
        model_manager._models = {
            "gpt-3.5-turbo": mock_openai_model,
            "claude-3-sonnet": mock_anthropic_model
        }
        
        # 获取OpenAI模型
        openai_models = model_manager.get_models_by_provider("openai")
        assert len(openai_models) == 1
        assert openai_models[0]["id"] == "gpt-3.5-turbo"
        
        # 获取Anthropic模型
        anthropic_models = model_manager.get_models_by_provider("anthropic")
        assert len(anthropic_models) == 1
        assert anthropic_models[0]["id"] == "claude-3-sonnet"
    
    def test_get_models_by_capability(self, model_manager: ModelManager, mock_openai_model):
        """测试按能力获取模型"""
        # 设置模型
        model_manager._models = {"gpt-3.5-turbo": mock_openai_model}
        
        # 模拟模型能力
        mock_openai_model.get_capabilities.return_value = ["text_generation", "code_generation"]
        
        models = model_manager.get_models_by_capability("code_generation")
        
        assert len(models) == 1
        assert models[0]["id"] == "gpt-3.5-turbo"
        mock_openai_model.get_capabilities.assert_called_once()
    
    def test_compare_models(self, model_manager: ModelManager, mock_openai_model, mock_anthropic_model):
        """测试模型比较"""
        # 设置模型
        model_manager._models = {
            "gpt-3.5-turbo": mock_openai_model,
            "claude-3-sonnet": mock_anthropic_model
        }
        
        # 模拟性能指标
        mock_openai_model.get_performance_metrics.return_value = {
            "response_time": 850,
            "cost_efficiency": 0.85,
            "quality_score": 0.88
        }
        
        mock_anthropic_model.get_performance_metrics.return_value = {
            "response_time": 920,
            "cost_efficiency": 0.78,
            "quality_score": 0.92
        }
        
        models = ["gpt-3.5-turbo", "claude-3-sonnet"]
        metrics = ["response_time", "cost_efficiency", "quality_score"]
        
        comparison = model_manager.compare_models(models, metrics)
        
        assert "models" in comparison
        assert "comparison" in comparison
        assert "recommendation" in comparison
        assert len(comparison["models"]) == 2
        assert "response_time" in comparison["comparison"]
        assert "cost_efficiency" in comparison["comparison"]
        assert "quality_score" in comparison["comparison"]
    
    def test_is_model_available(self, model_manager: ModelManager, mock_openai_model):
        """测试检查模型可用性"""
        # 设置模型
        model_manager._models = {"gpt-3.5-turbo": mock_openai_model}
        
        # 测试可用模型
        assert model_manager.is_model_available("gpt-3.5-turbo") is True
        
        # 测试不可用模型
        mock_openai_model.is_available.return_value = False
        assert model_manager.is_model_available("gpt-3.5-turbo") is False
        
        # 测试不存在的模型
        assert model_manager.is_model_available("non-existent-model") is False
    
    def test_validate_models_list(self, model_manager: ModelManager, mock_openai_model, mock_anthropic_model):
        """测试验证模型列表"""
        # 设置模型
        model_manager._models = {
            "gpt-3.5-turbo": mock_openai_model,
            "claude-3-sonnet": mock_anthropic_model
        }
        
        # 测试有效模型列表
        valid_models = ["gpt-3.5-turbo", "claude-3-sonnet"]
        assert model_manager.validate_models(valid_models) is True
        
        # 测试包含无效模型的列表
        invalid_models = ["gpt-3.5-turbo", "invalid-model"]
        assert model_manager.validate_models(invalid_models) is False
        
        # 测试空列表
        assert model_manager.validate_models([]) is False
    
    def test_model_health_check(self, model_manager: ModelManager, mock_openai_model, mock_anthropic_model):
        """测试模型健康检查"""
        # 设置模型
        model_manager._models = {
            "gpt-3.5-turbo": mock_openai_model,
            "claude-3-sonnet": mock_anthropic_model
        }
        
        # 模拟健康检查结果
        mock_openai_model.health_check.return_value = {"status": "healthy", "latency_ms": 200}
        mock_anthropic_model.health_check.return_value = {"status": "healthy", "latency_ms": 250}
        
        health_status = model_manager.health_check()
        
        assert "overall_status" in health_status
        assert "models" in health_status
        assert len(health_status["models"]) == 2
        assert "gpt-3.5-turbo" in health_status["models"]
        assert "claude-3-sonnet" in health_status["models"]
        
        mock_openai_model.health_check.assert_called_once()
        mock_anthropic_model.health_check.assert_called_once()