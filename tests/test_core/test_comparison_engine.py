from datetime import datetime
from unittest.mock import Mock, AsyncMock

import pytest

from src.core.comparison_engine import ComparisonEngine


class TestComparisonEngine:
    """比较引擎测试类"""
    
    @pytest.fixture
    def comparison_engine(self, mock_model_manager, mock_conv_manager, mock_prompt_manager):
        """创建比较引擎实例"""
        return ComparisonEngine(mock_model_manager, mock_conv_manager, mock_prompt_manager)
    
    @pytest.fixture
    def sample_models(self):
        """示例模型列表"""
        return [
            {"id": "gpt-4", "name": "GPT-4", "provider": "openai"},
            {"id": "claude-3", "name": "Claude-3", "provider": "anthropic"},
            {"id": "gemini-pro", "name": "Gemini Pro", "provider": "google"}
        ]
    
    @pytest.fixture
    def sample_prompt(self):
        """示例提示词"""
        return "请解释什么是人工智能？"
    
    @pytest.fixture
    def sample_responses(self):
        """示例模型响应"""
        return {
            "gpt-4": {
                "content": "人工智能是计算机科学的一个分支...",
                "tokens": 150,
                "cost": 0.003,
                "response_time": 2.5
            },
            "claude-3": {
                "content": "人工智能（AI）是指机器模拟人类智能...",
                "tokens": 140,
                "cost": 0.0025,
                "response_time": 2.1
            },
            "gemini-pro": {
                "content": "人工智能是一种技术，使计算机能够...",
                "tokens": 130,
                "cost": 0.002,
                "response_time": 1.8
            }
        }
    
    @pytest.mark.asyncio
    async def test_compare_models_success(self, comparison_engine, sample_models, 
                                        sample_prompt, sample_responses):
        """测试模型比较成功"""
        # 模拟模型管理器响应
        comparison_engine.model_manager.generate_response = AsyncMock(
            side_effect=lambda model_id, prompt, **kwargs: sample_responses[model_id]
        )
        
        result = await comparison_engine.compare_models(
            models=sample_models,
            prompt=sample_prompt
        )
        
        assert "results" in result
        assert "summary" in result
        assert "metadata" in result
        assert len(result["results"]) == 3
        
        # 验证每个结果包含必要字段
        for model_result in result["results"]:
            assert "model_id" in model_result
            assert "response" in model_result
            assert "metrics" in model_result
            assert "timestamp" in model_result
    
    @pytest.mark.asyncio
    async def test_compare_models_with_parameters(self, comparison_engine, 
                                                sample_models, sample_prompt):
        """测试带参数的模型比较"""
        parameters = {
            "temperature": 0.7,
            "max_tokens": 200,
            "top_p": 0.9
        }
        
        comparison_engine.model_manager.generate_response = AsyncMock(
            return_value={
                "content": "测试响应",
                "tokens": 100,
                "cost": 0.001,
                "response_time": 1.5
            }
        )
        
        result = await comparison_engine.compare_models(
            models=sample_models,
            prompt=sample_prompt,
            parameters=parameters
        )
        
        # 验证参数被传递
        comparison_engine.model_manager.generate_response.assert_called()
        call_args = comparison_engine.model_manager.generate_response.call_args
        assert call_args[1]["temperature"] == 0.7
        assert call_args[1]["max_tokens"] == 200
    
    @pytest.mark.asyncio
    async def test_compare_models_partial_failure(self, comparison_engine, 
                                                 sample_models, sample_prompt):
        """测试部分模型失败的情况"""
        def mock_generate_response(model_id, prompt, **kwargs):
            if model_id == "gpt-4":
                raise Exception("API错误")
            return {
                "content": f"{model_id}的响应",
                "tokens": 100,
                "cost": 0.001,
                "response_time": 1.5
            }
        
        comparison_engine.model_manager.generate_response = AsyncMock(
            side_effect=mock_generate_response
        )
        
        result = await comparison_engine.compare_models(
            models=sample_models,
            prompt=sample_prompt
        )
        
        # 应该有2个成功结果和1个错误
        successful_results = [r for r in result["results"] if "error" not in r]
        error_results = [r for r in result["results"] if "error" in r]
        
        assert len(successful_results) == 2
        assert len(error_results) == 1
        assert error_results[0]["model_id"] == "gpt-4"
    
    @pytest.mark.asyncio
    async def test_compare_models_empty_list(self, comparison_engine, sample_prompt):
        """测试空模型列表"""
        with pytest.raises(ValueError, match="模型列表不能为空"):
            await comparison_engine.compare_models(
                models=[],
                prompt=sample_prompt
            )
    
    @pytest.mark.asyncio
    async def test_compare_models_empty_prompt(self, comparison_engine, sample_models):
        """测试空提示词"""
        with pytest.raises(ValueError, match="提示词不能为空"):
            await comparison_engine.compare_models(
                models=sample_models,
                prompt=""
            )
    
    def test_analyze_results(self, comparison_engine, sample_responses):
        """测试结果分析"""
        results = [
            {
                "model_id": "gpt-4",
                "response": sample_responses["gpt-4"],
                "metrics": {
                    "tokens": 150,
                    "cost": 0.003,
                    "response_time": 2.5
                }
            },
            {
                "model_id": "claude-3",
                "response": sample_responses["claude-3"],
                "metrics": {
                    "tokens": 140,
                    "cost": 0.0025,
                    "response_time": 2.1
                }
            }
        ]
        
        analysis = comparison_engine.analyze_results(results)
        
        assert "best_performance" in analysis
        assert "cost_analysis" in analysis
        assert "token_analysis" in analysis
        assert "response_time_analysis" in analysis
        
        # 验证最佳性能分析
        assert "fastest" in analysis["best_performance"]
        assert "most_cost_effective" in analysis["best_performance"]
        assert "most_tokens" in analysis["best_performance"]
    
    def test_calculate_similarity(self, comparison_engine):
        """测试相似度计算"""
        text1 = "人工智能是计算机科学的一个分支"
        text2 = "AI是计算机科学的一个领域"
        text3 = "今天天气很好"
        
        # 相似文本应该有较高相似度
        similarity1 = comparison_engine.calculate_similarity(text1, text2)
        assert 0.3 < similarity1 < 1.0
        
        # 不相似文本应该有较低相似度
        similarity2 = comparison_engine.calculate_similarity(text1, text3)
        assert 0.0 <= similarity2 < 0.3
        
        # 相同文本应该有最高相似度
        similarity3 = comparison_engine.calculate_similarity(text1, text1)
        assert similarity3 == 1.0
    
    def test_rank_responses(self, comparison_engine):
        """测试响应排名"""
        responses = [
            {
                "model_id": "model1",
                "metrics": {
                    "response_time": 3.0,
                    "cost": 0.005,
                    "tokens": 100
                }
            },
            {
                "model_id": "model2",
                "metrics": {
                    "response_time": 1.5,
                    "cost": 0.002,
                    "tokens": 150
                }
            },
            {
                "model_id": "model3",
                "metrics": {
                    "response_time": 2.0,
                    "cost": 0.003,
                    "tokens": 120
                }
            }
        ]
        
        # 按响应时间排名
        ranked_by_time = comparison_engine.rank_responses(
            responses, criteria="response_time"
        )
        assert ranked_by_time[0]["model_id"] == "model2"  # 最快
        
        # 按成本排名
        ranked_by_cost = comparison_engine.rank_responses(
            responses, criteria="cost"
        )
        assert ranked_by_cost[0]["model_id"] == "model2"  # 最便宜
        
        # 按token数量排名
        ranked_by_tokens = comparison_engine.rank_responses(
            responses, criteria="tokens", ascending=False
        )
        assert ranked_by_tokens[0]["model_id"] == "model2"  # 最多tokens
    
    @pytest.mark.asyncio
    async def test_batch_compare(self, comparison_engine, sample_models):
        """测试批量比较"""
        prompts = [
            "什么是人工智能？",
            "解释机器学习的概念",
            "深度学习的应用有哪些？"
        ]
        
        comparison_engine.model_manager.generate_response = AsyncMock(
            return_value={
                "content": "测试响应",
                "tokens": 100,
                "cost": 0.001,
                "response_time": 1.5
            }
        )
        
        results = await comparison_engine.batch_compare(
            models=sample_models,
            prompts=prompts
        )
        
        assert len(results) == 3  # 3个提示词
        for result in results:
            assert "prompt" in result
            assert "results" in result
            assert "summary" in result
    
    def test_export_comparison_results(self, comparison_engine, tmp_path):
        """测试导出比较结果"""
        results = {
            "results": [
                {
                    "model_id": "gpt-4",
                    "response": {"content": "测试响应"},
                    "metrics": {"tokens": 100, "cost": 0.001}
                }
            ],
            "summary": {"total_models": 1},
            "metadata": {"timestamp": datetime.now().isoformat()}
        }
        
        # 测试JSON导出
        json_file = tmp_path / "results.json"
        comparison_engine.export_results(results, str(json_file), format="json")
        assert json_file.exists()
        
        # 测试CSV导出
        csv_file = tmp_path / "results.csv"
        comparison_engine.export_results(results, str(csv_file), format="csv")
        assert csv_file.exists()
    
    def test_get_comparison_history(self, comparison_engine, mock_db_session):
        """测试获取比较历史"""
        # 模拟数据库查询
        mock_conversations = [
            Mock(
                id=1,
                title="比较测试1",
                created_at=datetime.now(),
                session=Mock(models=["gpt-4", "claude-3"])
            ),
            Mock(
                id=2,
                title="比较测试2",
                created_at=datetime.now(),
                session=Mock(models=["gemini-pro"])
            )
        ]
        
        mock_db_session.query.return_value.filter.return_value.order_by.return_value.limit.return_value.all.return_value = mock_conversations
        
        history = comparison_engine.get_comparison_history(
            db=mock_db_session,
            limit=10
        )
        
        assert len(history) == 2
        assert history[0]["id"] == 1
        assert history[0]["title"] == "比较测试1"
    
    @pytest.mark.asyncio
    async def test_real_time_comparison(self, comparison_engine, sample_models):
        """测试实时比较"""
        prompt = "测试实时比较"
        
        # 模拟流式响应
        async def mock_stream_response(model_id, prompt, **kwargs):
            for i in range(3):
                yield {
                    "chunk": f"响应片段{i+1}",
                    "model_id": model_id,
                    "partial": i < 2
                }
        
        comparison_engine.model_manager.stream_response = AsyncMock(
            side_effect=mock_stream_response
        )
        
        results = []
        async for chunk in comparison_engine.real_time_compare(
            models=sample_models[:1],  # 只测试一个模型
            prompt=prompt
        ):
            results.append(chunk)
        
        assert len(results) == 3
        assert all("chunk" in result for result in results)
        assert all("model_id" in result for result in results)
    
    def test_validate_comparison_config(self, comparison_engine):
        """测试比较配置验证"""
        # 有效配置
        valid_config = {
            "models": ["gpt-4", "claude-3"],
            "parameters": {
                "temperature": 0.7,
                "max_tokens": 200
            },
            "criteria": ["response_time", "cost", "quality"]
        }
        
        assert comparison_engine.validate_config(valid_config) is True
        
        # 无效配置 - 缺少模型
        invalid_config1 = {
            "parameters": {"temperature": 0.7}
        }
        
        assert comparison_engine.validate_config(invalid_config1) is False
        
        # 无效配置 - 无效参数
        invalid_config2 = {
            "models": ["gpt-4"],
            "parameters": {
                "temperature": 2.0  # 超出范围
            }
        }
        
        assert comparison_engine.validate_config(invalid_config2) is False