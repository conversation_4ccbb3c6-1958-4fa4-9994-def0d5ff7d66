import json
from unittest.mock import Mock

import pytest
import yaml

from src.core.prompt_manager import PromptManager


class TestPromptManager:
    """提示词管理器测试类"""
    
    @pytest.fixture
    def prompt_manager(self):
        """创建提示词管理器实例"""
        return PromptManager()
    
    @pytest.fixture
    def sample_prompts(self):
        """示例提示词数据"""
        return {
            "system_prompts": {
                "assistant": "你是一个有用的AI助手",
                "translator": "你是一个专业的翻译助手",
                "coder": "你是一个专业的编程助手"
            },
            "user_prompts": {
                "greeting": "你好，请介绍一下自己",
                "explanation": "请解释{topic}的概念",
                "comparison": "请比较{item1}和{item2}的区别"
            },
            "templates": {
                "qa_format": "问题：{question}\n答案：{answer}",
                "conversation": "用户：{user_input}\n助手：{assistant_response}"
            }
        }
    
    @pytest.fixture
    def sample_categories(self):
        """示例提示词分类"""
        return {
            "general": ["greeting", "explanation"],
            "technical": ["coder", "comparison"],
            "creative": ["story", "poem"],
            "educational": ["explanation", "qa_format"]
        }
    
    def test_load_prompts_from_file(self, prompt_manager, sample_prompts, tmp_path):
        """测试从文件加载提示词"""
        # 创建临时提示词文件
        prompt_file = tmp_path / "prompts.json"
        with open(prompt_file, 'w', encoding='utf-8') as f:
            json.dump(sample_prompts, f, ensure_ascii=False)
        
        prompt_manager.load_prompts_from_file(str(prompt_file))
        
        assert len(prompt_manager.system_prompts) == 3
        assert len(prompt_manager.user_prompts) == 3
        assert len(prompt_manager.templates) == 2
        assert prompt_manager.system_prompts["assistant"] == "你是一个有用的AI助手"
    
    def test_load_prompts_from_yaml(self, prompt_manager, sample_prompts, tmp_path):
        """测试从YAML文件加载提示词"""
        # 创建临时YAML文件
        yaml_file = tmp_path / "prompts.yaml"
        with open(yaml_file, 'w', encoding='utf-8') as f:
            yaml.dump(sample_prompts, f, allow_unicode=True)
        
        prompt_manager.load_prompts_from_file(str(yaml_file))
        
        assert len(prompt_manager.system_prompts) == 3
        assert prompt_manager.system_prompts["translator"] == "你是一个专业的翻译助手"
    
    def test_load_prompts_file_not_found(self, prompt_manager):
        """测试加载不存在的文件"""
        with pytest.raises(FileNotFoundError):
            prompt_manager.load_prompts_from_file("nonexistent.json")
    
    def test_load_prompts_invalid_json(self, prompt_manager, tmp_path):
        """测试加载无效JSON文件"""
        invalid_file = tmp_path / "invalid.json"
        with open(invalid_file, 'w') as f:
            f.write("invalid json content")
        
        with pytest.raises(json.JSONDecodeError):
            prompt_manager.load_prompts_from_file(str(invalid_file))
    
    def test_add_system_prompt(self, prompt_manager):
        """测试添加系统提示词"""
        prompt_manager.add_system_prompt(
            "researcher", 
            "你是一个专业的研究助手"
        )
        
        assert "researcher" in prompt_manager.system_prompts
        assert prompt_manager.system_prompts["researcher"] == "你是一个专业的研究助手"
    
    def test_add_user_prompt(self, prompt_manager):
        """测试添加用户提示词"""
        prompt_manager.add_user_prompt(
            "analysis", 
            "请分析{data}的趋势"
        )
        
        assert "analysis" in prompt_manager.user_prompts
        assert prompt_manager.user_prompts["analysis"] == "请分析{data}的趋势"
    
    def test_add_template(self, prompt_manager):
        """测试添加模板"""
        prompt_manager.add_template(
            "report", 
            "标题：{title}\n内容：{content}\n结论：{conclusion}"
        )
        
        assert "report" in prompt_manager.templates
        assert "标题：{title}" in prompt_manager.templates["report"]
    
    def test_get_system_prompt(self, prompt_manager, sample_prompts):
        """测试获取系统提示词"""
        prompt_manager.system_prompts = sample_prompts["system_prompts"]
        
        # 获取存在的提示词
        prompt = prompt_manager.get_system_prompt("assistant")
        assert prompt == "你是一个有用的AI助手"
        
        # 获取不存在的提示词
        prompt = prompt_manager.get_system_prompt("nonexistent")
        assert prompt is None
        
        # 获取不存在的提示词，使用默认值
        prompt = prompt_manager.get_system_prompt("nonexistent", "默认提示词")
        assert prompt == "默认提示词"
    
    def test_get_user_prompt(self, prompt_manager, sample_prompts):
        """测试获取用户提示词"""
        prompt_manager.user_prompts = sample_prompts["user_prompts"]
        
        prompt = prompt_manager.get_user_prompt("greeting")
        assert prompt == "你好，请介绍一下自己"
        
        prompt = prompt_manager.get_user_prompt("nonexistent")
        assert prompt is None
    
    def test_get_template(self, prompt_manager, sample_prompts):
        """测试获取模板"""
        prompt_manager.templates = sample_prompts["templates"]
        
        template = prompt_manager.get_template("qa_format")
        assert template == "问题：{question}\n答案：{answer}"
        
        template = prompt_manager.get_template("nonexistent")
        assert template is None
    
    def test_format_prompt(self, prompt_manager):
        """测试格式化提示词"""
        template = "请解释{topic}的概念，并给出{examples}个例子"
        
        formatted = prompt_manager.format_prompt(
            template, 
            topic="机器学习", 
            examples=3
        )
        
        assert formatted == "请解释机器学习的概念，并给出3个例子"
    
    def test_format_prompt_missing_variables(self, prompt_manager):
        """测试格式化提示词时缺少变量"""
        template = "请解释{topic}的概念，并给出{examples}个例子"
        
        with pytest.raises(KeyError):
            prompt_manager.format_prompt(template, topic="机器学习")
    
    def test_format_template(self, prompt_manager, sample_prompts):
        """测试格式化模板"""
        prompt_manager.templates = sample_prompts["templates"]
        
        formatted = prompt_manager.format_template(
            "qa_format",
            question="什么是AI？",
            answer="AI是人工智能的缩写"
        )
        
        expected = "问题：什么是AI？\n答案：AI是人工智能的缩写"
        assert formatted == expected
    
    def test_format_template_not_found(self, prompt_manager):
        """测试格式化不存在的模板"""
        with pytest.raises(ValueError, match="模板 'nonexistent' 不存在"):
            prompt_manager.format_template("nonexistent", var="value")
    
    def test_list_prompts(self, prompt_manager, sample_prompts):
        """测试列出提示词"""
        prompt_manager.system_prompts = sample_prompts["system_prompts"]
        prompt_manager.user_prompts = sample_prompts["user_prompts"]
        prompt_manager.templates = sample_prompts["templates"]
        
        # 列出系统提示词
        system_list = prompt_manager.list_prompts("system")
        assert len(system_list) == 3
        assert "assistant" in system_list
        
        # 列出用户提示词
        user_list = prompt_manager.list_prompts("user")
        assert len(user_list) == 3
        assert "greeting" in user_list
        
        # 列出模板
        template_list = prompt_manager.list_prompts("template")
        assert len(template_list) == 2
        assert "qa_format" in template_list
        
        # 列出所有
        all_list = prompt_manager.list_prompts("all")
        assert len(all_list) == 8  # 3 + 3 + 2
    
    def test_search_prompts(self, prompt_manager, sample_prompts):
        """测试搜索提示词"""
        prompt_manager.system_prompts = sample_prompts["system_prompts"]
        prompt_manager.user_prompts = sample_prompts["user_prompts"]
        
        # 搜索包含"助手"的提示词
        results = prompt_manager.search_prompts("助手")
        assert len(results) >= 2  # assistant 和 translator
        
        # 搜索包含"编程"的提示词
        results = prompt_manager.search_prompts("编程")
        assert len(results) == 1
        assert results[0]["key"] == "coder"
    
    def test_delete_prompt(self, prompt_manager, sample_prompts):
        """测试删除提示词"""
        prompt_manager.system_prompts = sample_prompts["system_prompts"].copy()
        
        # 删除存在的提示词
        result = prompt_manager.delete_prompt("system", "assistant")
        assert result is True
        assert "assistant" not in prompt_manager.system_prompts
        
        # 删除不存在的提示词
        result = prompt_manager.delete_prompt("system", "nonexistent")
        assert result is False
    
    def test_update_prompt(self, prompt_manager, sample_prompts):
        """测试更新提示词"""
        prompt_manager.system_prompts = sample_prompts["system_prompts"].copy()
        
        # 更新存在的提示词
        new_content = "你是一个更加智能的AI助手"
        result = prompt_manager.update_prompt("system", "assistant", new_content)
        assert result is True
        assert prompt_manager.system_prompts["assistant"] == new_content
        
        # 更新不存在的提示词
        result = prompt_manager.update_prompt("system", "nonexistent", "内容")
        assert result is False
    
    def test_save_prompts_to_file(self, prompt_manager, sample_prompts, tmp_path):
        """测试保存提示词到文件"""
        prompt_manager.system_prompts = sample_prompts["system_prompts"]
        prompt_manager.user_prompts = sample_prompts["user_prompts"]
        prompt_manager.templates = sample_prompts["templates"]
        
        # 保存为JSON
        json_file = tmp_path / "output.json"
        prompt_manager.save_prompts_to_file(str(json_file))
        
        assert json_file.exists()
        
        # 验证保存的内容
        with open(json_file, 'r', encoding='utf-8') as f:
            saved_data = json.load(f)
        
        assert "system_prompts" in saved_data
        assert "user_prompts" in saved_data
        assert "templates" in saved_data
        assert len(saved_data["system_prompts"]) == 3
    
    def test_save_prompts_to_yaml(self, prompt_manager, sample_prompts, tmp_path):
        """测试保存提示词到YAML文件"""
        prompt_manager.system_prompts = sample_prompts["system_prompts"]
        prompt_manager.user_prompts = sample_prompts["user_prompts"]
        
        yaml_file = tmp_path / "output.yaml"
        prompt_manager.save_prompts_to_file(str(yaml_file))
        
        assert yaml_file.exists()
        
        # 验证保存的内容
        with open(yaml_file, 'r', encoding='utf-8') as f:
            saved_data = yaml.safe_load(f)
        
        assert "system_prompts" in saved_data
        assert len(saved_data["system_prompts"]) == 3
    
    def test_validate_prompt(self, prompt_manager):
        """测试验证提示词"""
        # 有效提示词
        assert prompt_manager.validate_prompt("这是一个有效的提示词") is True
        
        # 空提示词
        assert prompt_manager.validate_prompt("") is False
        assert prompt_manager.validate_prompt(None) is False
        
        # 只包含空白字符
        assert prompt_manager.validate_prompt("   \n\t   ") is False
        
        # 过长的提示词
        long_prompt = "a" * 10001  # 超过默认最大长度
        assert prompt_manager.validate_prompt(long_prompt) is False
    
    def test_get_prompt_variables(self, prompt_manager):
        """测试获取提示词中的变量"""
        template = "请解释{topic}的概念，并给出{examples}个{type}的例子"
        
        variables = prompt_manager.get_prompt_variables(template)
        
        assert len(variables) == 3
        assert "topic" in variables
        assert "examples" in variables
        assert "type" in variables
    
    def test_get_prompt_variables_no_variables(self, prompt_manager):
        """测试获取没有变量的提示词"""
        template = "这是一个没有变量的提示词"
        
        variables = prompt_manager.get_prompt_variables(template)
        
        assert len(variables) == 0
    
    def test_categorize_prompts(self, prompt_manager, sample_prompts, sample_categories):
        """测试提示词分类"""
        prompt_manager.system_prompts = sample_prompts["system_prompts"]
        prompt_manager.user_prompts = sample_prompts["user_prompts"]
        prompt_manager.categories = sample_categories
        
        # 获取特定分类的提示词
        general_prompts = prompt_manager.get_prompts_by_category("general")
        assert len(general_prompts) == 2
        
        technical_prompts = prompt_manager.get_prompts_by_category("technical")
        assert len(technical_prompts) >= 1
    
    def test_add_category(self, prompt_manager):
        """测试添加分类"""
        prompt_manager.add_category("business", ["proposal", "report"])
        
        assert "business" in prompt_manager.categories
        assert "proposal" in prompt_manager.categories["business"]
    
    def test_export_prompts(self, prompt_manager, sample_prompts, tmp_path):
        """测试导出提示词"""
        prompt_manager.system_prompts = sample_prompts["system_prompts"]
        prompt_manager.user_prompts = sample_prompts["user_prompts"]
        
        # 导出为CSV
        csv_file = tmp_path / "prompts.csv"
        prompt_manager.export_prompts(str(csv_file), format="csv")
        assert csv_file.exists()
        
        # 导出为Markdown
        md_file = tmp_path / "prompts.md"
        prompt_manager.export_prompts(str(md_file), format="markdown")
        assert md_file.exists()
    
    def test_import_prompts_from_database(self, prompt_manager, mock_db_session):
        """测试从数据库导入提示词"""
        # 模拟数据库中的消息数据
        mock_messages = [
            Mock(
                content="你是一个AI助手",
                role="system",
                conversation=Mock(title="助手配置")
            ),
            Mock(
                content="请解释人工智能",
                role="user",
                conversation=Mock(title="AI问答")
            )
        ]
        
        mock_db_session.query.return_value.filter.return_value.all.return_value = mock_messages
        
        imported_count = prompt_manager.import_from_database(mock_db_session)
        
        assert imported_count == 2
        assert len(prompt_manager.system_prompts) >= 1
        assert len(prompt_manager.user_prompts) >= 1
    
    def test_generate_prompt_variations(self, prompt_manager):
        """测试生成提示词变体"""
        base_prompt = "请解释{topic}的概念"
        
        variations = prompt_manager.generate_variations(
            base_prompt,
            count=3,
            variation_type="rephrase"
        )
        
        assert len(variations) == 3
        assert all(isinstance(v, str) for v in variations)
        assert all("{topic}" in v for v in variations)
    
    def test_prompt_statistics(self, prompt_manager, sample_prompts):
        """测试提示词统计"""
        prompt_manager.system_prompts = sample_prompts["system_prompts"]
        prompt_manager.user_prompts = sample_prompts["user_prompts"]
        prompt_manager.templates = sample_prompts["templates"]
        
        stats = prompt_manager.get_statistics()
        
        assert "total_prompts" in stats
        assert "system_prompts" in stats
        assert "user_prompts" in stats
        assert "templates" in stats
        assert "average_length" in stats
        
        assert stats["total_prompts"] == 8
        assert stats["system_prompts"] == 3
        assert stats["user_prompts"] == 3
        assert stats["templates"] == 2