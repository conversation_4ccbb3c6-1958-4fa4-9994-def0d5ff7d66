from datetime import datetime
from unittest.mock import Mock, patch, AsyncMock

import pytest

from database.models import Conversation
from src.core.conversation_manager import ConversationManager


class TestConversationManager:
    """对话管理器测试类"""
    
    @pytest.fixture
    def conversation_manager(self, mock_db_session, mock_model_manager):
        """创建对话管理器实例"""
        return ConversationManager(
            db_session=mock_db_session,
            model_manager=mock_model_manager
        )
    
    @pytest.fixture
    def sample_session(self):
        """示例会话"""
        return Mock(
            id=1,
            name="测试会话",
            models=["gpt-4", "claude-3"],
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
    
    @pytest.fixture
    def sample_conversation(self, sample_session):
        """示例对话"""
        return Mock(
            id=1,
            title="测试对话",
            session_id=1,
            session=sample_session,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            messages=[]
        )
    
    @pytest.fixture
    def sample_messages(self, sample_conversation):
        """示例消息"""
        return [
            Mock(
                id=1,
                conversation_id=1,
                conversation=sample_conversation,
                role="user",
                content="你好",
                model_id=None,
                created_at=datetime.now()
            ),
            Mock(
                id=2,
                conversation_id=1,
                conversation=sample_conversation,
                role="assistant",
                content="你好！我是AI助手",
                model_id="gpt-4",
                created_at=datetime.now()
            )
        ]
    
    @pytest.mark.asyncio
    async def test_create_conversation(self, conversation_manager, sample_session):
        """测试创建对话"""
        conversation_manager.db_session.query.return_value.filter.return_value.first.return_value = sample_session
        
        # 模拟数据库操作
        mock_conversation = Mock(id=1, title="新对话")
        conversation_manager.db_session.add = Mock()
        conversation_manager.db_session.commit = Mock()
        conversation_manager.db_session.refresh = Mock()
        
        with patch('database.models.Conversation', return_value=mock_conversation):
            result = await conversation_manager.create_conversation(
                session_id=1,
                title="新对话"
            )
        
        assert result.id == 1
        assert result.title == "新对话"
        conversation_manager.db_session.add.assert_called_once()
        conversation_manager.db_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_create_conversation_invalid_session(self, conversation_manager):
        """测试创建对话时会话不存在"""
        conversation_manager.db_session.query.return_value.filter.return_value.first.return_value = None
        
        with pytest.raises(ValueError, match="会话不存在"):
            await conversation_manager.create_conversation(
                session_id=999,
                title="新对话"
            )
    
    @pytest.mark.asyncio
    async def test_get_conversation(self, conversation_manager, sample_conversation):
        """测试获取对话"""
        conversation_manager.db_session.query.return_value.filter.return_value.first.return_value = sample_conversation
        
        result = await conversation_manager.get_conversation(1)
        
        assert result.id == 1
        assert result.title == "测试对话"
    
    @pytest.mark.asyncio
    async def test_get_conversation_not_found(self, conversation_manager):
        """测试获取不存在的对话"""
        conversation_manager.db_session.query.return_value.filter.return_value.first.return_value = None
        
        result = await conversation_manager.get_conversation(999)
        
        assert result is None
    
    @pytest.mark.asyncio
    async def test_add_message(self, conversation_manager, sample_conversation):
        """测试添加消息"""
        conversation_manager.db_session.query.return_value.filter.return_value.first.return_value = sample_conversation
        
        mock_message = Mock(id=1, content="测试消息")
        conversation_manager.db_session.add = Mock()
        conversation_manager.db_session.commit = Mock()
        conversation_manager.db_session.refresh = Mock()
        
        with patch('database.models.Message', return_value=mock_message):
            result = await conversation_manager.add_message(
                conversation_id=1,
                role="user",
                content="测试消息"
            )
        
        assert result.id == 1
        assert result.content == "测试消息"
        conversation_manager.db_session.add.assert_called_once()
        conversation_manager.db_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_add_message_invalid_conversation(self, conversation_manager):
        """测试向不存在的对话添加消息"""
        conversation_manager.db_session.query.return_value.filter.return_value.first.return_value = None
        
        with pytest.raises(ValueError, match="对话不存在"):
            await conversation_manager.add_message(
                conversation_id=999,
                role="user",
                content="测试消息"
            )
    
    @pytest.mark.asyncio
    async def test_get_messages(self, conversation_manager, sample_messages):
        """测试获取消息列表"""
        conversation_manager.db_session.query.return_value.filter.return_value.order_by.return_value.all.return_value = sample_messages
        
        result = await conversation_manager.get_messages(1)
        
        assert len(result) == 2
        assert result[0].role == "user"
        assert result[1].role == "assistant"
    
    @pytest.mark.asyncio
    async def test_get_messages_with_pagination(self, conversation_manager, sample_messages):
        """测试分页获取消息"""
        conversation_manager.db_session.query.return_value.filter.return_value.order_by.return_value.offset.return_value.limit.return_value.all.return_value = sample_messages[:1]
        
        result = await conversation_manager.get_messages(
            conversation_id=1,
            skip=0,
            limit=1
        )
        
        assert len(result) == 1
        assert result[0].role == "user"
    
    @pytest.mark.asyncio
    async def test_update_conversation(self, conversation_manager, sample_conversation):
        """测试更新对话"""
        conversation_manager.db_session.query.return_value.filter.return_value.first.return_value = sample_conversation
        conversation_manager.db_session.commit = Mock()
        
        result = await conversation_manager.update_conversation(
            conversation_id=1,
            title="更新的标题"
        )
        
        assert result.title == "更新的标题"
        conversation_manager.db_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_update_conversation_not_found(self, conversation_manager):
        """测试更新不存在的对话"""
        conversation_manager.db_session.query.return_value.filter.return_value.first.return_value = None
        
        result = await conversation_manager.update_conversation(
            conversation_id=999,
            title="更新的标题"
        )
        
        assert result is None
    
    @pytest.mark.asyncio
    async def test_delete_conversation(self, conversation_manager, sample_conversation):
        """测试删除对话"""
        conversation_manager.db_session.query.return_value.filter.return_value.first.return_value = sample_conversation
        conversation_manager.db_session.delete = Mock()
        conversation_manager.db_session.commit = Mock()
        
        result = await conversation_manager.delete_conversation(1)
        
        assert result is True
        conversation_manager.db_session.delete.assert_called_once_with(sample_conversation)
        conversation_manager.db_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_delete_conversation_not_found(self, conversation_manager):
        """测试删除不存在的对话"""
        conversation_manager.db_session.query.return_value.filter.return_value.first.return_value = None
        
        result = await conversation_manager.delete_conversation(999)
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_generate_response(self, conversation_manager, sample_conversation, sample_messages):
        """测试生成响应"""
        conversation_manager.db_session.query.return_value.filter.return_value.first.return_value = sample_conversation
        conversation_manager.db_session.query.return_value.filter.return_value.order_by.return_value.all.return_value = sample_messages
        
        # 模拟模型响应
        mock_response = {
            "content": "这是AI的回复",
            "model_id": "gpt-4",
            "tokens": 100,
            "cost": 0.001
        }
        conversation_manager.model_manager.generate_response = AsyncMock(return_value=mock_response)
        
        # 模拟添加消息
        mock_message = Mock(id=3, content="这是AI的回复")
        conversation_manager.db_session.add = Mock()
        conversation_manager.db_session.commit = Mock()
        conversation_manager.db_session.refresh = Mock()
        
        with patch('database.models.Message', return_value=mock_message):
            result = await conversation_manager.generate_response(
                conversation_id=1,
                user_message="用户的问题",
                model_id="gpt-4"
            )
        
        assert result["content"] == "这是AI的回复"
        assert result["model_id"] == "gpt-4"
        conversation_manager.model_manager.generate_response.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_generate_response_invalid_conversation(self, conversation_manager):
        """测试为不存在的对话生成响应"""
        conversation_manager.db_session.query.return_value.filter.return_value.first.return_value = None
        
        with pytest.raises(ValueError, match="对话不存在"):
            await conversation_manager.generate_response(
                conversation_id=999,
                user_message="用户的问题",
                model_id="gpt-4"
            )
    
    @pytest.mark.asyncio
    async def test_get_conversation_history(self, conversation_manager, sample_messages):
        """测试获取对话历史"""
        conversation_manager.db_session.query.return_value.filter.return_value.order_by.return_value.all.return_value = sample_messages
        
        history = await conversation_manager.get_conversation_history(1)
        
        assert len(history) == 2
        assert history[0]["role"] == "user"
        assert history[0]["content"] == "你好"
        assert history[1]["role"] == "assistant"
        assert history[1]["content"] == "你好！我是AI助手"
    
    @pytest.mark.asyncio
    async def test_search_conversations(self, conversation_manager):
        """测试搜索对话"""
        mock_conversations = [
            Mock(id=1, title="AI讨论", created_at=datetime.now()),
            Mock(id=2, title="编程问题", created_at=datetime.now())
        ]
        
        conversation_manager.db_session.query.return_value.filter.return_value.order_by.return_value.all.return_value = mock_conversations
        
        result = await conversation_manager.search_conversations("AI")
        
        assert len(result) == 2
        assert result[0].title == "AI讨论"
    
    @pytest.mark.asyncio
    async def test_get_conversations_by_session(self, conversation_manager):
        """测试按会话获取对话"""
        mock_conversations = [
            Mock(id=1, title="对话1", session_id=1),
            Mock(id=2, title="对话2", session_id=1)
        ]
        
        conversation_manager.db_session.query.return_value.filter.return_value.order_by.return_value.all.return_value = mock_conversations
        
        result = await conversation_manager.get_conversations_by_session(1)
        
        assert len(result) == 2
        assert all(conv.session_id == 1 for conv in result)
    
    @pytest.mark.asyncio
    async def test_export_conversation(self, conversation_manager, sample_conversation, sample_messages, tmp_path):
        """测试导出对话"""
        conversation_manager.db_session.query.return_value.filter.return_value.first.return_value = sample_conversation
        conversation_manager.db_session.query.return_value.filter.return_value.order_by.return_value.all.return_value = sample_messages
        
        # 测试导出为JSON
        json_file = tmp_path / "conversation.json"
        result = await conversation_manager.export_conversation(
            conversation_id=1,
            file_path=str(json_file),
            format="json"
        )
        
        assert result is True
        assert json_file.exists()
        
        # 测试导出为Markdown
        md_file = tmp_path / "conversation.md"
        result = await conversation_manager.export_conversation(
            conversation_id=1,
            file_path=str(md_file),
            format="markdown"
        )
        
        assert result is True
        assert md_file.exists()
    
    @pytest.mark.asyncio
    async def test_import_conversation(self, conversation_manager, tmp_path):
        """测试导入对话"""
        # 创建测试数据文件
        import json
        conversation_data = {
            "title": "导入的对话",
            "session_id": 1,
            "messages": [
                {"role": "user", "content": "你好"},
                {"role": "assistant", "content": "你好！", "model_id": "gpt-4"}
            ]
        }
        
        json_file = tmp_path / "import.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(conversation_data, f, ensure_ascii=False)
        
        # 模拟数据库操作
        mock_conversation = Mock(id=1, title="导入的对话")
        mock_message = Mock(id=1, content="测试")
        conversation_manager.db_session.add = Mock()
        conversation_manager.db_session.commit = Mock()
        conversation_manager.db_session.refresh = Mock()
        
        with patch('database.models.Conversation', return_value=mock_conversation), \
             patch('database.models.Message', return_value=mock_message):
            result = await conversation_manager.import_conversation(str(json_file))
        
        assert result.id == 1
        assert result.title == "导入的对话"
    
    @pytest.mark.asyncio
    async def test_get_conversation_statistics(self, conversation_manager):
        """测试获取对话统计"""
        # 模拟统计查询结果
        conversation_manager.db_session.query.return_value.count.return_value = 10
        conversation_manager.db_session.query.return_value.filter.return_value.count.return_value = 5
        
        stats = await conversation_manager.get_conversation_statistics(session_id=1)
        
        assert "total_conversations" in stats
        assert "total_messages" in stats
        assert "average_messages_per_conversation" in stats
    
    @pytest.mark.asyncio
    async def test_clone_conversation(self, conversation_manager, sample_conversation, sample_messages):
        """测试克隆对话"""
        conversation_manager.db_session.query.return_value.filter.return_value.first.return_value = sample_conversation
        conversation_manager.db_session.query.return_value.filter.return_value.order_by.return_value.all.return_value = sample_messages
        
        # 模拟创建新对话和消息
        mock_new_conversation = Mock(id=2, title="测试对话 (副本)")
        mock_new_message = Mock(id=3, content="克隆的消息")
        conversation_manager.db_session.add = Mock()
        conversation_manager.db_session.commit = Mock()
        conversation_manager.db_session.refresh = Mock()
        
        with patch('database.models.Conversation', return_value=mock_new_conversation), \
             patch('database.models.Message', return_value=mock_new_message):
            result = await conversation_manager.clone_conversation(
                conversation_id=1,
                new_title="测试对话 (副本)"
            )
        
        assert result.id == 2
        assert result.title == "测试对话 (副本)"
    
    @pytest.mark.asyncio
    async def test_merge_conversations(self, conversation_manager):
        """测试合并对话"""
        # 模拟两个对话
        conv1 = Mock(id=1, title="对话1", session_id=1)
        conv2 = Mock(id=2, title="对话2", session_id=1)
        
        messages1 = [Mock(id=1, content="消息1", role="user")]
        messages2 = [Mock(id=2, content="消息2", role="assistant")]
        
        def mock_query_side_effect(*args):
            if args[0] == Conversation:
                return Mock(
                    filter=Mock(
                        return_value=Mock(
                            first=Mock(side_effect=[conv1, conv2])
                        )
                    )
                )
            elif args[0] == Message:
                return Mock(
                    filter=Mock(
                        return_value=Mock(
                            order_by=Mock(
                                return_value=Mock(
                                    all=Mock(side_effect=[messages1, messages2])
                                )
                            )
                        )
                    )
                )
            return None

        conversation_manager.db_session.query.side_effect = mock_query_side_effect
        
        # 模拟创建合并后的对话
        mock_merged_conversation = Mock(id=3, title="合并的对话")
        conversation_manager.db_session.add = Mock()
        conversation_manager.db_session.commit = Mock()
        conversation_manager.db_session.delete = Mock()
        
        with patch('database.models.Conversation', return_value=mock_merged_conversation):
            result = await conversation_manager.merge_conversations(
                conversation_ids=[1, 2],
                new_title="合并的对话"
            )
        
        assert result.id == 3
        assert result.title == "合并的对话"
    
    @pytest.mark.asyncio
    async def test_get_recent_conversations(self, conversation_manager):
        """测试获取最近对话"""
        mock_conversations = [
            Mock(id=1, title="最近对话1", updated_at=datetime.now()),
            Mock(id=2, title="最近对话2", updated_at=datetime.now())
        ]
        
        conversation_manager.db_session.query.return_value.order_by.return_value.limit.return_value.all.return_value = mock_conversations
        
        result = await conversation_manager.get_recent_conversations(limit=5)
        
        assert len(result) == 2
        assert result[0].title == "最近对话1"
    
    @pytest.mark.asyncio
    async def test_archive_conversation(self, conversation_manager, sample_conversation):
        """测试归档对话"""
        conversation_manager.db_session.query.return_value.filter.return_value.first.return_value = sample_conversation
        conversation_manager.db_session.commit = Mock()
        
        result = await conversation_manager.archive_conversation(1)
        
        assert result is True
        assert sample_conversation.archived is True
        conversation_manager.db_session.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_restore_conversation(self, conversation_manager, sample_conversation):
        """测试恢复对话"""
        sample_conversation.archived = True
        conversation_manager.db_session.query.return_value.filter.return_value.first.return_value = sample_conversation
        conversation_manager.db_session.commit = Mock()
        
        result = await conversation_manager.restore_conversation(1)
        
        assert result is True
        assert sample_conversation.archived is False
        conversation_manager.db_session.commit.assert_called_once()