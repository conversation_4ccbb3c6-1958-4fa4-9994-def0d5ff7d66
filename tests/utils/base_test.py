"""基础测试工具类

提供通用的测试工具和方法，减少重复代码
"""

from typing import Dict, Any, List
from unittest.mock import Mock, AsyncMock

import pytest
from sqlalchemy.orm import Session

from database.models import Session as SessionModel, Conversation, Message, APIKey


class BaseTestCase:
    """基础测试用例类"""
    
    @staticmethod
    def create_test_session(
        db_session: Session,
        name: str = "测试会话",
        description: str = "测试描述",
        models: List[str] = None
    ) -> SessionModel:
        """创建测试会话"""
        if models is None:
            models = ["gpt-3.5-turbo", "claude-3-sonnet"]
        
        session = SessionModel(
            name=name,
            description=description,
            models=models
        )
        db_session.add(session)
        db_session.commit()
        db_session.refresh(session)
        return session
    
    @staticmethod
    def create_test_conversation(
        db_session: Session,
        session_id: int,
        title: str = "测试对话"
    ) -> Conversation:
        """创建测试对话"""
        conversation = Conversation(
            title=title,
            session_id=session_id
        )
        db_session.add(conversation)
        db_session.commit()
        db_session.refresh(conversation)
        return conversation
    
    @staticmethod
    def create_test_message(
        db_session: Session,
        conversation_id: int,
        content: str = "测试消息",
        role: str = "user",
        model: str = "gpt-3.5-turbo"
    ) -> Message:
        """创建测试消息"""
        message = Message(
            conversation_id=conversation_id,
            content=content,
            role=role,
            model=model
        )
        db_session.add(message)
        db_session.commit()
        db_session.refresh(message)
        return message
    
    @staticmethod
    def create_test_api_key(
        db_session: Session,
        name: str = "测试API密钥",
        permissions: Dict[str, bool] = None
    ) -> APIKey:
        """创建测试API密钥"""
        if permissions is None:
            permissions = {
                "read": True,
                "write": True,
                "delete": False,
                "export": True
            }
        
        api_key = APIKey(
            name=name,
            key="test-api-key-123",
            permissions=permissions
        )
        db_session.add(api_key)
        db_session.commit()
        db_session.refresh(api_key)
        return api_key


class MockFactory:
    """模拟对象工厂"""
    
    @staticmethod
    def create_mock_model(
        model_id: str = "gpt-3.5-turbo",
        provider: str = "openai",
        response_content: str = "模拟回复"
    ) -> Mock:
        """创建模拟模型"""
        model = Mock()
        model.model_id = model_id
        model.provider = provider
        model.is_available.return_value = True
        model.generate_response = AsyncMock(return_value={
            "content": response_content,
            "usage": {
                "prompt_tokens": 10,
                "completion_tokens": 20,
                "total_tokens": 30
            },
            "model": model_id,
            "finish_reason": "stop"
        })
        return model
    
    @staticmethod
    def create_mock_model_manager(models: List[str] = None) -> Mock:
        """创建模拟模型管理器"""
        if models is None:
            models = ["gpt-3.5-turbo", "claude-3-sonnet"]
        
        manager = Mock()
        manager.get_available_models.return_value = models
        manager.get_model.return_value = MockFactory.create_mock_model()
        manager.generate_response = AsyncMock(return_value={
            "content": "模拟回复",
            "usage": {"total_tokens": 30},
            "model": models[0] if models else "gpt-3.5-turbo"
        })
        return manager
    
    @staticmethod
    def create_mock_comparison_engine() -> Mock:
        """创建模拟比较引擎"""
        engine = Mock()
        engine.compare_models = AsyncMock(return_value={
            "results": {
                "gpt-3.5-turbo": {"content": "GPT回复", "score": 0.85},
                "claude-3-sonnet": {"content": "Claude回复", "score": 0.90}
            },
            "comparison": {
                "best_model": "claude-3-sonnet",
                "metrics": {
                    "response_time": 1.5,
                    "token_efficiency": 0.85
                }
            }
        })
        return engine


class TestDataFactory:
    """测试数据工厂"""
    
    @staticmethod
    def get_sample_conversation_data() -> Dict[str, Any]:
        """获取示例对话数据"""
        return {
            "message": "你好，请介绍一下人工智能",
            "models": ["gpt-3.5-turbo", "claude-3-sonnet"],
            "parameters": {
                "temperature": 0.7,
                "max_tokens": 1000
            }
        }
    
    @staticmethod
    def get_sample_session_data() -> Dict[str, Any]:
        """获取示例会话数据"""
        return {
            "name": "测试会话",
            "description": "这是一个测试会话",
            "models": ["gpt-3.5-turbo", "claude-3-sonnet"]
        }
    
    @staticmethod
    def get_sample_message_data() -> Dict[str, Any]:
        """获取示例消息数据"""
        return {
            "content": "这是一条测试消息",
            "role": "user",
            "model": "gpt-3.5-turbo"
        }
    
    @staticmethod
    def get_auth_headers() -> Dict[str, str]:
        """获取认证头"""
        return {
            "Authorization": "Bearer test-api-key",
            "Content-Type": "application/json"
        }


class APITestMixin:
    """API测试混入类"""
    
    def assert_response_success(self, response, expected_status: int = 200):
        """断言响应成功"""
        assert response.status_code == expected_status
        data = response.json()
        assert data.get("success") is True
        return data
    
    def assert_response_error(self, response, expected_status: int = 400):
        """断言响应错误"""
        assert response.status_code == expected_status
        data = response.json()
        assert data.get("success") is False
        assert "message" in data or "detail" in data
        return data
    
    def assert_pagination_response(self, data: Dict[str, Any]):
        """断言分页响应"""
        assert "items" in data
        assert "total" in data
        assert "page" in data
        assert "size" in data
        assert isinstance(data["items"], list)
        assert isinstance(data["total"], int)
        assert isinstance(data["page"], int)
        assert isinstance(data["size"], int)


class DatabaseTestMixin:
    """数据库测试混入类"""
    
    def assert_record_exists(self, db_session: Session, model_class, **filters):
        """断言记录存在"""
        query = db_session.query(model_class)
        for key, value in filters.items():
            query = query.filter(getattr(model_class, key) == value)
        record = query.first()
        assert record is not None, f"记录不存在: {model_class.__name__} with {filters}"
        return record
    
    def assert_record_count(self, db_session: Session, model_class, expected_count: int, **filters):
        """断言记录数量"""
        query = db_session.query(model_class)
        for key, value in filters.items():
            query = query.filter(getattr(model_class, key) == value)
        actual_count = query.count()
        assert actual_count == expected_count, f"记录数量不匹配: 期望 {expected_count}, 实际 {actual_count}"


class AsyncTestMixin:
    """异步测试混入类"""
    
    async def assert_async_raises(self, exception_class, async_func, *args, **kwargs):
        """断言异步函数抛出异常"""
        with pytest.raises(exception_class):
            await async_func(*args, **kwargs)
    
    async def assert_async_no_exception(self, async_func, *args, **kwargs):
        """断言异步函数不抛出异常"""
        try:
            result = await async_func(*args, **kwargs)
            return result
        except Exception as e:
            pytest.fail(f"异步函数抛出了意外异常: {e}")


class BaseAPITestCase(BaseTestCase, APITestMixin, DatabaseTestMixin):
    """基础API测试用例类"""
    pass


class BaseAsyncTestCase(BaseTestCase, AsyncTestMixin, DatabaseTestMixin):
    """基础异步测试用例类"""
    pass


# 常用的测试装饰器
def skip_if_no_api_key(api_key_env: str):
    """如果没有API密钥则跳过测试"""
    import os
    return pytest.mark.skipif(
        not os.getenv(api_key_env),
        reason=f"需要设置环境变量 {api_key_env}"
    )


def requires_external_service(service_name: str):
    """需要外部服务的测试标记"""
    import os
    return pytest.mark.skipif(
        os.getenv("DISABLE_EXTERNAL_APIS", "false").lower() == "true",
        reason=f"外部服务 {service_name} 被禁用"
    )


# 常用的参数化测试数据
COMMON_MODEL_NAMES = ["gpt-3.5-turbo", "gpt-4", "claude-3-sonnet", "claude-3-haiku"]
COMMON_ROLES = ["user", "assistant", "system"]
COMMON_HTTP_METHODS = ["GET", "POST", "PUT", "DELETE"]
COMMON_STATUS_CODES = [200, 201, 400, 401, 403, 404, 422, 500]
