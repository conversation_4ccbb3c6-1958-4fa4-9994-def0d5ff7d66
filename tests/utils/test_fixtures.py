"""测试场景和环境管理工具"""

import asyncio
import os
import json
import tempfile
import shutil
from typing import Dict, List, Any, Optional, Callable, Union
from pathlib import Path
from contextlib import asynccontextmanager, contextmanager
from datetime import datetime, timezone, timedelta
import uuid
import random
import string
import time
import logging

from sqlalchemy.ext.asyncio import AsyncSession
from fastapi.testclient import TestClient

from tests.fixtures.sample_data import (
    SAMPLE_USER, SAMPLE_SESSION, SAMPLE_CONVERSATION,
    SAMPLE_MESSAGE, SAMPLE_MODEL
)


class TestDataBuilder:
    """测试数据构建器"""
    
    def __init__(self, db_session: AsyncSession):
        self.db_session = db_session
        self.created_objects = []
    
    async def create_user(self, **kwargs) -> Dict[str, Any]:
        """创建用户（兼容性函数）"""
        # 由于User模型已移除，返回模拟数据
        user_data = {
            "id": random.randint(1, 1000),
            "name": f"test_user_{uuid.uuid4().hex[:8]}",
            "email": f"test_{uuid.uuid4().hex[:8]}@example.com"
        }
        user_data.update(kwargs)
        return user_data
    
    async def create_session(self, user_id: Optional[int] = None, **kwargs) -> Dict[str, Any]:
        """创建会话"""
        from database.models import Session
        
        if user_id is None:
            user = await self.create_user()
            user_id = user["id"]
        
        session_data = {
            "id": f"session_{uuid.uuid4().hex[:8]}",
            "models": ["gpt-3.5-turbo"],
            "system_prompt": "You are a helpful assistant.",
            "status": "active",
            "total_messages": 0,
            "total_tokens": 0
        }
        session_data.update(kwargs)
        
        session = Session(**session_data)
        self.db_session.add(session)
        await self.db_session.flush()
        await self.db_session.refresh(session)
        
        self.created_objects.append((Session, session.id))
        return session
    
    async def create_conversation(self, session_id: Optional[str] = None, **kwargs) -> Dict[str, Any]:
        """创建对话"""
        from database.models import Conversation
        
        if session_id is None:
            session = await self.create_session()
            session_id = session.id
        
        conversation_data = {
            "user_message": "Hello, how are you?",
            "model_name": "gpt-3.5-turbo",
            "model_response": "I'm doing well, thank you!",
            "session_id": session_id,
            "status": "success",
            "response_time": 1.5,
            "token_count": 20
        }
        conversation_data.update(kwargs)
        
        conversation = Conversation(**conversation_data)
        self.db_session.add(conversation)
        await self.db_session.flush()
        await self.db_session.refresh(conversation)
        
        self.created_objects.append((Conversation, conversation.id))
        return conversation
    
    async def create_message(self, conversation_id: Optional[int] = None, **kwargs) -> Dict[str, Any]:
        """创建消息"""
        from src.database.models import Message
        
        if conversation_id is None:
            conversation = await self.create_conversation()
            conversation_id = conversation.id
        
        # 注释掉Message创建，因为数据库模型中已经移除了Message模型
        # message_data = SAMPLE_MESSAGE.copy()
        # message_data.update(kwargs)
        # message_data['conversation_id'] = conversation_id
        # 
        # message = Message(**message_data)
        # self.db_session.add(message)
        # await self.db_session.flush()
        # await self.db_session.refresh(message)
        # 
        # self.created_objects.append((Message, message.id))
        # return message
        return {"id": 1, "content": "mock message"}  # 返回模拟数据
    
    async def create_model(self, **kwargs) -> Dict[str, Any]:
        """创建模型（兼容性函数）"""
        # 由于Model模型已移除，返回模拟数据
        model_data = {
            "id": f"model_{uuid.uuid4().hex[:8]}",
            "name": "gpt-3.5-turbo",
            "provider": "openai",
            "version": "1.0",
            "is_active": True
        }
        model_data.update(kwargs)
        return model_data
    
    # API密钥相关功能暂时移除，因为sample_data中没有定义相关数据
    
    async def create_complete_conversation(self, message_count: int = 3) -> Dict[str, Any]:
        """创建完整对话"""
        user = await self.create_user()
        session = await self.create_session(user_id=user["id"])
        conversation = await self.create_conversation(session_id=session.id)
        
        messages = []
        for i in range(message_count):
            role = "user" if i % 2 == 0 else "assistant"
            message = await self.create_message(
                conversation_id=conversation.id,
                content=f"测试消息 {i+1}",
                role=role
            )
            messages.append(message)
        
        return {
            "user": user,
            "session": session,
            "conversation": conversation,
            "messages": messages
        }
    
    async def create_bulk_data(self, count: int = 5) -> Dict[str, List[Any]]:
        """批量创建数据"""
        users = []
        sessions = []
        conversations = []
        messages = []
        
        for i in range(count):
            user = await self.create_user(
                name=f"user_{i}",
                email=f"user_{i}@example.com"
            )
            users.append(user)
            
            session = await self.create_session(
                user_id=user["id"]
            )
            sessions.append(session)
            
            conversation = await self.create_conversation(
                session_id=session.id,
                title=f"对话 {i}"
            )
            conversations.append(conversation)
            
            for j in range(3):  # 每个对话3条消息
                role = "user" if j % 2 == 0 else "assistant"
                message = await self.create_message(
                    conversation_id=conversation.id,
                    content=f"消息 {i}-{j}",
                    role=role
                )
                messages.append(message)
        
        return {
            "users": users,
            "sessions": sessions,
            "conversations": conversations,
            "messages": messages
        }
    
    async def cleanup(self):
        """清理创建的数据"""
        # 按创建的相反顺序删除对象
        for model_class, obj_id in reversed(self.created_objects):
            try:
                obj = await self.db_session.get(model_class, obj_id)
                if obj:
                    await self.db_session.delete(obj)
            except Exception as e:
                logging.warning(f"删除对象失败: {e}")
        
        await self.db_session.commit()
        self.created_objects.clear()


class TestScenarioManager:
    """测试场景管理器"""
    
    def __init__(self):
        self.scenarios = {}
        self.current_scenario = None
    
    def register_scenario(self, name: str, setup_func: Callable, teardown_func: Optional[Callable] = None):
        """注册测试场景"""
        self.scenarios[name] = {
            "setup": setup_func,
            "teardown": teardown_func
        }
    
    async def setup_scenario(self, name: str, **kwargs) -> Any:
        """设置测试场景"""
        if name not in self.scenarios:
            raise ValueError(f"未知的测试场景: {name}")
        
        # 清理当前场景
        if self.current_scenario:
            await self.teardown_scenario()
        
        # 设置新场景
        scenario = self.scenarios[name]
        result = await scenario["setup"](**kwargs) if asyncio.iscoroutinefunction(scenario["setup"]) else scenario["setup"](**kwargs)
        self.current_scenario = name
        return result
    
    async def teardown_scenario(self):
        """清理测试场景"""
        if not self.current_scenario:
            return
        
        scenario = self.scenarios[self.current_scenario]
        if scenario["teardown"]:
            await scenario["teardown"]() if asyncio.iscoroutinefunction(scenario["teardown"]) else scenario["teardown"]()
        
        self.current_scenario = None
    
    def get_scenario_names(self) -> List[str]:
        """获取所有场景名称"""
        return list(self.scenarios.keys())


class TestEnvironmentManager:
    """测试环境管理器"""
    
    def __init__(self):
        self.temp_dirs = []
        self.temp_files = []
        self.env_vars = {}
        self.original_env_vars = {}
    
    def create_temp_dir(self) -> Path:
        """创建临时目录"""
        temp_dir = Path(tempfile.mkdtemp())
        self.temp_dirs.append(temp_dir)
        return temp_dir
    
    def create_temp_file(self, content: Optional[str] = None, suffix: str = ".txt") -> Path:
        """创建临时文件"""
        fd, path = tempfile.mkstemp(suffix=suffix)
        os.close(fd)
        
        temp_path = Path(path)
        if content is not None:
            temp_path.write_text(content)
        
        self.temp_files.append(temp_path)
        return temp_path
    
    def create_temp_json_file(self, data: Dict[str, Any], suffix: str = ".json") -> Path:
        """创建临时JSON文件"""
        content = json.dumps(data, indent=2)
        return self.create_temp_file(content, suffix)
    
    def set_env_var(self, name: str, value: str):
        """设置环境变量"""
        if name not in self.original_env_vars:
            self.original_env_vars[name] = os.environ.get(name)
        
        os.environ[name] = value
        self.env_vars[name] = value
    
    def restore_env_vars(self):
        """恢复环境变量"""
        for name, value in self.original_env_vars.items():
            if value is None:
                if name in os.environ:
                    del os.environ[name]
            else:
                os.environ[name] = value
        
        self.env_vars.clear()
        self.original_env_vars.clear()
    
    def cleanup(self):
        """清理环境"""
        # 删除临时文件
        for temp_file in self.temp_files:
            try:
                if temp_file.exists():
                    temp_file.unlink()
            except Exception as e:
                logging.warning(f"删除临时文件失败: {e}")
        
        # 删除临时目录
        for temp_dir in self.temp_dirs:
            try:
                if temp_dir.exists():
                    shutil.rmtree(temp_dir)
            except Exception as e:
                logging.warning(f"删除临时目录失败: {e}")
        
        # 恢复环境变量
        self.restore_env_vars()
        
        self.temp_files.clear()
        self.temp_dirs.clear()


class PerformanceTestHelper:
    """性能测试助手"""
    
    def __init__(self):
        self.results = []
        self.start_time = None
        self.end_time = None
    
    def start_test(self):
        """开始测试"""
        self.start_time = time.perf_counter()
        self.results.clear()
    
    def end_test(self):
        """结束测试"""
        self.end_time = time.perf_counter()
    
    def record_result(self, operation: str, duration: float, **metadata):
        """记录结果"""
        result = {
            "operation": operation,
            "duration": duration,
            "timestamp": datetime.now(timezone.utc),
            **metadata
        }
        self.results.append(result)
    
    async def measure_operation(self, operation: str, func: Callable, **kwargs) -> Any:
        """测量操作耗时"""
        start_time = time.perf_counter()
        
        try:
            if asyncio.iscoroutinefunction(func):
                result = await func(**kwargs)
            else:
                result = func(**kwargs)
            
            duration = time.perf_counter() - start_time
            self.record_result(operation, duration, success=True, **kwargs)
            return result
        except Exception as e:
            duration = time.perf_counter() - start_time
            self.record_result(operation, duration, success=False, error=str(e), **kwargs)
            raise
    
    async def run_load_test(
        self,
        operation: Callable,
        iterations: int = 10,
        concurrency: int = 3,
        **kwargs
    ) -> Dict[str, Any]:
        """运行负载测试"""
        self.start_test()
        
        semaphore = asyncio.Semaphore(concurrency)
        
        async def run_operation():
            async with semaphore:
                return await self.measure_operation("load_test", operation, **kwargs)
            return None

        tasks = [run_operation() for _ in range(iterations)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        self.end_test()
        
        # 计算统计信息
        durations = [r["duration"] for r in self.results if r["success"]]
        success_count = len(durations)
        
        stats = {
            "total": iterations,
            "success": success_count,
            "failure": iterations - success_count,
            "success_rate": success_count / iterations if iterations > 0 else 0,
            "total_duration": self.end_time - self.start_time,
            "avg_duration": sum(durations) / len(durations) if durations else 0,
            "min_duration": min(durations) if durations else 0,
            "max_duration": max(durations) if durations else 0,
            "throughput": iterations / (self.end_time - self.start_time) if self.end_time > self.start_time else 0
        }
        
        return {
            "results": results,
            "stats": stats
        }
    
    def get_summary(self) -> Dict[str, Any]:
        """获取测试摘要"""
        if not self.results:
            return {"status": "no_results"}
        
        operations = {}
        for result in self.results:
            op = result["operation"]
            if op not in operations:
                operations[op] = []
            operations[op].append(result["duration"])
        
        summary = {
            "total_operations": len(self.results),
            "total_duration": self.end_time - self.start_time if self.end_time and self.start_time else None,
            "operations": {}
        }
        
        for op, durations in operations.items():
            summary["operations"][op] = {
                "count": len(durations),
                "avg_duration": sum(durations) / len(durations),
                "min_duration": min(durations),
                "max_duration": max(durations)
            }
        
        return summary


class SecurityTestHelper:
    """安全测试助手"""
    
    def __init__(self, client: TestClient):
        self.client = client
        self.vulnerabilities = []
    
    def test_sql_injection(self, endpoint: str, params: Dict[str, str]) -> bool:
        """测试SQL注入"""
        # SQL注入测试字符串
        sql_injection_strings = [
            "' OR '1'='1",
            "\" OR \"1\"=\"1",
            "1; DROP TABLE users--",
            "1' OR '1'='1'--",
            "' UNION SELECT username, password FROM users--"
        ]
        
        for param_name, param_value in params.items():
            for injection in sql_injection_strings:
                test_params = params.copy()
                test_params[param_name] = injection
                
                response = self.client.get(endpoint, params=test_params)
                
                # 检查是否有SQL错误或异常数据返回
                if response.status_code == 500 or "SQL" in response.text or "syntax" in response.text.lower():
                    self.vulnerabilities.append({
                        "type": "sql_injection",
                        "endpoint": endpoint,
                        "parameter": param_name,
                        "payload": injection,
                        "response_code": response.status_code,
                        "response_excerpt": response.text[:200]
                    })
                    return True
        
        return False
    
    def test_xss(self, endpoint: str, params: Dict[str, str]) -> bool:
        """测试跨站脚本攻击"""
        # XSS测试字符串
        xss_strings = [
            "<script>alert('XSS')</script>",
            "<img src='x' onerror='alert(\"XSS\")' />",
            "javascript:alert('XSS')",
            "<svg/onload=alert('XSS')>"
        ]
        
        for param_name, param_value in params.items():
            for xss in xss_strings:
                test_params = params.copy()
                test_params[param_name] = xss
                
                response = self.client.get(endpoint, params=test_params)
                
                # 检查响应中是否包含未转义的XSS载荷
                if xss in response.text and "&lt;script&gt;" not in response.text:
                    self.vulnerabilities.append({
                        "type": "xss",
                        "endpoint": endpoint,
                        "parameter": param_name,
                        "payload": xss,
                        "response_code": response.status_code,
                        "response_excerpt": response.text[:200]
                    })
                    return True
        
        return False
    
    def test_open_redirect(self, endpoint: str, redirect_param: str) -> bool:
        """测试开放重定向"""
        # 重定向测试URL
        redirect_urls = [
            "https://evil.com",
            "//evil.com",
            "/\\evil.com",
            "javascript:alert('Open Redirect')"
        ]
        
        for redirect_url in redirect_urls:
            params = {redirect_param: redirect_url}
            response = self.client.get(endpoint, params=params, allow_redirects=False)
            
            # 检查是否有重定向到外部域名
            if response.status_code in (301, 302, 303, 307, 308):
                location = response.headers.get("location", "")
                if redirect_url in location or "evil.com" in location:
                    self.vulnerabilities.append({
                        "type": "open_redirect",
                        "endpoint": endpoint,
                        "parameter": redirect_param,
                        "payload": redirect_url,
                        "response_code": response.status_code,
                        "location": location
                    })
                    return True
        
        return False
    
    def test_insecure_headers(self, endpoint: str) -> Dict[str, bool]:
        """测试不安全的HTTP头"""
        response = self.client.get(endpoint)
        headers = response.headers
        
        security_headers = {
            "X-XSS-Protection": False,
            "X-Content-Type-Options": False,
            "X-Frame-Options": False,
            "Content-Security-Policy": False,
            "Strict-Transport-Security": False
        }
        
        for header in security_headers.keys():
            if header in headers:
                security_headers[header] = True
        
        missing_headers = [h for h, present in security_headers.items() if not present]
        if missing_headers:
            self.vulnerabilities.append({
                "type": "missing_security_headers",
                "endpoint": endpoint,
                "missing_headers": missing_headers
            })
        
        return security_headers
    
    def test_csrf(self, endpoint: str, method: str = "post", data: Dict[str, Any] = None) -> bool:
        """测试CSRF保护"""
        # 发送不带CSRF令牌的请求
        if method.lower() == "post":
            response = self.client.post(endpoint, json=data or {})
        elif method.lower() == "put":
            response = self.client.put(endpoint, json=data or {})
        elif method.lower() == "delete":
            response = self.client.delete(endpoint)
        else:
            return False
        
        # 检查是否成功处理了请求（应该被CSRF保护拒绝）
        if response.status_code not in (403, 401, 400) and "csrf" not in response.text.lower():
            self.vulnerabilities.append({
                "type": "csrf_vulnerability",
                "endpoint": endpoint,
                "method": method,
                "response_code": response.status_code
            })
            return True
        
        return False
    
    def get_vulnerabilities(self) -> List[Dict[str, Any]]:
        """获取发现的漏洞"""
        return self.vulnerabilities
    
    def generate_report(self) -> Dict[str, Any]:
        """生成安全测试报告"""
        vulnerability_types = {}
        for vuln in self.vulnerabilities:
            vuln_type = vuln["type"]
            if vuln_type not in vulnerability_types:
                vulnerability_types[vuln_type] = 0
            vulnerability_types[vuln_type] += 1
        
        return {
            "total_vulnerabilities": len(self.vulnerabilities),
            "vulnerability_types": vulnerability_types,
            "vulnerabilities": self.vulnerabilities,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }


class IntegrationTestHelper:
    """集成测试助手"""
    
    def __init__(self, client: TestClient):
        self.client = client
        self.auth_token = None
        self.created_resources = []
    
    async def login(self, username: str, password: str) -> bool:
        """登录并获取认证令牌"""
        response = self.client.post(
            "/api/auth/login",
            json={"username": username, "password": password}
        )
        
        if response.status_code == 200:
            data = response.json()
            if "access_token" in data:
                self.auth_token = data["access_token"]
                return True
        
        return False
    
    def get_auth_headers(self) -> Dict[str, str]:
        """获取认证头"""
        if not self.auth_token:
            return {}
        
        return {"Authorization": f"Bearer {self.auth_token}"}
    
    def create_resource(self, endpoint: str, data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """创建资源"""
        response = self.client.post(
            endpoint,
            json=data,
            headers=self.get_auth_headers()
        )
        
        if response.status_code in (200, 201):
            resource = response.json()
            self.created_resources.append({
                "endpoint": endpoint,
                "id": resource.get("id"),
                "resource": resource
            })
            return resource
        
        return None
    
    def get_resource(self, endpoint: str, resource_id: Union[int, str]) -> Optional[Dict[str, Any]]:
        """获取资源"""
        response = self.client.get(
            f"{endpoint}/{resource_id}",
            headers=self.get_auth_headers()
        )
        
        if response.status_code == 200:
            return response.json()
        
        return None
    
    def update_resource(self, endpoint: str, resource_id: Union[int, str], data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """更新资源"""
        response = self.client.put(
            f"{endpoint}/{resource_id}",
            json=data,
            headers=self.get_auth_headers()
        )
        
        if response.status_code == 200:
            return response.json()
        
        return None
    
    def delete_resource(self, endpoint: str, resource_id: Union[int, str]) -> bool:
        """删除资源"""
        response = self.client.delete(
            f"{endpoint}/{resource_id}",
            headers=self.get_auth_headers()
        )
        
        return response.status_code in (200, 204)
    
    async def cleanup_resources(self):
        """清理创建的资源"""
        # 按创建的相反顺序删除资源
        for resource in reversed(self.created_resources):
            endpoint = resource["endpoint"]
            resource_id = resource["id"]
            
            if resource_id:
                self.delete_resource(endpoint, resource_id)
        
        self.created_resources.clear()
    
    async def run_workflow(self, steps: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """运行工作流"""
        results = []
        
        for step in steps:
            step_type = step.get("type")
            endpoint = step.get("endpoint")
            data = step.get("data", {})
            resource_id = step.get("resource_id")
            expected_status = step.get("expected_status", 200)
            
            if step_type == "create":
                response = self.client.post(
                    endpoint,
                    json=data,
                    headers=self.get_auth_headers()
                )
            elif step_type == "get":
                response = self.client.get(
                    f"{endpoint}/{resource_id}" if resource_id else endpoint,
                    headers=self.get_auth_headers()
                )
            elif step_type == "update":
                response = self.client.put(
                    f"{endpoint}/{resource_id}",
                    json=data,
                    headers=self.get_auth_headers()
                )
            elif step_type == "delete":
                response = self.client.delete(
                    f"{endpoint}/{resource_id}",
                    headers=self.get_auth_headers()
                )
            else:
                continue
            
            result = {
                "step": step,
                "status_code": response.status_code,
                "success": response.status_code == expected_status
            }
            
            try:
                result["data"] = response.json()
            except:
                result["data"] = None
            
            results.append(result)
            
            # 如果步骤失败，停止工作流
            if not result["success"]:
                break
        
        return results