"""测试辅助工具函数"""

import asyncio
import logging
import shutil
import tempfile
import time
import uuid
from contextlib import asynccontextmanager, contextmanager
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, List, Any, Optional, Callable
from unittest.mock import Mock, patch, AsyncMock

from fastapi.testclient import TestClient
from sqlalchemy import select, delete
from sqlalchemy.ext.asyncio import AsyncSession

from database.models import (
    Session, Conversation, APIKey, Prompt
)
from tests.fixtures.sample_data import (
    SAMPLE_SESSION, SAMPLE_CONVERSATION
)


class AsyncTestCase:
    """异步测试基类"""
    
    def setup_method(self):
        """测试方法设置"""
        self.loop = None
        self.cleanup_tasks = []
    
    async def async_setup(self):
        """异步设置"""
        self.loop = asyncio.get_event_loop()
    
    async def async_teardown(self):
        """异步清理"""
        # 执行清理任务
        for task in reversed(self.cleanup_tasks):
            try:
                if asyncio.iscoroutinefunction(task):
                    await task()
                else:
                    task()
            except Exception as e:
                logging.warning(f"清理任务失败: {e}")
        
        self.cleanup_tasks.clear()
    
    def add_cleanup(self, cleanup_func: Callable):
        """添加清理任务"""
        self.cleanup_tasks.append(cleanup_func)
    
    async def run_with_timeout(self, coro, timeout: float = 10.0):
        """运行协程并设置超时"""
        return await asyncio.wait_for(coro, timeout=timeout)


class DatabaseTestCase(AsyncTestCase):
    """数据库测试基类"""
    
    def setup_method(self, db_session: AsyncSession = None):
        """测试方法设置"""
        super().setup_method()
        self.db_session = db_session
        self.created_objects = []
    
    async def async_setup(self):
        """数据库测试设置"""
        await super().async_setup()
        # 开始事务
        await self.db_session.begin()
    
    async def async_teardown(self):
        """数据库测试清理"""
        # 清理创建的对象
        for obj in reversed(self.created_objects):
            try:
                await self.db_session.delete(obj)
            except Exception as e:
                logging.warning(f"删除对象失败: {e}")
        
        # 回滚事务
        await self.db_session.rollback()
        await super().async_teardown()
    
    def track_object(self, obj):
        """跟踪创建的对象"""
        self.created_objects.append(obj)
        return obj
    
    async def create_and_save(self, model_class, **kwargs):
        """创建并保存对象"""
        obj = model_class(**kwargs)
        self.db_session.add(obj)
        await self.db_session.flush()
        await self.db_session.refresh(obj)
        self.track_object(obj)
        return obj


class APITestCase(AsyncTestCase):
    """API测试基类"""
    
    def setup_method(self, client: TestClient = None):
        """测试方法设置"""
        super().setup_method()
        self.client = client
        self.auth_headers = {}
    
    def set_auth_headers(self, headers: Dict[str, str]):
        """设置认证头"""
        self.auth_headers = headers
    
    def get(self, url: str, **kwargs):
        """GET请求"""
        headers = kwargs.pop('headers', {})
        headers.update(self.auth_headers)
        return self.client.get(url, headers=headers, **kwargs)
    
    def post(self, url: str, **kwargs):
        """POST请求"""
        headers = kwargs.pop('headers', {})
        headers.update(self.auth_headers)
        return self.client.post(url, headers=headers, **kwargs)
    
    def put(self, url: str, **kwargs):
        """PUT请求"""
        headers = kwargs.pop('headers', {})
        headers.update(self.auth_headers)
        return self.client.put(url, headers=headers, **kwargs)
    
    def delete(self, url: str, **kwargs):
        """DELETE请求"""
        headers = kwargs.pop('headers', {})
        headers.update(self.auth_headers)
        return self.client.delete(url, headers=headers, **kwargs)


class MockTestCase(AsyncTestCase):
    """模拟测试基类"""
    
    def setup_method(self):
        """测试方法设置"""
        super().setup_method()
        self.patches = []
        self.mocks = {}
    
    def mock_object(self, target: str, **kwargs) -> Mock:
        """模拟对象"""
        mock = Mock(**kwargs)
        patcher = patch(target, mock)
        self.patches.append(patcher)
        patcher.start()
        self.mocks[target] = mock
        return mock
    
    def mock_async_object(self, target: str, **kwargs) -> AsyncMock:
        """模拟异步对象"""
        mock = AsyncMock(**kwargs)
        patcher = patch(target, mock)
        self.patches.append(patcher)
        patcher.start()
        self.mocks[target] = mock
        return mock
    
    async def async_teardown(self):
        """清理模拟"""
        for patcher in reversed(self.patches):
            patcher.stop()
        self.patches.clear()
        self.mocks.clear()
        await super().async_teardown()


# 数据创建工具
# 注意：项目中暂时没有User模型，跳过用户相关功能


async def create_test_session(
    db_session: AsyncSession,
    **kwargs
) -> Session:
    """创建测试会话"""
    session_data = SAMPLE_SESSION.copy()
    session_data.update(kwargs)
    
    session = Session(**session_data)
    db_session.add(session)
    await db_session.flush()
    await db_session.refresh(session)
    return session


async def create_test_conversation(
    db_session: AsyncSession,
    session_id: Optional[int] = None,
    **kwargs
) -> Conversation:
    """创建测试对话"""
    if session_id is None:
        session = await create_test_session(db_session)
        session_id = session.id
    
    conversation_data = SAMPLE_CONVERSATION.copy()
    conversation_data.update(kwargs)
    conversation_data['session_id'] = session_id
    
    conversation = Conversation(**conversation_data)
    db_session.add(conversation)
    await db_session.flush()
    await db_session.refresh(conversation)
    return conversation


# Message和Model相关函数暂时移除，因为数据库模型中没有这些类


async def create_test_api_key(
    db_session: AsyncSession,
    **kwargs
) -> APIKey:
    """创建测试API密钥"""
    # 创建基础API密钥数据
    api_key_data = {
        "key": "test_api_key_123",
        "name": "测试API密钥",
        "description": "用于测试的API密钥",
        "is_active": True,
        "permissions": ["read", "write"]
    }
    api_key_data.update(kwargs)
    
    api_key = APIKey(**api_key_data)
    db_session.add(api_key)
    await db_session.flush()
    await db_session.refresh(api_key)
    return api_key


# 清理工具
async def cleanup_test_data(db_session: AsyncSession):
    """清理测试数据"""
    # 按依赖关系顺序删除
    tables = [
        Conversation, Session, APIKey, Prompt
    ]
    
    for table in tables:
        await db_session.execute(delete(table))
    
    await db_session.commit()


# 断言工具
def assert_response_format(response: Dict[str, Any], expected_fields: List[str]):
    """断言响应格式"""
    for field in expected_fields:
        assert field in response, f"响应中缺少字段: {field}"


def assert_database_state(
    db_session: AsyncSession,
    model_class,
    expected_count: int,
    filters: Optional[Dict[str, Any]] = None
):
    """断言数据库状态"""
    query = select(model_class)
    if filters:
        for key, value in filters.items():
            query = query.where(getattr(model_class, key) == value)
    
    result = db_session.execute(query)
    actual_count = len(result.scalars().all())
    assert actual_count == expected_count, f"期望{expected_count}条记录，实际{actual_count}条"


# 等待工具
async def wait_for_condition(
    condition: Callable[[], bool],
    timeout: float = 10.0,
    interval: float = 0.1
) -> bool:
    """等待条件满足"""
    start_time = time.time()
    while time.time() - start_time < timeout:
        if condition():
            return True
        await asyncio.sleep(interval)
    return False


# 日志捕获
@contextmanager
def capture_logs(logger_name: str = None, level: int = logging.INFO):
    """捕获日志"""
    import io
    
    logger = logging.getLogger(logger_name)
    original_level = logger.level
    logger.setLevel(level)
    
    log_capture = io.StringIO()
    handler = logging.StreamHandler(log_capture)
    handler.setLevel(level)
    
    logger.addHandler(handler)
    
    try:
        yield log_capture
    finally:
        logger.removeHandler(handler)
        logger.setLevel(original_level)


# 外部API模拟
@asynccontextmanager
async def mock_external_api(base_url: str, responses: Dict[str, Any]):
    """模拟外部API"""
    from aioresponses import aioresponses
    
    with aioresponses() as m:
        for endpoint, response in responses.items():
            url = f"{base_url.rstrip('/')}/{endpoint.lstrip('/')}"
            if isinstance(response, dict) and 'status' in response:
                m.get(url, payload=response.get('data', {}), status=response['status'])
                m.post(url, payload=response.get('data', {}), status=response['status'])
            else:
                m.get(url, payload=response)
                m.post(url, payload=response)
        
        yield m


# 测试数据生成
def generate_test_data(data_type: str, count: int = 1, **kwargs) -> List[Dict[str, Any]]:
    """生成测试数据"""
    data_generators = {
        'user': lambda: {
            'username': f'user_{uuid.uuid4().hex[:8]}',
            'email': f'test_{uuid.uuid4().hex[:8]}@example.com',
            'password_hash': 'hashed_password',
            'role': 'user',
            'is_active': True
        },
        'session': lambda: {
            'name': f'Session {uuid.uuid4().hex[:8]}',
            'description': 'Test session',
            'models': ['gpt-3.5-turbo', 'gpt-4'],
            'parameters': {'temperature': 0.7}
        },
        'conversation': lambda: {
            'title': f'Conversation {uuid.uuid4().hex[:8]}',
            'status': 'active'
        },
        'message': lambda: {
            'content': f'Test message {uuid.uuid4().hex[:8]}',
            'role': 'user',
            'timestamp': datetime.now(timezone.utc)
        }
    }
    
    if data_type not in data_generators:
        raise ValueError(f"不支持的数据类型: {data_type}")
    
    generator = data_generators[data_type]
    data_list = []
    
    for _ in range(count):
        data = generator()
        data.update(kwargs)
        data_list.append(data)
    
    return data_list


# 数据验证
def validate_test_data(data: Dict[str, Any], schema: Dict[str, type]) -> bool:
    """验证测试数据"""
    for field, expected_type in schema.items():
        if field not in data:
            return False
        if not isinstance(data[field], expected_type):
            return False
    return True


# 临时文件管理
@contextmanager
def temporary_directory():
    """创建临时目录"""
    temp_dir = tempfile.mkdtemp()
    try:
        yield Path(temp_dir)
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)


@contextmanager
def temporary_file(suffix: str = '.tmp', content: str = None):
    """创建临时文件"""
    with tempfile.NamedTemporaryFile(mode='w', suffix=suffix, delete=False) as f:
        if content:
            f.write(content)
        temp_path = Path(f.name)
    
    try:
        yield temp_path
    finally:
        if temp_path.exists():
            temp_path.unlink()


# 性能测试工具
class PerformanceTimer:
    """性能计时器"""
    
    def __init__(self):
        self.start_time = None
        self.end_time = None
    
    def __enter__(self):
        self.start_time = time.perf_counter()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = time.perf_counter()
    
    @property
    def elapsed(self) -> float:
        """获取耗时（秒）"""
        if self.start_time is None or self.end_time is None:
            return 0.0
        return self.end_time - self.start_time


# 内存使用监控
class MemoryMonitor:
    """内存使用监控"""
    
    def __init__(self):
        import psutil
        self.process = psutil.Process()
        self.start_memory = None
        self.peak_memory = None
    
    def __enter__(self):
        self.start_memory = self.process.memory_info().rss
        self.peak_memory = self.start_memory
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        pass
    
    def update_peak(self):
        """更新峰值内存"""
        current_memory = self.process.memory_info().rss
        if current_memory > self.peak_memory:
            self.peak_memory = current_memory
    
    @property
    def memory_usage(self) -> Dict[str, int]:
        """获取内存使用情况"""
        current_memory = self.process.memory_info().rss
        return {
            'start': self.start_memory,
            'current': current_memory,
            'peak': self.peak_memory,
            'increase': current_memory - self.start_memory
        }


# 并发测试工具
async def run_concurrent_tasks(
    tasks: List[Callable],
    max_concurrency: int = 10
) -> List[Any]:
    """并发运行任务"""
    semaphore = asyncio.Semaphore(max_concurrency)
    
    async def run_task(task):
        async with semaphore:
            if asyncio.iscoroutinefunction(task):
                return await task()
            else:
                return task()
        return None

    return await asyncio.gather(*[run_task(task) for task in tasks])


# 重试工具
async def retry_async(
    func: Callable,
    max_attempts: int = 3,
    delay: float = 1.0,
    backoff: float = 2.0
) -> Any:
    """异步重试"""
    last_exception = None
    
    for attempt in range(max_attempts):
        try:
            if asyncio.iscoroutinefunction(func):
                return await func()
            else:
                return func()
        except Exception as e:
            last_exception = e
            if attempt < max_attempts - 1:
                await asyncio.sleep(delay * (backoff ** attempt))
    
    raise last_exception


# 环境变量管理
@contextmanager
def environment_variables(**env_vars):
    """临时设置环境变量"""
    import os
    
    original_values = {}
    for key, value in env_vars.items():
        original_values[key] = os.environ.get(key)
        os.environ[key] = str(value)
    
    try:
        yield
    finally:
        for key, original_value in original_values.items():
            if original_value is None:
                os.environ.pop(key, None)
            else:
                os.environ[key] = original_value