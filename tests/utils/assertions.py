"""测试断言工具"""

import json
import re
from datetime import datetime
from typing import Dict, List, Any, Optional

import pytest
from fastapi import status
from fastapi.testclient import TestClient
from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession


def assert_model_response(response: Dict[str, Any], expected_fields: List[str] = None):
    """断言模型响应格式"""
    # 检查基本结构
    assert isinstance(response, dict), "响应必须是字典类型"
    
    # 检查预期字段
    if expected_fields:
        for field in expected_fields:
            assert field in response, f"响应中缺少字段: {field}"
    
    # 检查常见字段
    if 'model' in response:
        assert isinstance(response['model'], str), "model字段必须是字符串"
    
    # 检查OpenAI格式
    if 'choices' in response:
        assert isinstance(response['choices'], list), "choices字段必须是列表"
        if response['choices']:
            assert 'message' in response['choices'][0] or 'delta' in response['choices'][0], \
                "choices中的项必须包含message或delta字段"
    
    # 检查Anthropic格式
    if 'content' in response:
        if isinstance(response['content'], list):
            for item in response['content']:
                assert 'type' in item, "content列表中的项必须包含type字段"
                if item['type'] == 'text':
                    assert 'text' in item, "text类型的内容必须包含text字段"
    
    # 检查使用量信息
    if 'usage' in response:
        usage = response['usage']
        assert isinstance(usage, dict), "usage字段必须是字典类型"
        
        # OpenAI格式
        if all(k in usage for k in ['prompt_tokens', 'completion_tokens', 'total_tokens']):
            assert usage['total_tokens'] == usage['prompt_tokens'] + usage['completion_tokens'], \
                "total_tokens应等于prompt_tokens和completion_tokens之和"
        
        # Anthropic格式
        if all(k in usage for k in ['input_tokens', 'output_tokens', 'total_tokens']):
            assert usage['total_tokens'] == usage['input_tokens'] + usage['output_tokens'], \
                "total_tokens应等于input_tokens和output_tokens之和"


def assert_model_response_format(response: Dict[str, Any], expected_fields: List[str] = None):
    """断言模型响应格式（别名函数）"""
    return assert_model_response(response, expected_fields)


def assert_comparison_result(result: Dict[str, Any]):
    """断言比较结果格式"""
    # 检查基本结构
    assert isinstance(result, dict), "比较结果必须是字典类型"
    
    # 检查必要字段
    required_fields = ['comparison_id', 'prompt', 'models', 'responses', 'analysis']
    for field in required_fields:
        assert field in result, f"比较结果中缺少字段: {field}"
    
    # 检查模型和响应
    assert isinstance(result['models'], list), "models字段必须是列表"
    assert isinstance(result['responses'], list), "responses字段必须是列表"
    assert len(result['responses']) == len(result['models']), "响应数量必须与模型数量相同"
    
    # 检查每个响应
    for response in result['responses']:
        assert 'model' in response, "每个响应必须包含model字段"
        assert 'content' in response, "每个响应必须包含content字段"
        assert 'response_time' in response, "每个响应必须包含response_time字段"
    
    # 检查分析结果
    analysis = result['analysis']
    assert 'ranking' in analysis, "分析结果必须包含ranking字段"
    assert 'criteria_scores' in analysis, "分析结果必须包含criteria_scores字段"
    
    # 检查排名
    assert len(analysis['ranking']) == len(result['models']), "排名数量必须与模型数量相同"
    for model in result['models']:
        assert model in analysis['ranking'], f"模型{model}必须在排名中"
    
    # 检查评分
    for model in result['models']:
        assert model in analysis['criteria_scores'], f"模型{model}必须在评分中"
        scores = analysis['criteria_scores'][model]
        assert 'overall' in scores, f"模型{model}的评分必须包含overall字段"


async def assert_database_consistency(db_session: AsyncSession):
    """断言数据库一致性"""
    from database.models import Session, Conversation

    # 检查会话-对话关系
    result = await db_session.execute(
        select(func.count(Conversation.id)).where(~Conversation.session_id.in_(
            select(Session.id)
        ))
    )
    orphaned_conversations = result.scalar()
    assert orphaned_conversations == 0, f"存在{orphaned_conversations}个没有关联会话的对话"
    
    # 检查会话状态一致性
    result = await db_session.execute(
        select(func.count(Session.id)).where(Session.status.notin_(['active', 'inactive', 'expired']))
    )
    invalid_sessions = result.scalar()
    assert invalid_sessions == 0, f"存在{invalid_sessions}个状态无效的会话"
    
    # 检查对话状态一致性
    result = await db_session.execute(
        select(func.count(Conversation.id)).where(Conversation.status.notin_(['success', 'error', 'timeout']))
    )
    invalid_conversations = result.scalar()
    assert invalid_conversations == 0, f"存在{invalid_conversations}个状态无效的对话"


def assert_api_response(
    response,
    expected_status_code: int = status.HTTP_200_OK,
    expected_fields: List[str] = None,
    expected_content_type: str = "application/json"
):
    """断言API响应"""
    # 检查状态码
    assert response.status_code == expected_status_code, \
        f"期望状态码{expected_status_code}，实际{response.status_code}"
    
    # 检查内容类型
    if expected_content_type:
        content_type = response.headers.get("content-type", "")
        assert expected_content_type in content_type, \
            f"期望内容类型{expected_content_type}，实际{content_type}"
    
    # 对于JSON响应，检查字段
    if expected_content_type == "application/json" and expected_fields:
        try:
            data = response.json()
            for field in expected_fields:
                assert field in data, f"响应中缺少字段: {field}"
        except json.JSONDecodeError:
            pytest.fail("响应不是有效的JSON")


def assert_performance_metrics(
    response_time: float,
    max_response_time: float,
    token_count: Optional[int] = None,
    max_token_count: Optional[int] = None,
    memory_usage: Optional[Dict[str, int]] = None,
    max_memory_increase: Optional[int] = None
):
    """断言性能指标"""
    # 检查响应时间
    assert response_time <= max_response_time, \
        f"响应时间{response_time}秒超过最大允许值{max_response_time}秒"
    
    # 检查令牌数量
    if token_count is not None and max_token_count is not None:
        assert token_count <= max_token_count, \
            f"令牌数量{token_count}超过最大允许值{max_token_count}"
    
    # 检查内存使用
    if memory_usage is not None and max_memory_increase is not None:
        memory_increase = memory_usage['current'] - memory_usage['start']
        assert memory_increase <= max_memory_increase, \
            f"内存增加{memory_increase}字节超过最大允许值{max_memory_increase}字节"


def assert_security_compliance(
    response,
    sensitive_patterns: List[str] = None,
    required_headers: List[str] = None,
    forbidden_headers: List[str] = None
):
    """断言安全合规性"""
    # 检查敏感信息泄露
    if sensitive_patterns:
        response_text = response.text if hasattr(response, 'text') else str(response)
        for pattern in sensitive_patterns:
            matches = re.findall(pattern, response_text)
            assert not matches, f"响应中包含敏感信息: {pattern}"
    
    # 检查必要的安全头
    if required_headers and hasattr(response, 'headers'):
        for header in required_headers:
            assert header.lower() in [h.lower() for h in response.headers], \
                f"响应缺少必要的安全头: {header}"
    
    # 检查禁止的头
    if forbidden_headers and hasattr(response, 'headers'):
        for header in forbidden_headers:
            assert header.lower() not in [h.lower() for h in response.headers], \
                f"响应包含禁止的头: {header}"


def assert_conversation_equal(conv1: Dict[str, Any], conv2: Dict[str, Any]):
    """断言两个对话对象相等"""
    assert conv1['user_message'] == conv2['user_message'], "用户消息不匹配"
    assert conv1['model_name'] == conv2['model_name'], "模型名称不匹配"
    assert conv1['model_response'] == conv2['model_response'], "模型响应不匹配"
    assert conv1['session_id'] == conv2['session_id'], "会话ID不匹配"


def assert_message_equal(msg1: Dict[str, Any], msg2: Dict[str, Any]):
    """断言两个消息对象相等（兼容性函数）"""
    # 由于Message模型已移除，这里提供基本的字典比较
    assert msg1.get('content') == msg2.get('content'), "消息内容不匹配"
    assert msg1.get('role') == msg2.get('role'), "消息角色不匹配"


def assert_user_equal(user1: Dict[str, Any], user2: Dict[str, Any]):
    """断言两个用户对象相等（兼容性函数）"""
    # 由于User模型已移除，这里提供基本的字典比较
    assert user1.get('id') == user2.get('id'), "用户ID不匹配"
    assert user1.get('name') == user2.get('name'), "用户名不匹配"


def assert_error_handling(
    response,
    expected_status_code: int,
    expected_error_type: Optional[str] = None,
    expected_error_message: Optional[str] = None
):
    """断言错误处理"""
    # 检查状态码
    assert response.status_code == expected_status_code, \
        f"期望状态码{expected_status_code}，实际{response.status_code}"
    
    # 对于JSON响应，检查错误信息
    try:
        data = response.json()
        
        # 检查错误类型
        if expected_error_type:
            assert 'error' in data, "响应中缺少error字段"
            assert 'type' in data['error'], "error中缺少type字段"
            assert data['error']['type'] == expected_error_type, \
                f"期望错误类型{expected_error_type}，实际{data['error']['type']}"
        
        # 检查错误消息
        if expected_error_message:
            assert 'error' in data, "响应中缺少error字段"
            assert 'message' in data['error'], "error中缺少message字段"
            assert expected_error_message in data['error']['message'], \
                f"期望错误消息包含'{expected_error_message}'，实际'{data['error']['message']}'"
    
    except json.JSONDecodeError:
        # 对于非JSON响应，只检查状态码
        if expected_error_type or expected_error_message:
            pytest.fail("响应不是有效的JSON，无法检查错误类型和消息")


def assert_pagination_response(
    response,
    page: int,
    per_page: int,
    total: Optional[int] = None,
    items_field: str = "items"
):
    """断言分页响应"""
    # 检查状态码
    assert response.status_code == status.HTTP_200_OK, \
        f"期望状态码200，实际{response.status_code}"
    
    # 检查分页字段
    data = response.json()
    assert "page" in data, "响应中缺少page字段"
    assert "per_page" in data, "响应中缺少per_page字段"
    assert "total" in data, "响应中缺少total字段"
    assert items_field in data, f"响应中缺少{items_field}字段"
    
    # 检查分页值
    assert data["page"] == page, f"期望页码{page}，实际{data['page']}"
    assert data["per_page"] == per_page, f"期望每页数量{per_page}，实际{data['per_page']}"
    
    # 检查总数
    if total is not None:
        assert data["total"] == total, f"期望总数{total}，实际{data['total']}"
    
    # 检查项目数量
    items = data[items_field]
    assert isinstance(items, list), f"{items_field}字段必须是列表"
    assert len(items) <= per_page, f"项目数量{len(items)}超过每页数量{per_page}"


def assert_webhook_request(
    request_data: Dict[str, Any],
    expected_event_type: str,
    expected_fields: List[str] = None
):
    """断言Webhook请求"""
    # 检查事件类型
    assert "event_type" in request_data, "请求中缺少event_type字段"
    assert request_data["event_type"] == expected_event_type, \
        f"期望事件类型{expected_event_type}，实际{request_data['event_type']}"
    
    # 检查数据字段
    assert "data" in request_data, "请求中缺少data字段"
    data = request_data["data"]
    
    # 检查预期字段
    if expected_fields:
        for field in expected_fields:
            assert field in data, f"data中缺少字段: {field}"
    
    # 检查时间戳
    assert "timestamp" in request_data, "请求中缺少timestamp字段"
    timestamp = request_data["timestamp"]
    try:
        datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
    except (ValueError, TypeError):
        pytest.fail(f"无效的时间戳格式: {timestamp}")


def assert_file_content(
    file_path: str,
    expected_content: Optional[str] = None,
    expected_pattern: Optional[str] = None,
    expected_line_count: Optional[int] = None
):
    """断言文件内容"""
    import os
    
    # 检查文件存在
    assert os.path.exists(file_path), f"文件不存在: {file_path}"
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
        lines = content.splitlines()
    
    # 检查内容
    if expected_content is not None:
        assert content == expected_content, "文件内容与预期不符"
    
    # 检查模式
    if expected_pattern is not None:
        assert re.search(expected_pattern, content), f"文件内容不匹配模式: {expected_pattern}"
    
    # 检查行数
    if expected_line_count is not None:
        assert len(lines) == expected_line_count, \
            f"期望{expected_line_count}行，实际{len(lines)}行"


def assert_json_schema(data: Dict[str, Any], schema: Dict[str, Any]):
    """断言JSON模式"""
    try:
        import jsonschema
        jsonschema.validate(instance=data, schema=schema)
    except ImportError:
        # 如果jsonschema库不可用，进行简单检查
        _validate_schema(data, schema)


def _validate_schema(data: Dict[str, Any], schema: Dict[str, Any], path: str = ""):
    """简单的JSON模式验证"""
    if "type" in schema:
        schema_type = schema["type"]
        
        # 检查类型
        if schema_type == "object":
            assert isinstance(data, dict), f"{path}必须是对象"
            
            # 检查必要属性
            if "required" in schema:
                for prop in schema["required"]:
                    assert prop in data, f"{path}缺少必要属性: {prop}"
            
            # 检查属性
            if "properties" in schema:
                for prop, prop_schema in schema["properties"].items():
                    if prop in data:
                        new_path = f"{path}.{prop}" if path else prop
                        _validate_schema(data[prop], prop_schema, new_path)
        
        elif schema_type == "array":
            assert isinstance(data, list), f"{path}必须是数组"
            
            # 检查项目
            if "items" in schema and data:
                for i, item in enumerate(data):
                    new_path = f"{path}[{i}]"
                    _validate_schema(item, schema["items"], new_path)
        
        elif schema_type == "string":
            assert isinstance(data, str), f"{path}必须是字符串"
            
            # 检查格式
            if "format" in schema and schema["format"] == "date-time":
                try:
                    datetime.fromisoformat(data.replace('Z', '+00:00'))
                except (ValueError, TypeError):
                    pytest.fail(f"{path}不是有效的日期时间格式: {data}")
        
        elif schema_type == "number" or schema_type == "integer":
            assert isinstance(data, (int, float)), f"{path}必须是数字"
            if schema_type == "integer":
                assert isinstance(data, int) or data.is_integer(), f"{path}必须是整数"
        
        elif schema_type == "boolean":
            assert isinstance(data, bool), f"{path}必须是布尔值"
        
        elif schema_type == "null":
            assert data is None, f"{path}必须是null"


def assert_api_documentation(client: TestClient, endpoint: str):
    """断言API文档"""
    # 检查OpenAPI文档
    response = client.get("/openapi.json")
    assert response.status_code == status.HTTP_200_OK, "无法获取OpenAPI文档"
    
    openapi = response.json()
    assert "paths" in openapi, "OpenAPI文档缺少paths字段"
    
    # 检查端点是否在文档中
    paths = openapi["paths"]
    endpoint_found = False
    
    for path, methods in paths.items():
        if path == endpoint or path.rstrip("/") == endpoint.rstrip("/"):
            endpoint_found = True
            break
    
    assert endpoint_found, f"端点{endpoint}未在API文档中定义"


def assert_cache_behavior(
    first_response,
    second_response,
    should_be_cached: bool = True
):
    """断言缓存行为"""
    # 检查状态码
    assert first_response.status_code == status.HTTP_200_OK, \
        f"第一个响应状态码应为200，实际{first_response.status_code}"
    assert second_response.status_code == status.HTTP_200_OK, \
        f"第二个响应状态码应为200，实际{second_response.status_code}"
    
    # 检查缓存头
    if should_be_cached:
        # 检查缓存命中头
        assert "X-Cache" in second_response.headers, "第二个响应缺少X-Cache头"
        assert second_response.headers["X-Cache"] == "HIT", \
            f"期望缓存命中，实际{second_response.headers.get('X-Cache')}"
    else:
        # 检查缓存未命中
        if "X-Cache" in second_response.headers:
            assert second_response.headers["X-Cache"] != "HIT", "不应该缓存命中"


def assert_rate_limit_behavior(
    responses: List[Any],
    rate_limit_header: str = "X-RateLimit-Remaining",
    rate_limit_reset_header: str = "X-RateLimit-Reset"
):
    """断言速率限制行为"""
    # 检查速率限制头
    for response in responses:
        assert rate_limit_header in response.headers, f"响应缺少{rate_limit_header}头"
        assert rate_limit_reset_header in response.headers, f"响应缺少{rate_limit_reset_header}头"
    
    # 检查剩余请求数递减
    remaining_values = [int(r.headers[rate_limit_header]) for r in responses]
    for i in range(1, len(remaining_values)):
        assert remaining_values[i] <= remaining_values[i-1], \
            f"剩余请求数应递减，但从{remaining_values[i-1]}增加到{remaining_values[i]}"
    
    # 检查是否达到限制
    if 0 in remaining_values:
        limit_reached_index = remaining_values.index(0)
        if limit_reached_index < len(responses) - 1:
            # 检查后续请求是否返回429
            for response in responses[limit_reached_index+1:]:
                assert response.status_code == status.HTTP_429_TOO_MANY_REQUESTS, \
                    f"达到速率限制后应返回429，实际{response.status_code}"


def assert_websocket_message(
    message: Dict[str, Any],
    expected_type: str,
    expected_fields: List[str] = None
):
    """断言WebSocket消息"""
    # 检查消息类型
    assert "type" in message, "消息缺少type字段"
    assert message["type"] == expected_type, \
        f"期望消息类型{expected_type}，实际{message['type']}"
    
    # 检查预期字段
    if expected_fields:
        for field in expected_fields:
            assert field in message, f"消息缺少字段: {field}"


def assert_streaming_response(
    chunks: List[Dict[str, Any]],
    expected_model: Optional[str] = None
):
    """断言流式响应"""
    # 检查至少有一个块
    assert len(chunks) > 0, "流式响应应至少包含一个块"
    
    # 检查第一个块
    first_chunk = chunks[0]
    assert "id" in first_chunk, "第一个块缺少id字段"
    assert "choices" in first_chunk, "第一个块缺少choices字段"
    assert len(first_chunk["choices"]) > 0, "第一个块的choices应至少包含一项"
    
    # 检查模型
    if expected_model and "model" in first_chunk:
        assert first_chunk["model"] == expected_model, \
            f"期望模型{expected_model}，实际{first_chunk['model']}"
    
    # 检查最后一个块
    last_chunk = chunks[-1]
    assert "choices" in last_chunk, "最后一个块缺少choices字段"
    assert len(last_chunk["choices"]) > 0, "最后一个块的choices应至少包含一项"
    assert last_chunk["choices"][0]["finish_reason"] is not None, \
        "最后一个块应包含finish_reason"


def assert_concurrent_requests(
    results: List[Any],
    expected_success_rate: float = 1.0,
    max_response_time: Optional[float] = None
):
    """断言并发请求"""
    # 检查成功率
    success_count = sum(1 for r in results if getattr(r, "status_code", 500) < 500)
    actual_success_rate = success_count / len(results)
    
    assert actual_success_rate >= expected_success_rate, \
        f"期望成功率至少{expected_success_rate}，实际{actual_success_rate}"
    
    # 检查响应时间
    if max_response_time is not None and hasattr(results[0], "elapsed"):
        response_times = [r.elapsed.total_seconds() for r in results]
        max_actual_time = max(response_times)
        
        assert max_actual_time <= max_response_time, \
            f"最大响应时间{max_actual_time}秒超过允许值{max_response_time}秒"


def assert_idempotent_request(
    first_response,
    second_response,
    id_field: str = "id"
):
    """断言幂等请求"""
    # 检查状态码
    assert first_response.status_code == second_response.status_code, \
        f"两个响应的状态码应相同，实际{first_response.status_code}和{second_response.status_code}"
    
    # 对于成功响应，检查ID
    if first_response.status_code == status.HTTP_200_OK:
        first_data = first_response.json()
        second_data = second_response.json()
        
        assert id_field in first_data, f"第一个响应缺少{id_field}字段"
        assert id_field in second_data, f"第二个响应缺少{id_field}字段"
        assert first_data[id_field] == second_data[id_field], \
            f"两个响应的{id_field}应相同，实际{first_data[id_field]}和{second_data[id_field]}"