"""模拟管理器类"""

import asyncio
import uuid
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Union

from tests.fixtures.mock_responses import (
    create_mock_response,
    create_mock_comparison_response
)


class MockModelManager:
    """模拟模型管理器"""
    
    def __init__(self):
        self.models = {
            'gpt-3.5-turbo': {
                'provider': 'openai',
                'name': 'GPT-3.5 Turbo',
                'max_tokens': 4096,
                'cost_per_token': 0.002
            },
            'gpt-4': {
                'provider': 'openai',
                'name': 'GPT-4',
                'max_tokens': 8192,
                'cost_per_token': 0.03
            },
            'claude-3-sonnet': {
                'provider': 'anthropic',
                'name': 'Claude 3 Sonnet',
                'max_tokens': 4096,
                'cost_per_token': 0.015
            }
        }
        self.call_count = 0
        self.last_request = None
        self.response_delay = 0.1
        self.should_fail = False
        self.failure_rate = 0.0
    
    async def get_available_models(self) -> List[Dict[str, Any]]:
        """获取可用模型列表"""
        await asyncio.sleep(self.response_delay)
        return list(self.models.values())
    
    async def generate_response(
        self,
        model: str,
        prompt: str,
        **kwargs
    ) -> Dict[str, Any]:
        """生成模型响应"""
        self.call_count += 1
        self.last_request = {
            'model': model,
            'prompt': prompt,
            'kwargs': kwargs,
            'timestamp': datetime.now(timezone.utc)
        }
        
        # 模拟延迟
        await asyncio.sleep(self.response_delay)
        
        # 模拟失败
        if self.should_fail or (self.failure_rate > 0 and 
                               self.call_count % int(1/self.failure_rate) == 0):
            raise Exception(f"模拟{model}调用失败")
        
        # 生成响应
        if model not in self.models:
            raise ValueError(f"不支持的模型: {model}")
        
        content = f"这是来自{model}的回复：{prompt}"
        return create_mock_response(model, content, **kwargs)
    
    async def validate_api_key(self, provider: str, api_key: str) -> bool:
        """验证API密钥"""
        await asyncio.sleep(0.05)
        return api_key.startswith(f"{provider}_") and len(api_key) > 10
    
    def get_model_info(self, model: str) -> Optional[Dict[str, Any]]:
        """获取模型信息"""
        return self.models.get(model)
    
    def reset_stats(self):
        """重置统计"""
        self.call_count = 0
        self.last_request = None
    
    def set_failure_mode(self, should_fail: bool = True, failure_rate: float = 0.0):
        """设置失败模式"""
        self.should_fail = should_fail
        self.failure_rate = failure_rate
    
    def set_response_delay(self, delay: float):
        """设置响应延迟"""
        self.response_delay = delay


class MockComparisonEngine:
    """模拟比较引擎"""
    
    def __init__(self):
        self.comparisons = []
        self.response_delay = 0.2
        self.should_fail = False
        self.analysis_enabled = True
    
    async def compare_models(
        self,
        prompt: str,
        models: List[str],
        **kwargs
    ) -> Dict[str, Any]:
        """比较多个模型"""
        # 模拟延迟
        await asyncio.sleep(self.response_delay)
        
        if self.should_fail:
            raise Exception("模拟比较失败")
        
        # 生成比较结果
        comparison_result = create_mock_comparison_response(
            prompt, models, **kwargs
        )
        
        # 记录比较
        self.comparisons.append({
            'prompt': prompt,
            'models': models,
            'result': comparison_result,
            'timestamp': datetime.now(timezone.utc)
        })
        
        return comparison_result
    
    async def analyze_responses(
        self,
        responses: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """分析响应"""
        if not self.analysis_enabled:
            return {}
        
        await asyncio.sleep(0.1)
        
        # 模拟分析结果
        return {
            'best_response': responses[0] if responses else None,
            'ranking': [r.get('model', f'model_{i}') for i, r in enumerate(responses)],
            'scores': {
                r.get('model', f'model_{i}'): {
                    'accuracy': 8.0 + i * 0.5,
                    'clarity': 7.5 + i * 0.3,
                    'completeness': 8.0 + i * 0.2,
                    'overall': 7.8 + i * 0.3
                }
                for i, r in enumerate(responses)
            }
        }
    
    def get_comparison_history(self) -> List[Dict[str, Any]]:
        """获取比较历史"""
        return self.comparisons.copy()
    
    def clear_history(self):
        """清空历史"""
        self.comparisons.clear()
    
    def set_failure_mode(self, should_fail: bool = True):
        """设置失败模式"""
        self.should_fail = should_fail
    
    def enable_analysis(self, enabled: bool = True):
        """启用/禁用分析"""
        self.analysis_enabled = enabled


class MockCacheManager:
    """模拟缓存管理器"""
    
    def __init__(self):
        self.cache = {}
        self.hit_count = 0
        self.miss_count = 0
        self.enabled = True
        self.ttl = 3600  # 默认1小时
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存"""
        if not self.enabled:
            self.miss_count += 1
            return None
        
        if key in self.cache:
            self.hit_count += 1
            return self.cache[key]['value']
        else:
            self.miss_count += 1
            return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存"""
        if not self.enabled:
            return False
        
        self.cache[key] = {
            'value': value,
            'ttl': ttl or self.ttl,
            'timestamp': datetime.now(timezone.utc)
        }
        return True
    
    async def delete(self, key: str) -> bool:
        """删除缓存"""
        if key in self.cache:
            del self.cache[key]
            return True
        return False
    
    async def clear(self) -> bool:
        """清空缓存"""
        self.cache.clear()
        return True
    
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        return key in self.cache
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        total_requests = self.hit_count + self.miss_count
        hit_rate = self.hit_count / total_requests if total_requests > 0 else 0
        
        return {
            'hit_count': self.hit_count,
            'miss_count': self.miss_count,
            'hit_rate': hit_rate,
            'cache_size': len(self.cache),
            'enabled': self.enabled
        }
    
    def reset_stats(self):
        """重置统计"""
        self.hit_count = 0
        self.miss_count = 0
    
    def enable(self, enabled: bool = True):
        """启用/禁用缓存"""
        self.enabled = enabled


class MockSecurityManager:
    """模拟安全管理器"""
    
    def __init__(self):
        self.tokens = {}
        self.blacklisted_tokens = set()
        self.rate_limits = {}
        self.security_enabled = True
    
    async def create_token(self, user_id: int, **kwargs) -> str:
        """创建令牌"""
        token = f"token_{uuid.uuid4().hex}"
        self.tokens[token] = {
            'user_id': user_id,
            'created_at': datetime.now(timezone.utc),
            'expires_at': datetime.now(timezone.utc),
            **kwargs
        }
        return token
    
    async def validate_token(self, token: str) -> Optional[Dict[str, Any]]:
        """验证令牌"""
        if not self.security_enabled:
            return {'user_id': 1, 'role': 'user'}
        
        if token in self.blacklisted_tokens:
            return None
        
        return self.tokens.get(token)
    
    async def revoke_token(self, token: str) -> bool:
        """撤销令牌"""
        self.blacklisted_tokens.add(token)
        return True
    
    async def check_rate_limit(self, user_id: int, action: str) -> bool:
        """检查速率限制"""
        if not self.security_enabled:
            return True
        
        key = f"{user_id}:{action}"
        current_time = datetime.now(timezone.utc)
        
        if key not in self.rate_limits:
            self.rate_limits[key] = []
        
        # 清理过期记录
        self.rate_limits[key] = [
            timestamp for timestamp in self.rate_limits[key]
            if (current_time - timestamp).seconds < 3600
        ]
        
        # 检查限制
        if len(self.rate_limits[key]) >= 100:  # 每小时100次
            return False
        
        self.rate_limits[key].append(current_time)
        return True
    
    async def encrypt_data(self, data: str) -> str:
        """加密数据"""
        return f"encrypted_{data}"
    
    async def decrypt_data(self, encrypted_data: str) -> str:
        """解密数据"""
        if encrypted_data.startswith("encrypted_"):
            return encrypted_data[10:]
        return encrypted_data
    
    def enable_security(self, enabled: bool = True):
        """启用/禁用安全"""
        self.security_enabled = enabled
    
    def clear_rate_limits(self):
        """清空速率限制"""
        self.rate_limits.clear()


class MockNotificationManager:
    """模拟通知管理器"""
    
    def __init__(self):
        self.notifications = []
        self.enabled = True
        self.delivery_delay = 0.05
    
    async def send_notification(
        self,
        user_id: int,
        title: str,
        message: str,
        notification_type: str = 'info',
        **kwargs
    ) -> bool:
        """发送通知"""
        if not self.enabled:
            return False
        
        await asyncio.sleep(self.delivery_delay)
        
        notification = {
            'id': str(uuid.uuid4()),
            'user_id': user_id,
            'title': title,
            'message': message,
            'type': notification_type,
            'timestamp': datetime.now(timezone.utc),
            'delivered': True,
            **kwargs
        }
        
        self.notifications.append(notification)
        return True
    
    async def send_email(
        self,
        to_email: str,
        subject: str,
        body: str,
        **kwargs
    ) -> bool:
        """发送邮件"""
        if not self.enabled:
            return False
        
        await asyncio.sleep(self.delivery_delay)
        
        email = {
            'id': str(uuid.uuid4()),
            'to': to_email,
            'subject': subject,
            'body': body,
            'timestamp': datetime.now(timezone.utc),
            'sent': True,
            **kwargs
        }
        
        self.notifications.append(email)
        return True
    
    def get_notifications(self, user_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """获取通知"""
        if user_id is None:
            return self.notifications.copy()
        
        return [
            n for n in self.notifications
            if n.get('user_id') == user_id
        ]
    
    def clear_notifications(self):
        """清空通知"""
        self.notifications.clear()
    
    def enable(self, enabled: bool = True):
        """启用/禁用通知"""
        self.enabled = enabled


class MockStorageManager:
    """模拟存储管理器"""
    
    def __init__(self):
        self.files = {}
        self.enabled = True
        self.max_file_size = 10 * 1024 * 1024  # 10MB
    
    async def upload_file(
        self,
        file_path: str,
        content: bytes,
        **kwargs
    ) -> Dict[str, Any]:
        """上传文件"""
        if not self.enabled:
            raise Exception("存储服务不可用")
        
        if len(content) > self.max_file_size:
            raise Exception("文件太大")
        
        file_id = str(uuid.uuid4())
        file_info = {
            'id': file_id,
            'path': file_path,
            'size': len(content),
            'content': content,
            'uploaded_at': datetime.now(timezone.utc),
            **kwargs
        }
        
        self.files[file_id] = file_info
        return {
            'file_id': file_id,
            'url': f"https://storage.example.com/{file_id}",
            'size': len(content)
        }
    
    async def download_file(self, file_id: str) -> Optional[bytes]:
        """下载文件"""
        if not self.enabled:
            return None
        
        file_info = self.files.get(file_id)
        return file_info['content'] if file_info else None
    
    async def delete_file(self, file_id: str) -> bool:
        """删除文件"""
        if file_id in self.files:
            del self.files[file_id]
            return True
        return False
    
    async def get_file_info(self, file_id: str) -> Optional[Dict[str, Any]]:
        """获取文件信息"""
        file_info = self.files.get(file_id)
        if file_info:
            return {
                'id': file_info['id'],
                'path': file_info['path'],
                'size': file_info['size'],
                'uploaded_at': file_info['uploaded_at']
            }
        return None
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """获取存储统计"""
        total_files = len(self.files)
        total_size = sum(f['size'] for f in self.files.values())
        
        return {
            'total_files': total_files,
            'total_size': total_size,
            'enabled': self.enabled
        }
    
    def clear_storage(self):
        """清空存储"""
        self.files.clear()
    
    def enable(self, enabled: bool = True):
        """启用/禁用存储"""
        self.enabled = enabled


class MockMonitoringManager:
    """模拟监控管理器"""
    
    def __init__(self):
        self.metrics = {}
        self.alerts = []
        self.enabled = True


class MockConversationManager:
    """模拟对话管理器"""
    
    def __init__(self):
        self.conversations = {}
        self.detailed_conversations = {}
        self.data_dir = "data/conversations"
        self.max_history = 10
        self.save_enabled = False
    
    def get_history(self, model_name, format_type="messages"):
        """获取模型的对话历史"""
        if model_name not in self.conversations:
            self.conversations[model_name] = []
        return self.conversations[model_name]
    
    def add_message(self, model_name, role, content):
        """添加消息到对话历史"""
        if model_name not in self.conversations:
            self.conversations[model_name] = []
        
        self.conversations[model_name].append({
            "role": role,
            "content": content
        })
        
        return True
    
    def clear_history(self, model_name):
        """清空对话历史"""
        if model_name in self.conversations:
            self.conversations[model_name] = []
        return True


class MockPromptManager:
    """模拟提示词管理器"""
    
    def __init__(self, prompts_dir="prompts"):
        self.prompts_dir = prompts_dir
        self.system_prompts = {
            "default": "你是一个有用的AI助手",
            "translator": "你是一个专业的翻译助手",
            "programmer": "你是一个编程专家"
        }
        self.templates = {
            "qa_format": "问题：{question}\n回答：",
            "report": "标题：{title}\n内容：{content}"
        }
    
    def get_system_prompt(self, name, variables=None):
        """获取系统提示词"""
        prompt = self.system_prompts.get(name, self.system_prompts.get("default", ""))
        
        if variables and isinstance(variables, dict):
            for key, value in variables.items():
                prompt = prompt.replace("{" + key + "}", str(value))
        
        return prompt
    
    async def record_metric(
        self,
        name: str,
        value: Union[int, float],
        tags: Optional[Dict[str, str]] = None,
        **kwargs
    ):
        """记录指标"""
        if not self.enabled:
            return
        
        if name not in self.metrics:
            self.metrics[name] = []
        
        metric = {
            'value': value,
            'timestamp': datetime.now(timezone.utc),
            'tags': tags or {},
            **kwargs
        }
        
        self.metrics[name].append(metric)
    
    async def get_metrics(
        self,
        name: str,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> List[Dict[str, Any]]:
        """获取指标"""
        metrics = self.metrics.get(name, [])
        
        if start_time or end_time:
            filtered_metrics = []
            for metric in metrics:
                timestamp = metric['timestamp']
                if start_time and timestamp < start_time:
                    continue
                if end_time and timestamp > end_time:
                    continue
                filtered_metrics.append(metric)
            return filtered_metrics
        
        return metrics.copy()
    
    async def create_alert(
        self,
        name: str,
        message: str,
        severity: str = 'info',
        **kwargs
    ):
        """创建告警"""
        if not self.enabled:
            return
        
        alert = {
            'id': str(uuid.uuid4()),
            'name': name,
            'message': message,
            'severity': severity,
            'timestamp': datetime.now(timezone.utc),
            'resolved': False,
            **kwargs
        }
        
        self.alerts.append(alert)
    
    async def resolve_alert(self, alert_id: str) -> bool:
        """解决告警"""
        for alert in self.alerts:
            if alert['id'] == alert_id:
                alert['resolved'] = True
                alert['resolved_at'] = datetime.now(timezone.utc)
                return True
        return False
    
    def get_alerts(
        self,
        resolved: Optional[bool] = None
    ) -> List[Dict[str, Any]]:
        """获取告警"""
        alerts = self.alerts.copy()
        
        if resolved is not None:
            alerts = [a for a in alerts if a['resolved'] == resolved]
        
        return alerts
    
    def get_system_health(self) -> Dict[str, Any]:
        """获取系统健康状态"""
        total_alerts = len(self.alerts)
        unresolved_alerts = len([a for a in self.alerts if not a['resolved']])
        
        return {
            'status': 'healthy' if unresolved_alerts == 0 else 'warning',
            'total_alerts': total_alerts,
            'unresolved_alerts': unresolved_alerts,
            'metrics_count': sum(len(metrics) for metrics in self.metrics.values()),
            'enabled': self.enabled
        }
    
    def clear_metrics(self):
        """清空指标"""
        self.metrics.clear()
    
    def clear_alerts(self):
        """清空告警"""
        self.alerts.clear()
    
    def enable(self, enabled: bool = True):
        """启用/禁用监控"""
        self.enabled = enabled