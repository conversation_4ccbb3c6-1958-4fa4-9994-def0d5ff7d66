"""API密钥服务单元测试"""

import uuid
from datetime import datetime
from unittest.mock import Mock

import pytest
from exceptions.api_exceptions import NotFoundAPIError, ValidationAPIError, DuplicateAPIError
from models.api_key import APIKeyModel
from schemas.api_key import APIKeyCreate, APIKeyUpdate
from services.api_key_service import APIKeyService

from tests.utils.test_helpers import MockTestCase


@pytest.mark.service
@pytest.mark.unit
class TestAPIKeyService(MockTestCase):
    """API密钥服务测试类"""
    
    @pytest.fixture
    def mock_db_session(self):
        """模拟数据库会话"""
        session = Mock()
        session.query.return_value = session
        session.filter.return_value = session
        session.filter_by.return_value = session
        session.first.return_value = None
        session.all.return_value = []
        session.count.return_value = 0
        session.add = Mock()
        session.commit = Mock()
        session.delete = Mock()
        session.rollback = Mock()
        return session
    
    @pytest.fixture
    def api_key_service(self, mock_db_session):
        """创建API密钥服务实例"""
        return APIKeyService(db_session=mock_db_session)
    
    @pytest.fixture
    def sample_api_key_create(self):
        """示例API密钥创建数据"""
        return APIKeyCreate(
            name="Test API Key",
            provider="openai",
            key_value="sk-test123456789",
            description="Test API key for unit testing",
            is_active=True
        )
    
    @pytest.fixture
    def sample_api_key_update(self):
        """示例API密钥更新数据"""
        return APIKeyUpdate(
            name="Updated API Key",
            description="Updated description",
            is_active=False
        )
    
    @pytest.fixture
    def sample_api_key_model(self):
        """示例API密钥模型"""
        return APIKeyModel(
            id=str(uuid.uuid4()),
            name="Test API Key",
            provider="openai",
            key_value="sk-test123456789",
            description="Test API key",
            is_active=True,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
            last_used_at=None,
            usage_count=0
        )
    
    def test_create_api_key_success(self, api_key_service: APIKeyService, mock_db_session, sample_api_key_create):
        """测试成功创建API密钥"""
        # 模拟数据库操作
        mock_db_session.query.return_value.filter_by.return_value.first.return_value = None
        
        # 创建API密钥
        result = api_key_service.create_api_key(sample_api_key_create)
        
        # 验证结果
        assert result.name == sample_api_key_create.name
        assert result.provider == sample_api_key_create.provider
        assert result.key_value == sample_api_key_create.key_value
        assert result.description == sample_api_key_create.description
        assert result.is_active == sample_api_key_create.is_active
        
        # 验证数据库操作
        mock_db_session.add.assert_called_once()
        mock_db_session.commit.assert_called_once()
    
    def test_create_api_key_duplicate_name(self, api_key_service: APIKeyService, mock_db_session, sample_api_key_create, sample_api_key_model):
        """测试创建重复名称的API密钥"""
        # 模拟名称已存在
        mock_db_session.query.return_value.filter_by.return_value.first.return_value = sample_api_key_model
        
        # 尝试创建重复名称的API密钥
        with pytest.raises(DuplicateAPIError) as exc_info:
            api_key_service.create_api_key(sample_api_key_create)
        
        assert "API key name already exists" in str(exc_info.value)
    
    def test_create_api_key_invalid_provider(self, api_key_service: APIKeyService, mock_db_session):
        """测试创建无效提供商的API密钥"""
        invalid_create = APIKeyCreate(
            name="Invalid Provider Key",
            provider="invalid_provider",
            key_value="sk-test123",
            description="Invalid provider test"
        )
        
        # 尝试创建无效提供商的API密钥
        with pytest.raises(ValidationAPIError) as exc_info:
            api_key_service.create_api_key(invalid_create)
        
        assert "Invalid provider" in str(exc_info.value)
    
    def test_get_api_key_by_id_success(self, api_key_service: APIKeyService, mock_db_session, sample_api_key_model):
        """测试成功根据ID获取API密钥"""
        # 模拟数据库查询
        mock_db_session.query.return_value.filter.return_value.first.return_value = sample_api_key_model
        
        # 获取API密钥
        result = api_key_service.get_api_key_by_id(sample_api_key_model.id)
        
        # 验证结果
        assert result.id == sample_api_key_model.id
        assert result.name == sample_api_key_model.name
        assert result.provider == sample_api_key_model.provider
    
    def test_get_api_key_by_id_not_found(self, api_key_service: APIKeyService, mock_db_session):
        """测试获取不存在的API密钥"""
        # 模拟API密钥不存在
        mock_db_session.query.return_value.filter.return_value.first.return_value = None
        
        # 尝试获取不存在的API密钥
        with pytest.raises(NotFoundAPIError) as exc_info:
            api_key_service.get_api_key_by_id("nonexistent-id")
        
        assert "API key not found" in str(exc_info.value)
    
    def test_get_api_key_by_name_success(self, api_key_service: APIKeyService, mock_db_session, sample_api_key_model):
        """测试成功根据名称获取API密钥"""
        # 模拟数据库查询
        mock_db_session.query.return_value.filter_by.return_value.first.return_value = sample_api_key_model
        
        # 获取API密钥
        result = api_key_service.get_api_key_by_name(sample_api_key_model.name)
        
        # 验证结果
        assert result.name == sample_api_key_model.name
    
    def test_get_api_keys_list(self, api_key_service: APIKeyService, mock_db_session, sample_api_key_model):
        """测试获取API密钥列表"""
        # 模拟数据库查询
        mock_db_session.query.return_value.all.return_value = [sample_api_key_model]
        
        # 获取API密钥列表
        result = api_key_service.get_api_keys()
        
        # 验证结果
        assert len(result) == 1
        assert result[0].id == sample_api_key_model.id
    
    def test_get_api_keys_by_provider(self, api_key_service: APIKeyService, mock_db_session, sample_api_key_model):
        """测试按提供商获取API密钥"""
        # 模拟数据库查询
        mock_db_session.query.return_value.filter_by.return_value.all.return_value = [sample_api_key_model]
        
        # 按提供商获取API密钥
        result = api_key_service.get_api_keys_by_provider("openai")
        
        # 验证结果
        assert len(result) == 1
        assert result[0].provider == "openai"
    
    def test_get_active_api_keys(self, api_key_service: APIKeyService, mock_db_session, sample_api_key_model):
        """测试获取活跃的API密钥"""
        # 模拟数据库查询
        mock_db_session.query.return_value.filter_by.return_value.all.return_value = [sample_api_key_model]
        
        # 获取活跃的API密钥
        result = api_key_service.get_active_api_keys()
        
        # 验证结果
        assert len(result) == 1
        assert result[0].is_active is True
    
    def test_update_api_key_success(self, api_key_service: APIKeyService, mock_db_session, sample_api_key_model, sample_api_key_update):
        """测试成功更新API密钥"""
        # 模拟数据库查询
        mock_db_session.query.return_value.filter.return_value.first.return_value = sample_api_key_model
        
        # 更新API密钥
        result = api_key_service.update_api_key(sample_api_key_model.id, sample_api_key_update)
        
        # 验证结果
        assert result.name == sample_api_key_update.name
        assert result.description == sample_api_key_update.description
        assert result.is_active == sample_api_key_update.is_active
        
        # 验证数据库操作
        mock_db_session.commit.assert_called_once()
    
    def test_update_api_key_not_found(self, api_key_service: APIKeyService, mock_db_session, sample_api_key_update):
        """测试更新不存在的API密钥"""
        # 模拟API密钥不存在
        mock_db_session.query.return_value.filter.return_value.first.return_value = None
        
        # 尝试更新不存在的API密钥
        with pytest.raises(NotFoundAPIError) as exc_info:
            api_key_service.update_api_key("nonexistent-id", sample_api_key_update)
        
        assert "API key not found" in str(exc_info.value)
    
    def test_delete_api_key_success(self, api_key_service: APIKeyService, mock_db_session, sample_api_key_model):
        """测试成功删除API密钥"""
        # 模拟数据库查询
        mock_db_session.query.return_value.filter.return_value.first.return_value = sample_api_key_model
        
        # 删除API密钥
        result = api_key_service.delete_api_key(sample_api_key_model.id)
        
        # 验证结果
        assert result is True
        
        # 验证数据库操作
        mock_db_session.delete.assert_called_once_with(sample_api_key_model)
        mock_db_session.commit.assert_called_once()
    
    def test_delete_api_key_not_found(self, api_key_service: APIKeyService, mock_db_session):
        """测试删除不存在的API密钥"""
        # 模拟API密钥不存在
        mock_db_session.query.return_value.filter.return_value.first.return_value = None
        
        # 尝试删除不存在的API密钥
        with pytest.raises(NotFoundAPIError) as exc_info:
            api_key_service.delete_api_key("nonexistent-id")
        
        assert "API key not found" in str(exc_info.value)
    
    def test_toggle_api_key_status(self, api_key_service: APIKeyService, mock_db_session, sample_api_key_model):
        """测试切换API密钥状态"""
        # 模拟数据库查询
        mock_db_session.query.return_value.filter.return_value.first.return_value = sample_api_key_model
        
        original_status = sample_api_key_model.is_active
        
        # 切换API密钥状态
        result = api_key_service.toggle_api_key_status(sample_api_key_model.id)
        
        # 验证结果
        assert result.is_active != original_status
        
        # 验证数据库操作
        mock_db_session.commit.assert_called_once()
    
    def test_validate_api_key_success(self, api_key_service: APIKeyService, mock_db_session, sample_api_key_model):
        """测试成功验证API密钥"""
        # 模拟数据库查询
        mock_db_session.query.return_value.filter_by.return_value.first.return_value = sample_api_key_model
        
        # 验证API密钥
        result = api_key_service.validate_api_key(sample_api_key_model.key_value)
        
        # 验证结果
        assert result is True
    
    def test_validate_api_key_invalid(self, api_key_service: APIKeyService, mock_db_session):
        """测试验证无效的API密钥"""
        # 模拟API密钥不存在
        mock_db_session.query.return_value.filter_by.return_value.first.return_value = None
        
        # 验证无效的API密钥
        result = api_key_service.validate_api_key("invalid-key")
        
        # 验证结果
        assert result is False
    
    def test_validate_api_key_inactive(self, api_key_service: APIKeyService, mock_db_session, sample_api_key_model):
        """测试验证非活跃的API密钥"""
        # 设置API密钥为非活跃状态
        sample_api_key_model.is_active = False
        
        # 模拟数据库查询
        mock_db_session.query.return_value.filter_by.return_value.first.return_value = sample_api_key_model
        
        # 验证非活跃的API密钥
        result = api_key_service.validate_api_key(sample_api_key_model.key_value)
        
        # 验证结果
        assert result is False
    
    def test_update_usage_stats(self, api_key_service: APIKeyService, mock_db_session, sample_api_key_model):
        """测试更新使用统计"""
        # 模拟数据库查询
        mock_db_session.query.return_value.filter_by.return_value.first.return_value = sample_api_key_model
        
        original_count = sample_api_key_model.usage_count
        
        # 更新使用统计
        api_key_service.update_usage_stats(sample_api_key_model.key_value)
        
        # 验证结果
        assert sample_api_key_model.usage_count == original_count + 1
        assert sample_api_key_model.last_used_at is not None
        
        # 验证数据库操作
        mock_db_session.commit.assert_called_once()
    
    def test_get_usage_statistics(self, api_key_service: APIKeyService, mock_db_session, sample_api_key_model):
        """测试获取使用统计"""
        # 设置使用统计数据
        sample_api_key_model.usage_count = 100
        sample_api_key_model.last_used_at = datetime.utcnow()
        
        # 模拟数据库查询
        mock_db_session.query.return_value.filter.return_value.first.return_value = sample_api_key_model
        
        # 获取使用统计
        result = api_key_service.get_usage_statistics(sample_api_key_model.id)
        
        # 验证结果
        assert result["usage_count"] == 100
        assert result["last_used_at"] is not None
        assert "created_at" in result
    
    def test_search_api_keys(self, api_key_service: APIKeyService, mock_db_session, sample_api_key_model):
        """测试搜索API密钥"""
        # 模拟数据库查询
        mock_db_session.query.return_value.filter.return_value.all.return_value = [sample_api_key_model]
        
        # 搜索API密钥
        result = api_key_service.search_api_keys("Test")
        
        # 验证结果
        assert len(result) == 1
        assert result[0].name == sample_api_key_model.name
    
    def test_get_api_keys_pagination(self, api_key_service: APIKeyService, mock_db_session, sample_api_key_model):
        """测试分页获取API密钥"""
        # 模拟数据库查询
        mock_query = mock_db_session.query.return_value
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = [sample_api_key_model]
        mock_db_session.query.return_value.count.return_value = 1
        
        # 分页获取API密钥
        result, total = api_key_service.get_api_keys_paginated(page=1, page_size=10)
        
        # 验证结果
        assert len(result) == 1
        assert total == 1
        
        # 验证分页参数
        mock_query.offset.assert_called_once_with(0)
        mock_query.limit.assert_called_once_with(10)
    
    def test_bulk_update_api_keys(self, api_key_service: APIKeyService, mock_db_session, sample_api_key_model):
        """测试批量更新API密钥"""
        # 模拟数据库查询
        mock_db_session.query.return_value.filter.return_value.all.return_value = [sample_api_key_model]
        
        api_key_ids = [sample_api_key_model.id]
        update_data = {"is_active": False}
        
        # 批量更新API密钥
        result = api_key_service.bulk_update_api_keys(api_key_ids, update_data)
        
        # 验证结果
        assert len(result) == 1
        assert result[0].is_active is False
        
        # 验证数据库操作
        mock_db_session.commit.assert_called_once()
    
    def test_export_api_keys(self, api_key_service: APIKeyService, mock_db_session, sample_api_key_model):
        """测试导出API密钥"""
        # 模拟数据库查询
        mock_db_session.query.return_value.all.return_value = [sample_api_key_model]
        
        # 导出API密钥
        result = api_key_service.export_api_keys()
        
        # 验证结果
        assert len(result) == 1
        assert result[0]["name"] == sample_api_key_model.name
        assert result[0]["provider"] == sample_api_key_model.provider
        # 密钥值应该被掩码
        assert "***" in result[0]["key_value"]


@pytest.mark.service
@pytest.mark.unit
class TestAPIKeyServiceEdgeCases(MockTestCase):
    """API密钥服务边界情况测试类"""
    
    @pytest.fixture
    def mock_db_session(self):
        """模拟数据库会话"""
        session = Mock()
        return session
    
    @pytest.fixture
    def api_key_service(self, mock_db_session):
        """创建API密钥服务实例"""
        return APIKeyService(db_session=mock_db_session)
    
    def test_create_api_key_with_special_characters(self, api_key_service: APIKeyService, mock_db_session):
        """测试创建包含特殊字符的API密钥"""
        special_create = APIKeyCreate(
            name="API Key with 特殊字符 & symbols!",
            provider="openai",
            key_value="sk-test_123-456.789",
            description="Description with émojis 🔑 and symbols @#$%"
        )
        
        # 模拟数据库操作
        mock_db_session.query.return_value.filter_by.return_value.first.return_value = None
        
        # 创建API密钥
        result = api_key_service.create_api_key(special_create)
        
        # 验证特殊字符被正确处理
        assert result.name == special_create.name
        assert result.description == special_create.description
    
    def test_create_api_key_with_empty_description(self, api_key_service: APIKeyService, mock_db_session):
        """测试创建空描述的API密钥"""
        empty_desc_create = APIKeyCreate(
            name="Empty Description Key",
            provider="anthropic",
            key_value="sk-ant-test123",
            description=""
        )
        
        # 模拟数据库操作
        mock_db_session.query.return_value.filter_by.return_value.first.return_value = None
        
        # 创建API密钥
        result = api_key_service.create_api_key(empty_desc_create)
        
        # 验证空描述被正确处理
        assert result.description == ""
    
    def test_create_api_key_with_very_long_name(self, api_key_service: APIKeyService, mock_db_session):
        """测试创建超长名称的API密钥"""
        long_name = "A" * 1000  # 超长名称
        
        long_name_create = APIKeyCreate(
            name=long_name,
            provider="openai",
            key_value="sk-test123",
            description="Long name test"
        )
        
        # 应该抛出验证错误
        with pytest.raises(ValidationAPIError) as exc_info:
            api_key_service.create_api_key(long_name_create)
        
        assert "Name too long" in str(exc_info.value)
    
    def test_database_connection_error(self, api_key_service: APIKeyService, mock_db_session):
        """测试数据库连接错误"""
        from sqlalchemy.exc import OperationalError
        
        # 模拟数据库连接错误
        mock_db_session.query.side_effect = OperationalError("Connection failed", None, None)
        
        # 尝试获取API密钥列表
        with pytest.raises(Exception):
            api_key_service.get_api_keys()
    
    def test_concurrent_api_key_creation(self, api_key_service: APIKeyService, mock_db_session):
        """测试并发创建API密钥"""
        import threading

        results = []
        errors = []
        
        def create_api_key(index):
            try:
                create_data = APIKeyCreate(
                    name=f"Concurrent Key {index}",
                    provider="openai",
                    key_value=f"sk-test{index}",
                    description=f"Concurrent test {index}"
                )
                result = api_key_service.create_api_key(create_data)
                results.append(result)
            except Exception as e:
                errors.append(e)
        
        # 模拟数据库操作
        mock_db_session.query.return_value.filter_by.return_value.first.return_value = None
        
        # 创建多个线程同时创建API密钥
        threads = []
        for i in range(10):
            thread = threading.Thread(target=create_api_key, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证结果
        assert len(errors) == 0
        assert len(results) == 10
    
    def test_api_key_encryption(self, api_key_service: APIKeyService, mock_db_session):
        """测试API密钥加密"""
        create_data = APIKeyCreate(
            name="Encryption Test Key",
            provider="openai",
            key_value="sk-very-secret-key-123",
            description="Encryption test"
        )
        
        # 模拟数据库操作
        mock_db_session.query.return_value.filter_by.return_value.first.return_value = None
        
        # 创建API密钥
        result = api_key_service.create_api_key(create_data)
        
        # 验证密钥值被加密存储（实际实现中应该加密）
        # 这里只是验证密钥值存在
        assert result.key_value is not None
    
    def test_api_key_masking(self, api_key_service: APIKeyService):
        """测试API密钥掩码"""
        key_value = "sk-**********abcdef"
        
        # 测试密钥掩码
        masked_key = api_key_service.mask_api_key(key_value)
        
        # 验证掩码格式
        assert masked_key.startswith("sk-")
        assert "***" in masked_key
        assert len(masked_key) < len(key_value)
    
    def test_api_key_validation_patterns(self, api_key_service: APIKeyService):
        """测试API密钥验证模式"""
        # 测试不同提供商的密钥格式
        test_cases = [
            ("openai", "sk-**********abcdef", True),
            ("anthropic", "sk-ant-**********abcdef", True),
            ("google", "AIza**********abcdef", True),
            ("openai", "invalid-key", False),
            ("anthropic", "sk-**********", False),
        ]
        
        for provider, key_value, expected in test_cases:
            result = api_key_service.validate_key_format(provider, key_value)
            assert result == expected, f"Failed for {provider}: {key_value}"
    
    def test_memory_usage_with_large_dataset(self, api_key_service: APIKeyService, mock_db_session, memory_monitor):
        """测试大数据集的内存使用"""
        # 创建大量API密钥模型
        large_dataset = []
        for i in range(10000):
            api_key = APIKeyModel(
                id=str(uuid.uuid4()),
                name=f"API Key {i}",
                provider="openai",
                key_value=f"sk-test{i}",
                description=f"Test key {i}",
                is_active=True,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            large_dataset.append(api_key)
        
        # 模拟数据库返回大数据集
        mock_db_session.query.return_value.all.return_value = large_dataset
        
        with memory_monitor:
            # 获取大量API密钥
            result = api_key_service.get_api_keys()
        
        # 验证内存使用在合理范围内
        memory_usage = memory_monitor.get_peak_memory()
        assert memory_usage < 100 * 1024 * 1024  # 小于100MB
        assert len(result) == 10000
    
    def test_performance_with_frequent_operations(self, api_key_service: APIKeyService, mock_db_session, performance_timer):
        """测试频繁操作的性能"""
        # 模拟数据库操作
        mock_db_session.query.return_value.filter_by.return_value.first.return_value = None
        
        with performance_timer:
            # 执行大量API密钥操作
            for i in range(1000):
                create_data = APIKeyCreate(
                    name=f"Perf Test Key {i}",
                    provider="openai",
                    key_value=f"sk-test{i}",
                    description=f"Performance test {i}"
                )
                api_key_service.create_api_key(create_data)
        
        # 验证性能在可接受范围内
        elapsed_time = performance_timer.elapsed
        assert elapsed_time < 5.0  # 1000次操作应该在5秒内完成