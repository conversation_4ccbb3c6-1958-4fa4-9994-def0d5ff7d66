"""日志服务单元测试"""

import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock, mock_open
from datetime import datetime, timedelta
import uuid
import json
import logging
import tempfile
import os

from services.logging_service import LoggingService
from exceptions.api_exceptions import LoggingError, ValidationAPIError
from tests.utils.test_helpers import AsyncTestCase, MockTestCase


@pytest.mark.service
@pytest.mark.unit
class TestLoggingService(MockTestCase):
    """日志服务测试类"""
    
    @pytest.fixture
    def mock_logger(self):
        """模拟日志记录器"""
        logger = Mock(spec=logging.Logger)
        logger.debug = Mock()
        logger.info = Mock()
        logger.warning = Mock()
        logger.error = Mock()
        logger.critical = Mock()
        logger.exception = Mock()
        logger.setLevel = Mock()
        logger.addHandler = Mock()
        logger.removeHandler = Mock()
        logger.handlers = []
        return logger
    
    @pytest.fixture
    def mock_handler(self):
        """模拟日志处理器"""
        handler = Mock(spec=logging.Handler)
        handler.setLevel = Mock()
        handler.setFormatter = Mock()
        handler.emit = Mock()
        handler.close = Mock()
        return handler
    
    @pytest.fixture
    def logging_service(self, mock_logger):
        """创建日志服务实例"""
        with patch('logging.getLogger', return_value=mock_logger):
            service = LoggingService(
                name="test_logger",
                level=logging.INFO,
                format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            )
            service.logger = mock_logger
            return service
        return None

    @pytest.fixture
    def sample_log_data(self):
        """示例日志数据"""
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "level": "INFO",
            "message": "Test log message",
            "module": "test_module",
            "function": "test_function",
            "line_number": 42,
            "extra_data": {
                "user_id": "user123",
                "request_id": "req456",
                "action": "test_action"
            }
        }
    
    def test_create_logging_service_success(self, logging_service: LoggingService):
        """测试成功创建日志服务"""
        # 验证日志服务创建
        assert logging_service is not None
        assert logging_service.name == "test_logger"
        assert logging_service.level == logging.INFO
    
    def test_log_debug_message(self, logging_service: LoggingService, mock_logger):
        """测试记录调试消息"""
        # 记录调试消息
        message = "Debug message"
        logging_service.debug(message)
        
        # 验证调用
        mock_logger.debug.assert_called_once_with(message)
    
    def test_log_info_message(self, logging_service: LoggingService, mock_logger):
        """测试记录信息消息"""
        # 记录信息消息
        message = "Info message"
        logging_service.info(message)
        
        # 验证调用
        mock_logger.info.assert_called_once_with(message)
    
    def test_log_warning_message(self, logging_service: LoggingService, mock_logger):
        """测试记录警告消息"""
        # 记录警告消息
        message = "Warning message"
        logging_service.warning(message)
        
        # 验证调用
        mock_logger.warning.assert_called_once_with(message)
    
    def test_log_error_message(self, logging_service: LoggingService, mock_logger):
        """测试记录错误消息"""
        # 记录错误消息
        message = "Error message"
        logging_service.error(message)
        
        # 验证调用
        mock_logger.error.assert_called_once_with(message)
    
    def test_log_critical_message(self, logging_service: LoggingService, mock_logger):
        """测试记录严重错误消息"""
        # 记录严重错误消息
        message = "Critical message"
        logging_service.critical(message)
        
        # 验证调用
        mock_logger.critical.assert_called_once_with(message)
    
    def test_log_exception(self, logging_service: LoggingService, mock_logger):
        """测试记录异常"""
        # 记录异常
        try:
            raise ValueError("Test exception")
        except ValueError:
            logging_service.exception("Exception occurred")
        
        # 验证调用
        mock_logger.exception.assert_called_once_with("Exception occurred")
    
    def test_log_with_extra_data(self, logging_service: LoggingService, mock_logger, sample_log_data):
        """测试记录带额外数据的日志"""
        # 记录带额外数据的日志
        message = "Message with extra data"
        extra_data = sample_log_data["extra_data"]
        
        logging_service.info(message, extra=extra_data)
        
        # 验证调用
        mock_logger.info.assert_called_once_with(message, extra=extra_data)
    
    def test_set_log_level(self, logging_service: LoggingService, mock_logger):
        """测试设置日志级别"""
        # 设置日志级别
        new_level = logging.DEBUG
        logging_service.set_level(new_level)
        
        # 验证设置
        mock_logger.setLevel.assert_called_once_with(new_level)
        assert logging_service.level == new_level
    
    def test_add_file_handler(self, logging_service: LoggingService, mock_logger, mock_handler):
        """测试添加文件处理器"""
        with patch('logging.FileHandler', return_value=mock_handler):
            # 添加文件处理器
            file_path = "/tmp/test.log"
            logging_service.add_file_handler(file_path, level=logging.WARNING)
            
            # 验证处理器添加
            mock_logger.addHandler.assert_called_once_with(mock_handler)
            mock_handler.setLevel.assert_called_once_with(logging.WARNING)
    
    def test_add_console_handler(self, logging_service: LoggingService, mock_logger, mock_handler):
        """测试添加控制台处理器"""
        with patch('logging.StreamHandler', return_value=mock_handler):
            # 添加控制台处理器
            logging_service.add_console_handler(level=logging.ERROR)
            
            # 验证处理器添加
            mock_logger.addHandler.assert_called_once_with(mock_handler)
            mock_handler.setLevel.assert_called_once_with(logging.ERROR)
    
    def test_add_rotating_file_handler(self, logging_service: LoggingService, mock_logger, mock_handler):
        """测试添加轮转文件处理器"""
        with patch('logging.handlers.RotatingFileHandler', return_value=mock_handler):
            # 添加轮转文件处理器
            file_path = "/tmp/rotating.log"
            max_bytes = 1024 * 1024  # 1MB
            backup_count = 5
            
            logging_service.add_rotating_file_handler(
                file_path, 
                max_bytes=max_bytes, 
                backup_count=backup_count
            )
            
            # 验证处理器添加
            mock_logger.addHandler.assert_called_once_with(mock_handler)
    
    def test_add_time_rotating_handler(self, logging_service: LoggingService, mock_logger, mock_handler):
        """测试添加时间轮转处理器"""
        with patch('logging.handlers.TimedRotatingFileHandler', return_value=mock_handler):
            # 添加时间轮转处理器
            file_path = "/tmp/timed_rotating.log"
            when = "midnight"
            interval = 1
            backup_count = 7
            
            logging_service.add_timed_rotating_handler(
                file_path,
                when=when,
                interval=interval,
                backup_count=backup_count
            )
            
            # 验证处理器添加
            mock_logger.addHandler.assert_called_once_with(mock_handler)
    
    def test_remove_handler(self, logging_service: LoggingService, mock_logger, mock_handler):
        """测试移除处理器"""
        # 模拟已添加的处理器
        mock_logger.handlers = [mock_handler]
        
        # 移除处理器
        logging_service.remove_handler(mock_handler)
        
        # 验证处理器移除
        mock_logger.removeHandler.assert_called_once_with(mock_handler)
    
    def test_clear_handlers(self, logging_service: LoggingService, mock_logger):
        """测试清除所有处理器"""
        # 模拟多个处理器
        handler1 = Mock()
        handler2 = Mock()
        mock_logger.handlers = [handler1, handler2]
        
        # 清除所有处理器
        logging_service.clear_handlers()
        
        # 验证所有处理器被移除
        assert mock_logger.removeHandler.call_count == 2
    
    def test_format_log_message(self, logging_service: LoggingService):
        """测试格式化日志消息"""
        # 格式化日志消息
        message = "Test message"
        level = "INFO"
        timestamp = datetime.utcnow()
        
        formatted = logging_service.format_message(message, level, timestamp)
        
        # 验证格式化结果
        assert message in formatted
        assert level in formatted
        assert timestamp.strftime("%Y-%m-%d") in formatted
    
    def test_log_structured_data(self, logging_service: LoggingService, mock_logger, sample_log_data):
        """测试记录结构化数据"""
        # 记录结构化数据
        logging_service.log_structured(sample_log_data)
        
        # 验证调用
        mock_logger.info.assert_called_once()
        call_args = mock_logger.info.call_args
        assert sample_log_data["message"] in str(call_args)
    
    def test_log_performance_metrics(self, logging_service: LoggingService, mock_logger):
        """测试记录性能指标"""
        # 记录性能指标
        metrics = {
            "operation": "database_query",
            "duration_ms": 150.5,
            "memory_usage_mb": 45.2,
            "cpu_usage_percent": 12.8
        }
        
        logging_service.log_performance(metrics)
        
        # 验证调用
        mock_logger.info.assert_called_once()
        call_args = mock_logger.info.call_args
        assert "performance" in str(call_args).lower()
    
    def test_log_security_event(self, logging_service: LoggingService, mock_logger):
        """测试记录安全事件"""
        # 记录安全事件
        security_event = {
            "event_type": "authentication_failure",
            "user_id": "user123",
            "ip_address": "*************",
            "user_agent": "Mozilla/5.0...",
            "timestamp": datetime.utcnow().isoformat()
        }
        
        logging_service.log_security(security_event)
        
        # 验证调用
        mock_logger.warning.assert_called_once()
        call_args = mock_logger.warning.call_args
        assert "security" in str(call_args).lower()
    
    def test_log_api_request(self, logging_service: LoggingService, mock_logger):
        """测试记录API请求"""
        # 记录API请求
        request_data = {
            "method": "POST",
            "url": "/api/v1/models",
            "status_code": 201,
            "response_time_ms": 245.7,
            "user_id": "user123",
            "request_id": "req456"
        }
        
        logging_service.log_api_request(request_data)
        
        # 验证调用
        mock_logger.info.assert_called_once()
        call_args = mock_logger.info.call_args
        assert "api_request" in str(call_args).lower()
    
    def test_log_database_operation(self, logging_service: LoggingService, mock_logger):
        """测试记录数据库操作"""
        # 记录数据库操作
        db_operation = {
            "operation": "INSERT",
            "table": "models",
            "duration_ms": 12.3,
            "affected_rows": 1,
            "query_hash": "abc123"
        }
        
        logging_service.log_database_operation(db_operation)
        
        # 验证调用
        mock_logger.debug.assert_called_once()
        call_args = mock_logger.debug.call_args
        assert "database" in str(call_args).lower()
    
    def test_create_child_logger(self, logging_service: LoggingService):
        """测试创建子日志记录器"""
        with patch('logging.getLogger') as mock_get_logger:
            # 创建子日志记录器
            child_name = "child_logger"
            child_logger = logging_service.create_child_logger(child_name)
            
            # 验证子日志记录器创建
            expected_name = f"{logging_service.name}.{child_name}"
            mock_get_logger.assert_called_once_with(expected_name)
    
    def test_log_context_manager(self, logging_service: LoggingService, mock_logger):
        """测试日志上下文管理器"""
        # 使用日志上下文
        context_data = {"user_id": "user123", "session_id": "session456"}
        
        with logging_service.log_context(context_data):
            logging_service.info("Message with context")
        
        # 验证上下文数据被包含
        mock_logger.info.assert_called_once()
    
    def test_log_correlation_id(self, logging_service: LoggingService, mock_logger):
        """测试日志关联ID"""
        # 设置关联ID
        correlation_id = "corr123"
        logging_service.set_correlation_id(correlation_id)
        
        # 记录日志
        logging_service.info("Message with correlation ID")
        
        # 验证关联ID被包含
        mock_logger.info.assert_called_once()
        call_args = mock_logger.info.call_args
        # 在实际实现中，关联ID应该被包含在日志中
    
    def test_log_sampling(self, logging_service: LoggingService, mock_logger):
        """测试日志采样"""
        # 设置采样率（50%）
        logging_service.set_sampling_rate(0.5)
        
        # 记录多条日志
        for i in range(100):
            logging_service.info(f"Sampled message {i}")
        
        # 验证采样效果（应该少于100次调用）
        call_count = mock_logger.info.call_count
        assert call_count < 100
        assert call_count > 0  # 但应该有一些调用
    
    def test_log_filtering(self, logging_service: LoggingService, mock_logger):
        """测试日志过滤"""
        # 添加日志过滤器
        def sensitive_filter(record):
            return "password" not in record.getMessage().lower()
        
        logging_service.add_filter(sensitive_filter)
        
        # 记录包含敏感信息的日志
        logging_service.info("User login successful")
        logging_service.info("Password reset failed")
        
        # 验证过滤效果
        # 在实际实现中，包含"password"的日志应该被过滤掉
    
    def test_log_aggregation(self, logging_service: LoggingService, mock_logger):
        """测试日志聚合"""
        # 启用日志聚合
        logging_service.enable_aggregation(window_seconds=1)
        
        # 记录重复日志
        for i in range(10):
            logging_service.info("Repeated message")
        
        # 等待聚合窗口
        import time
        time.sleep(1.1)
        
        # 验证聚合效果（应该只有一次调用，但包含计数信息）
        # 在实际实现中，重复的日志应该被聚合
    
    def test_log_buffering(self, logging_service: LoggingService, mock_logger):
        """测试日志缓冲"""
        # 启用日志缓冲
        logging_service.enable_buffering(buffer_size=5)
        
        # 记录少于缓冲区大小的日志
        for i in range(3):
            logging_service.info(f"Buffered message {i}")
        
        # 验证日志被缓冲（暂时没有调用）
        assert mock_logger.info.call_count == 0
        
        # 记录更多日志触发缓冲区刷新
        for i in range(3, 6):
            logging_service.info(f"Buffered message {i}")
        
        # 验证缓冲区被刷新
        assert mock_logger.info.call_count >= 5
    
    def test_log_compression(self, logging_service: LoggingService):
        """测试日志压缩"""
        # 启用日志压缩
        with tempfile.TemporaryDirectory() as temp_dir:
            log_file = os.path.join(temp_dir, "test.log")
            
            # 添加压缩文件处理器
            logging_service.add_compressed_file_handler(
                log_file,
                max_bytes=1024,
                backup_count=3
            )
            
            # 记录大量日志数据
            large_message = "x" * 200
            for i in range(10):
                logging_service.info(f"Large message {i}: {large_message}")
            
            # 验证压缩文件存在
            # 在实际实现中，应该生成压缩的日志文件
    
    def test_log_rotation_by_time(self, logging_service: LoggingService):
        """测试按时间轮转日志"""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_file = os.path.join(temp_dir, "time_rotated.log")
            
            # 添加时间轮转处理器
            logging_service.add_timed_rotating_handler(
                log_file,
                when="S",  # 每秒轮转（用于测试）
                interval=1,
                backup_count=3
            )
            
            # 记录日志并等待轮转
            logging_service.info("Message before rotation")
            import time
            time.sleep(1.1)
            logging_service.info("Message after rotation")
            
            # 验证轮转文件
            # 在实际实现中，应该生成轮转的日志文件
    
    def test_get_log_statistics(self, logging_service: LoggingService, mock_logger):
        """测试获取日志统计信息"""
        # 记录不同级别的日志
        logging_service.debug("Debug message")
        logging_service.info("Info message")
        logging_service.warning("Warning message")
        logging_service.error("Error message")
        
        # 获取统计信息
        stats = logging_service.get_statistics()
        
        # 验证统计信息
        assert "total_logs" in stats
        assert "logs_by_level" in stats
        assert "start_time" in stats
    
    def test_export_logs(self, logging_service: LoggingService):
        """测试导出日志"""
        with tempfile.TemporaryDirectory() as temp_dir:
            export_file = os.path.join(temp_dir, "exported_logs.json")
            
            # 导出日志
            result = logging_service.export_logs(
                export_file,
                start_time=datetime.utcnow() - timedelta(hours=1),
                end_time=datetime.utcnow(),
                level="INFO"
            )
            
            # 验证导出结果
            assert result is True
    
    def test_search_logs(self, logging_service: LoggingService):
        """测试搜索日志"""
        # 搜索日志
        search_results = logging_service.search_logs(
            query="error",
            start_time=datetime.utcnow() - timedelta(hours=1),
            end_time=datetime.utcnow(),
            limit=100
        )
        
        # 验证搜索结果
        assert isinstance(search_results, list)


@pytest.mark.service
@pytest.mark.unit
class TestLoggingServiceEdgeCases(MockTestCase):
    """日志服务边界情况测试类"""
    
    @pytest.fixture
    def logging_service(self):
        """创建日志服务实例"""
        return LoggingService(name="edge_case_logger")
    
    def test_log_with_none_message(self, logging_service: LoggingService):
        """测试记录None消息"""
        # 尝试记录None消息
        with pytest.raises(LoggingError) as exc_info:
            logging_service.info(None)
        
        assert "Invalid log message" in str(exc_info.value)
    
    def test_log_with_empty_message(self, logging_service: LoggingService):
        """测试记录空消息"""
        # 记录空消息
        logging_service.info("")
        
        # 应该成功记录（空消息是有效的）
    
    def test_log_with_very_long_message(self, logging_service: LoggingService, memory_monitor):
        """测试记录超长消息"""
        with memory_monitor:
            # 创建超长消息（10MB）
            very_long_message = "x" * (10 * 1024 * 1024)
            
            # 记录超长消息
            logging_service.info(very_long_message)
        
        # 验证内存使用合理
        memory_usage = memory_monitor.get_peak_memory()
        assert memory_usage < 50 * 1024 * 1024  # 小于50MB
    
    def test_log_with_unicode_characters(self, logging_service: LoggingService):
        """测试记录Unicode字符"""
        # 记录包含各种Unicode字符的消息
        unicode_messages = [
            "测试中文日志记录",
            "🚀 Emoji in logs",
            "Ñoño español",
            "Русский текст",
            "العربية",
            "日本語のログ"
        ]
        
        for message in unicode_messages:
            logging_service.info(message)
        
        # 应该成功记录所有Unicode消息
    
    def test_log_with_special_characters(self, logging_service: LoggingService):
        """测试记录特殊字符"""
        # 记录包含特殊字符的消息
        special_messages = [
            "Message with \n newline",
            "Message with \t tab",
            "Message with \r carriage return",
            "Message with \" quotes",
            "Message with \\ backslash",
            "Message with null \x00 character"
        ]
        
        for message in special_messages:
            logging_service.info(message)
        
        # 应该成功处理所有特殊字符
    
    def test_concurrent_logging(self, logging_service: LoggingService):
        """测试并发日志记录"""
        import threading
        import time
        
        errors = []
        
        def log_worker(worker_id):
            try:
                for i in range(100):
                    logging_service.info(f"Worker {worker_id} - Message {i}")
                    time.sleep(0.001)  # 模拟处理时间
            except Exception as e:
                errors.append(e)
        
        # 创建多个线程并发记录日志
        threads = []
        for i in range(10):
            thread = threading.Thread(target=log_worker, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证没有错误
        assert len(errors) == 0
    
    def test_log_handler_failure(self, logging_service: LoggingService):
        """测试日志处理器失败"""
        # 创建会失败的处理器
        failing_handler = Mock()
        failing_handler.emit.side_effect = Exception("Handler failed")
        
        # 添加失败的处理器
        logging_service.logger.addHandler(failing_handler)
        
        # 记录日志（应该处理处理器失败）
        logging_service.info("Message with failing handler")
        
        # 验证处理器被调用但失败被处理
        failing_handler.emit.assert_called_once()
    
    def test_log_disk_space_exhaustion(self, logging_service: LoggingService):
        """测试磁盘空间耗尽"""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_file = os.path.join(temp_dir, "disk_full.log")
            
            # 模拟磁盘空间不足
            with patch('builtins.open', side_effect=OSError("No space left on device")):
                # 尝试添加文件处理器
                with pytest.raises(LoggingError) as exc_info:
                    logging_service.add_file_handler(log_file)
                
                assert "disk space" in str(exc_info.value).lower() or "no space" in str(exc_info.value).lower()
    
    def test_log_permission_denied(self, logging_service: LoggingService):
        """测试日志文件权限被拒绝"""
        # 模拟权限被拒绝
        with patch('builtins.open', side_effect=PermissionError("Permission denied")):
            # 尝试添加文件处理器
            with pytest.raises(LoggingError) as exc_info:
                logging_service.add_file_handler("/root/permission_denied.log")
            
            assert "permission" in str(exc_info.value).lower()
    
    def test_log_circular_reference_in_data(self, logging_service: LoggingService):
        """测试日志数据中的循环引用"""
        # 创建循环引用的数据
        data = {"key": "value"}
        data["self"] = data  # 循环引用
        
        # 尝试记录包含循环引用的数据
        logging_service.info("Message with circular reference", extra={"data": data})
        
        # 应该处理循环引用而不崩溃
    
    def test_log_memory_pressure(self, logging_service: LoggingService, memory_monitor):
        """测试内存压力下的日志记录"""
        with memory_monitor:
            # 在内存压力下记录大量日志
            large_data = "x" * (1024 * 1024)  # 1MB per log
            
            for i in range(100):
                logging_service.info(f"Large log {i}", extra={"data": large_data})
        
        # 验证内存使用合理
        memory_usage = memory_monitor.get_peak_memory()
        assert memory_usage < 200 * 1024 * 1024  # 小于200MB
    
    def test_log_performance_under_load(self, logging_service: LoggingService, performance_timer):
        """测试高负载下的日志性能"""
        with performance_timer:
            # 记录大量日志
            for i in range(10000):
                logging_service.info(f"Performance test message {i}")
        
        # 验证性能
        elapsed_time = performance_timer.elapsed
        assert elapsed_time < 5.0  # 10000条日志应该在5秒内完成
    
    def test_log_format_injection_attack(self, logging_service: LoggingService):
        """测试日志格式注入攻击"""
        # 尝试日志格式注入
        malicious_messages = [
            "User input: %(password)s",
            "Malicious: {user.__class__.__bases__[0].__subclasses__()}",
            "Injection: %(__import__)s",
            "Format: {0.__class__.__bases__[0].__subclasses__()}"
        ]
        
        for message in malicious_messages:
            # 记录潜在恶意消息
            logging_service.info(message)
        
        # 应该安全处理而不执行恶意代码
    
    def test_log_configuration_reload(self, logging_service: LoggingService):
        """测试日志配置重新加载"""
        # 获取初始配置
        initial_level = logging_service.level
        initial_handlers = len(logging_service.logger.handlers)
        
        # 修改配置
        new_config = {
            "level": "DEBUG",
            "handlers": [
                {"type": "console", "level": "INFO"},
                {"type": "file", "filename": "/tmp/test.log", "level": "WARNING"}
            ]
        }
        
        # 重新加载配置
        logging_service.reload_configuration(new_config)
        
        # 验证配置更新
        assert logging_service.level != initial_level
    
    def test_log_cleanup_old_files(self, logging_service: LoggingService):
        """测试清理旧日志文件"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建一些旧日志文件
            old_files = []
            for i in range(5):
                file_path = os.path.join(temp_dir, f"old_log_{i}.log")
                with open(file_path, 'w') as f:
                    f.write(f"Old log content {i}")
                old_files.append(file_path)
                
                # 修改文件时间为过去
                old_time = time.time() - (i + 1) * 24 * 3600  # i+1 days ago
                os.utime(file_path, (old_time, old_time))
            
            # 清理超过2天的日志文件
            cleaned_count = logging_service.cleanup_old_logs(temp_dir, days=2)
            
            # 验证清理结果
            assert cleaned_count >= 3  # 应该清理至少3个文件
    
    def test_log_archiving(self, logging_service: LoggingService):
        """测试日志归档"""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_dir = os.path.join(temp_dir, "logs")
            archive_dir = os.path.join(temp_dir, "archive")
            os.makedirs(log_dir)
            os.makedirs(archive_dir)
            
            # 创建一些日志文件
            log_files = []
            for i in range(3):
                file_path = os.path.join(log_dir, f"app_{i}.log")
                with open(file_path, 'w') as f:
                    f.write(f"Log content {i}" * 1000)
                log_files.append(file_path)
            
            # 归档日志文件
            archive_path = logging_service.archive_logs(log_dir, archive_dir)
            
            # 验证归档结果
            assert os.path.exists(archive_path)
            assert archive_path.endswith('.tar.gz') or archive_path.endswith('.zip')
    
    def test_log_monitoring_and_alerting(self, logging_service: LoggingService):
        """测试日志监控和告警"""
        alerts = []
        
        def alert_handler(alert_data):
            alerts.append(alert_data)
        
        # 设置告警规则
        logging_service.add_alert_rule(
            name="error_rate",
            condition="error_count > 10 in 60s",
            handler=alert_handler
        )
        
        # 触发大量错误日志
        for i in range(15):
            logging_service.error(f"Error message {i}")
        
        # 验证告警触发
        # 在实际实现中，应该触发告警
        # assert len(alerts) > 0
    
    def test_log_anonymization(self, logging_service: LoggingService):
        """测试日志匿名化"""
        # 启用日志匿名化
        logging_service.enable_anonymization([
            r'\b\d{4}-\d{4}-\d{4}-\d{4}\b',  # 信用卡号
            r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',  # 邮箱
            r'\b\d{3}-\d{2}-\d{4}\b'  # SSN
        ])
        
        # 记录包含敏感信息的日志
        sensitive_message = "User email: <EMAIL>, SSN: ***********, Card: 1234-5678-9012-3456"
        logging_service.info(sensitive_message)
        
        # 验证敏感信息被匿名化
        # 在实际实现中，敏感信息应该被替换为占位符