"""模型管理器单元测试"""

import asyncio
from unittest.mock import Mock, AsyncMock

import pytest

from tests.utils.assertions import (
    assert_model_response_format,
    assert_performance_metrics
)
from tests.utils.test_helpers import MockTestCase


class TestModelManager(MockTestCase):
    """模型管理器测试类"""
    
    def setup_method(self):
        """设置测试方法"""
        self.mock_openai_client = AsyncMock()
        self.mock_anthropic_client = AsyncMock()
        self.mock_cache = AsyncMock()
        self.mock_config = Mock()
        
        # 配置模拟对象
        self.mock_config.openai_api_key = "test-openai-key"
        self.mock_config.anthropic_api_key = "test-anthropic-key"
        self.mock_config.model_timeout = 30
        self.mock_config.max_retries = 3
    
    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_available_models(self, mock_model_manager):
        """测试获取可用模型列表"""
        # 准备测试数据
        expected_models = [
            "gpt-3.5-turbo",
            "gpt-4",
            "claude-3-sonnet",
            "claude-3-haiku"
        ]
        
        # 设置模拟返回值
        mock_model_manager.get_available_models.return_value = expected_models
        
        # 执行测试
        models = await mock_model_manager.get_available_models()
        
        # 验证结果
        assert isinstance(models, list)
        assert len(models) > 0
        assert all(isinstance(model, str) for model in models)
        assert "gpt-3.5-turbo" in models
        assert "claude-3-sonnet" in models
    
    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_generate_response_openai(self, mock_model_manager, mock_openai_response):
        """测试OpenAI模型响应生成"""
        # 准备测试数据
        model = "gpt-3.5-turbo"
        messages = [
            {"role": "user", "content": "Hello, how are you?"}
        ]
        parameters = {
            "temperature": 0.7,
            "max_tokens": 100
        }
        
        # 设置模拟返回值
        mock_model_manager.generate_response.return_value = mock_openai_response
        
        # 执行测试
        response = await mock_model_manager.generate_response(
            model=model,
            messages=messages,
            **parameters
        )
        
        # 验证结果
        assert_model_response_format(response)
        assert response["model"] == model
        assert "content" in response
        assert "usage" in response
        assert response["usage"]["total_tokens"] > 0
    
    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_generate_response_anthropic(self, mock_model_manager, mock_anthropic_response):
        """测试Anthropic模型响应生成"""
        # 准备测试数据
        model = "claude-3-sonnet"
        messages = [
            {"role": "user", "content": "Explain quantum computing"}
        ]
        parameters = {
            "temperature": 0.5,
            "max_tokens": 200
        }
        
        # 设置模拟返回值
        mock_model_manager.generate_response.return_value = mock_anthropic_response
        
        # 执行测试
        response = await mock_model_manager.generate_response(
            model=model,
            messages=messages,
            **parameters
        )
        
        # 验证结果
        assert_model_response_format(response)
        assert response["model"] == model
        assert "content" in response
        assert "usage" in response
        assert response["usage"]["total_tokens"] > 0
    
    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_generate_response_with_invalid_model(self, mock_model_manager):
        """测试使用无效模型生成响应"""
        # 准备测试数据
        invalid_model = "invalid-model"
        messages = [
            {"role": "user", "content": "Test message"}
        ]
        
        # 设置模拟异常
        mock_model_manager.generate_response.side_effect = ValueError(f"不支持的模型: {invalid_model}")
        
        # 执行测试并验证异常
        with pytest.raises(ValueError) as exc_info:
            await mock_model_manager.generate_response(
                model=invalid_model,
                messages=messages
            )
        
        assert "不支持的模型" in str(exc_info.value)
    
    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_generate_response_with_empty_messages(self, mock_model_manager):
        """测试使用空消息列表生成响应"""
        # 准备测试数据
        model = "gpt-3.5-turbo"
        empty_messages = []
        
        # 设置模拟异常
        mock_model_manager.generate_response.side_effect = ValueError("消息列表不能为空")
        
        # 执行测试并验证异常
        with pytest.raises(ValueError) as exc_info:
            await mock_model_manager.generate_response(
                model=model,
                messages=empty_messages
            )
        
        assert "消息列表不能为空" in str(exc_info.value)
    
    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_validate_api_key_valid(self, mock_model_manager):
        """测试验证有效API密钥"""
        # 准备测试数据
        provider = "openai"
        api_key = "sk-valid-api-key"
        
        # 设置模拟返回值
        mock_model_manager.validate_api_key.return_value = True
        
        # 执行测试
        is_valid = await mock_model_manager.validate_api_key(provider, api_key)
        
        # 验证结果
        assert is_valid is True
    
    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_validate_api_key_invalid(self, mock_model_manager):
        """测试验证无效API密钥"""
        # 准备测试数据
        provider = "openai"
        invalid_api_key = "invalid-key"
        
        # 设置模拟返回值
        mock_model_manager.validate_api_key.return_value = False
        
        # 执行测试
        is_valid = await mock_model_manager.validate_api_key(provider, invalid_api_key)
        
        # 验证结果
        assert is_valid is False
    
    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_model_info(self, mock_model_manager):
        """测试获取模型信息"""
        # 准备测试数据
        model = "gpt-3.5-turbo"
        expected_info = {
            "name": "gpt-3.5-turbo",
            "provider": "openai",
            "max_tokens": 4096,
            "supports_streaming": True,
            "cost_per_token": 0.002
        }
        
        # 设置模拟返回值
        mock_model_manager.get_model_info.return_value = expected_info
        
        # 执行测试
        model_info = await mock_model_manager.get_model_info(model)
        
        # 验证结果
        assert isinstance(model_info, dict)
        assert model_info["name"] == model
        assert "provider" in model_info
        assert "max_tokens" in model_info
        assert "supports_streaming" in model_info
    
    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_model_info_unknown_model(self, mock_model_manager):
        """测试获取未知模型信息"""
        # 准备测试数据
        unknown_model = "unknown-model"
        
        # 设置模拟异常
        mock_model_manager.get_model_info.side_effect = ValueError(f"未知模型: {unknown_model}")
        
        # 执行测试并验证异常
        with pytest.raises(ValueError) as exc_info:
            await mock_model_manager.get_model_info(unknown_model)
        
        assert "未知模型" in str(exc_info.value)
    
    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_generate_response_with_timeout(self, mock_model_manager):
        """测试响应生成超时"""
        # 准备测试数据
        model = "gpt-4"
        messages = [
            {"role": "user", "content": "Complex question that might timeout"}
        ]
        
        # 设置模拟超时异常
        mock_model_manager.generate_response.side_effect = asyncio.TimeoutError("请求超时")
        
        # 执行测试并验证异常
        with pytest.raises(asyncio.TimeoutError):
            await mock_model_manager.generate_response(
                model=model,
                messages=messages,
                timeout=1  # 1秒超时
            )
    
    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_generate_response_with_retry(self, mock_model_manager):
        """测试响应生成重试机制"""
        # 准备测试数据
        model = "gpt-3.5-turbo"
        messages = [
            {"role": "user", "content": "Test retry mechanism"}
        ]
        
        # 模拟前两次失败，第三次成功
        mock_response = {
            "content": "Success after retry",
            "model": model,
            "usage": {"total_tokens": 50}
        }
        
        mock_model_manager.generate_response.side_effect = [
            Exception("Network error"),
            Exception("Rate limit"),
            mock_response
        ]
        
        # 执行测试
        response = await mock_model_manager.generate_response(
            model=model,
            messages=messages,
            max_retries=3
        )
        
        # 验证结果
        assert response["content"] == "Success after retry"
        assert mock_model_manager.generate_response.call_count == 3
    
    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_generate_streaming_response(self, mock_model_manager):
        """测试流式响应生成"""
        # 准备测试数据
        model = "gpt-3.5-turbo"
        messages = [
            {"role": "user", "content": "Generate streaming response"}
        ]
        
        # 模拟流式响应
        async def mock_stream():
            chunks = [
                {"content": "Hello", "finish_reason": None},
                {"content": " world", "finish_reason": None},
                {"content": "!", "finish_reason": "stop"}
            ]
            for chunk in chunks:
                yield chunk
        
        mock_model_manager.generate_streaming_response.return_value = mock_stream()
        
        # 执行测试
        full_content = ""
        async for chunk in mock_model_manager.generate_streaming_response(
            model=model,
            messages=messages,
            stream=True
        ):
            if chunk.get("content"):
                full_content += chunk["content"]
        
        # 验证结果
        assert full_content == "Hello world!"
    
    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_calculate_cost(self, mock_model_manager):
        """测试计算使用成本"""
        # 准备测试数据
        model = "gpt-3.5-turbo"
        usage = {
            "prompt_tokens": 100,
            "completion_tokens": 50,
            "total_tokens": 150
        }
        
        expected_cost = 0.0003  # 假设的成本
        
        # 设置模拟返回值
        mock_model_manager.calculate_cost.return_value = expected_cost
        
        # 执行测试
        cost = await mock_model_manager.calculate_cost(model, usage)
        
        # 验证结果
        assert isinstance(cost, (int, float))
        assert cost >= 0
        assert cost == expected_cost
    
    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_get_usage_statistics(self, mock_model_manager):
        """测试获取使用统计"""
        # 准备测试数据
        expected_stats = {
            "total_requests": 100,
            "total_tokens": 50000,
            "total_cost": 1.25,
            "average_response_time": 2.5,
            "models_used": {
                "gpt-3.5-turbo": 60,
                "claude-3-sonnet": 40
            }
        }
        
        # 设置模拟返回值
        mock_model_manager.get_usage_statistics.return_value = expected_stats
        
        # 执行测试
        stats = await mock_model_manager.get_usage_statistics()
        
        # 验证结果
        assert isinstance(stats, dict)
        assert "total_requests" in stats
        assert "total_tokens" in stats
        assert "total_cost" in stats
        assert "models_used" in stats
        assert stats["total_requests"] >= 0
        assert stats["total_tokens"] >= 0
        assert stats["total_cost"] >= 0
    
    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_reset_statistics(self, mock_model_manager):
        """测试重置统计信息"""
        # 设置模拟返回值
        mock_model_manager.reset_statistics.return_value = True
        
        # 执行测试
        result = await mock_model_manager.reset_statistics()
        
        # 验证结果
        assert result is True
        mock_model_manager.reset_statistics.assert_called_once()
    
    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_batch_generate_responses(self, mock_model_manager):
        """测试批量生成响应"""
        # 准备测试数据
        requests = [
            {
                "model": "gpt-3.5-turbo",
                "messages": [{"role": "user", "content": "Question 1"}]
            },
            {
                "model": "claude-3-sonnet",
                "messages": [{"role": "user", "content": "Question 2"}]
            }
        ]
        
        expected_responses = [
            {
                "content": "Answer 1",
                "model": "gpt-3.5-turbo",
                "usage": {"total_tokens": 30}
            },
            {
                "content": "Answer 2",
                "model": "claude-3-sonnet",
                "usage": {"total_tokens": 35}
            }
        ]
        
        # 设置模拟返回值
        mock_model_manager.batch_generate_responses.return_value = expected_responses
        
        # 执行测试
        responses = await mock_model_manager.batch_generate_responses(requests)
        
        # 验证结果
        assert isinstance(responses, list)
        assert len(responses) == len(requests)
        for response in responses:
            assert_model_response_format(response)
    
    @pytest.mark.unit
    @pytest.mark.performance
    async def test_performance_metrics(self, mock_model_manager, performance_timer):
        """测试性能指标"""
        # 准备测试数据
        model = "gpt-3.5-turbo"
        messages = [
            {"role": "user", "content": "Performance test"}
        ]
        
        mock_response = {
            "content": "Performance response",
            "model": model,
            "usage": {"total_tokens": 25}
        }
        
        # 设置模拟返回值
        mock_model_manager.generate_response.return_value = mock_response
        
        # 执行性能测试
        with performance_timer:
            response = await mock_model_manager.generate_response(
                model=model,
                messages=messages
            )
        
        # 验证性能指标
        assert_performance_metrics(
            response_time=performance_timer.elapsed_time,
            max_response_time=5.0,
            token_count=response["usage"]["total_tokens"],
            min_token_efficiency=0.5
        )


@pytest.mark.unit
class TestModelManagerEdgeCases:
    """模型管理器边界情况测试"""
    
    @pytest.mark.asyncio
    async def test_concurrent_requests(self, mock_model_manager):
        """测试并发请求处理"""
        # 准备测试数据
        model = "gpt-3.5-turbo"
        messages = [{"role": "user", "content": "Concurrent test"}]
        
        mock_response = {
            "content": "Concurrent response",
            "model": model,
            "usage": {"total_tokens": 20}
        }
        
        # 设置模拟返回值
        mock_model_manager.generate_response.return_value = mock_response
        
        # 创建并发任务
        tasks = [
            mock_model_manager.generate_response(model=model, messages=messages)
            for _ in range(10)
        ]
        
        # 执行并发测试
        responses = await asyncio.gather(*tasks)
        
        # 验证结果
        assert len(responses) == 10
        for response in responses:
            assert response["content"] == "Concurrent response"
    
    @pytest.mark.asyncio
    async def test_large_message_handling(self, mock_model_manager):
        """测试大消息处理"""
        # 准备大消息测试数据
        large_content = "A" * 10000  # 10K字符
        model = "gpt-3.5-turbo"
        messages = [{"role": "user", "content": large_content}]
        
        mock_response = {
            "content": "Large message processed",
            "model": model,
            "usage": {"total_tokens": 2500}
        }
        
        # 设置模拟返回值
        mock_model_manager.generate_response.return_value = mock_response
        
        # 执行测试
        response = await mock_model_manager.generate_response(
            model=model,
            messages=messages
        )
        
        # 验证结果
        assert response["content"] == "Large message processed"
        assert response["usage"]["total_tokens"] > 1000
    
    @pytest.mark.asyncio
    async def test_special_characters_handling(self, mock_model_manager):
        """测试特殊字符处理"""
        # 准备包含特殊字符的测试数据
        special_content = "Hello! 你好 🌟 \n\t\r Special chars: @#$%^&*()"
        model = "gpt-3.5-turbo"
        messages = [{"role": "user", "content": special_content}]
        
        mock_response = {
            "content": "Special characters handled",
            "model": model,
            "usage": {"total_tokens": 30}
        }
        
        # 设置模拟返回值
        mock_model_manager.generate_response.return_value = mock_response
        
        # 执行测试
        response = await mock_model_manager.generate_response(
            model=model,
            messages=messages
        )
        
        # 验证结果
        assert response["content"] == "Special characters handled"
    
    @pytest.mark.asyncio
    async def test_memory_usage_monitoring(self, mock_model_manager, memory_monitor):
        """测试内存使用监控"""
        # 开始内存监控
        memory_monitor.start_monitoring()
        
        # 准备测试数据
        model = "gpt-4"
        messages = [{"role": "user", "content": "Memory test"}]
        
        mock_response = {
            "content": "Memory response",
            "model": model,
            "usage": {"total_tokens": 40}
        }
        
        # 设置模拟返回值
        mock_model_manager.generate_response.return_value = mock_response
        
        # 执行测试
        response = await mock_model_manager.generate_response(
            model=model,
            messages=messages
        )
        
        # 停止内存监控
        memory_usage = memory_monitor.stop_monitoring()
        
        # 验证内存使用
        assert memory_usage["peak_memory"] > 0
        assert memory_usage["average_memory"] > 0
        assert memory_usage["peak_memory"] >= memory_usage["average_memory"]