"""数据库服务单元测试"""

import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
import uuid
import json

from services.database_service import DatabaseService
from exceptions.api_exceptions import DatabaseError, ValidationAPIError
from tests.utils.test_helpers import AsyncTestCase, MockTestCase


@pytest.mark.service
@pytest.mark.unit
class TestDatabaseService(MockTestCase):
    """数据库服务测试类"""
    
    @pytest.fixture
    def mock_engine(self):
        """模拟数据库引擎"""
        engine = Mock()
        engine.connect.return_value.__enter__ = Mock()
        engine.connect.return_value.__exit__ = Mock()
        engine.execute = Mock()
        engine.dispose = Mock()
        return engine
    
    @pytest.fixture
    def mock_session_factory(self):
        """模拟会话工厂"""
        session = Mock()
        session.query.return_value = session
        session.filter.return_value = session
        session.filter_by.return_value = session
        session.first.return_value = None
        session.all.return_value = []
        session.count.return_value = 0
        session.add = Mock()
        session.commit = Mock()
        session.rollback = Mock()
        session.close = Mock()
        session.delete = Mock()
        session.merge = Mock()
        session.flush = Mock()
        session.refresh = Mock()
        
        factory = Mock()
        factory.return_value = session
        factory.return_value.__enter__ = Mock(return_value=session)
        factory.return_value.__exit__ = Mock()
        return factory
    
    @pytest.fixture
    def database_service(self, mock_engine, mock_session_factory):
        """创建数据库服务实例"""
        with patch('services.database_service.create_engine', return_value=mock_engine), \
             patch('services.database_service.sessionmaker', return_value=mock_session_factory):
            service = DatabaseService(database_url="sqlite:///test.db")
            service.engine = mock_engine
            service.SessionLocal = mock_session_factory
            return service
        return None
        return None

    @pytest.fixture
    def sample_model_data(self):
        """示例模型数据"""
        return {
            "id": str(uuid.uuid4()),
            "name": "Test Model",
            "provider": "openai",
            "model_type": "chat",
            "is_active": True,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
    
    def test_create_connection_success(self, database_service: DatabaseService, mock_engine):
        """测试成功创建数据库连接"""
        # 测试连接创建
        connection = database_service.get_connection()
        
        # 验证连接
        assert connection is not None
        mock_engine.connect.assert_called_once()
    
    def test_create_session_success(self, database_service: DatabaseService, mock_session_factory):
        """测试成功创建数据库会话"""
        # 创建会话
        session = database_service.get_session()
        
        # 验证会话
        assert session is not None
        mock_session_factory.assert_called_once()
    
    def test_execute_query_success(self, database_service: DatabaseService, mock_session_factory):
        """测试成功执行查询"""
        mock_session = mock_session_factory.return_value
        mock_result = Mock()
        mock_result.fetchall.return_value = [(1, "test"), (2, "test2")]
        mock_session.execute.return_value = mock_result
        
        # 执行查询
        query = "SELECT * FROM test_table"
        result = database_service.execute_query(query)
        
        # 验证结果
        assert len(result) == 2
        assert result[0] == (1, "test")
        assert result[1] == (2, "test2")
        mock_session.execute.assert_called_once_with(query)
    
    def test_execute_query_with_parameters(self, database_service: DatabaseService, mock_session_factory):
        """测试执行带参数的查询"""
        mock_session = mock_session_factory.return_value
        mock_result = Mock()
        mock_result.fetchall.return_value = [(1, "test")]
        mock_session.execute.return_value = mock_result
        
        # 执行带参数的查询
        query = "SELECT * FROM test_table WHERE id = :id"
        params = {"id": 1}
        result = database_service.execute_query(query, params)
        
        # 验证结果
        assert len(result) == 1
        mock_session.execute.assert_called_once_with(query, params)
    
    def test_execute_query_error(self, database_service: DatabaseService, mock_session_factory):
        """测试查询执行错误"""
        from sqlalchemy.exc import SQLAlchemyError
        
        mock_session = mock_session_factory.return_value
        mock_session.execute.side_effect = SQLAlchemyError("Database error")
        
        # 尝试执行查询
        with pytest.raises(DatabaseError) as exc_info:
            database_service.execute_query("SELECT * FROM test_table")
        
        assert "Database error" in str(exc_info.value)
        mock_session.rollback.assert_called_once()
    
    def test_insert_record_success(self, database_service: DatabaseService, mock_session_factory, sample_model_data):
        """测试成功插入记录"""
        mock_session = mock_session_factory.return_value
        
        # 插入记录
        result = database_service.insert_record("test_table", sample_model_data)
        
        # 验证结果
        assert result is True
        mock_session.add.assert_called_once()
        mock_session.commit.assert_called_once()
    
    def test_update_record_success(self, database_service: DatabaseService, mock_session_factory, sample_model_data):
        """测试成功更新记录"""
        mock_session = mock_session_factory.return_value
        mock_record = Mock()
        mock_session.query.return_value.filter.return_value.first.return_value = mock_record
        
        # 更新记录
        update_data = {"name": "Updated Model"}
        result = database_service.update_record("test_table", sample_model_data["id"], update_data)
        
        # 验证结果
        assert result is True
        mock_session.commit.assert_called_once()
    
    def test_update_record_not_found(self, database_service: DatabaseService, mock_session_factory):
        """测试更新不存在的记录"""
        mock_session = mock_session_factory.return_value
        mock_session.query.return_value.filter.return_value.first.return_value = None
        
        # 尝试更新不存在的记录
        update_data = {"name": "Updated Model"}
        result = database_service.update_record("test_table", "nonexistent-id", update_data)
        
        # 验证结果
        assert result is False
    
    def test_delete_record_success(self, database_service: DatabaseService, mock_session_factory, sample_model_data):
        """测试成功删除记录"""
        mock_session = mock_session_factory.return_value
        mock_record = Mock()
        mock_session.query.return_value.filter.return_value.first.return_value = mock_record
        
        # 删除记录
        result = database_service.delete_record("test_table", sample_model_data["id"])
        
        # 验证结果
        assert result is True
        mock_session.delete.assert_called_once_with(mock_record)
        mock_session.commit.assert_called_once()
    
    def test_delete_record_not_found(self, database_service: DatabaseService, mock_session_factory):
        """测试删除不存在的记录"""
        mock_session = mock_session_factory.return_value
        mock_session.query.return_value.filter.return_value.first.return_value = None
        
        # 尝试删除不存在的记录
        result = database_service.delete_record("test_table", "nonexistent-id")
        
        # 验证结果
        assert result is False
    
    def test_get_record_by_id_success(self, database_service: DatabaseService, mock_session_factory, sample_model_data):
        """测试成功根据ID获取记录"""
        mock_session = mock_session_factory.return_value
        mock_record = Mock()
        mock_record.to_dict.return_value = sample_model_data
        mock_session.query.return_value.filter.return_value.first.return_value = mock_record
        
        # 获取记录
        result = database_service.get_record_by_id("test_table", sample_model_data["id"])
        
        # 验证结果
        assert result == sample_model_data
    
    def test_get_record_by_id_not_found(self, database_service: DatabaseService, mock_session_factory):
        """测试获取不存在的记录"""
        mock_session = mock_session_factory.return_value
        mock_session.query.return_value.filter.return_value.first.return_value = None
        
        # 获取不存在的记录
        result = database_service.get_record_by_id("test_table", "nonexistent-id")
        
        # 验证结果
        assert result is None
    
    def test_get_all_records(self, database_service: DatabaseService, mock_session_factory, sample_model_data):
        """测试获取所有记录"""
        mock_session = mock_session_factory.return_value
        mock_record = Mock()
        mock_record.to_dict.return_value = sample_model_data
        mock_session.query.return_value.all.return_value = [mock_record]
        
        # 获取所有记录
        result = database_service.get_all_records("test_table")
        
        # 验证结果
        assert len(result) == 1
        assert result[0] == sample_model_data
    
    def test_count_records(self, database_service: DatabaseService, mock_session_factory):
        """测试计数记录"""
        mock_session = mock_session_factory.return_value
        mock_session.query.return_value.count.return_value = 5
        
        # 计数记录
        result = database_service.count_records("test_table")
        
        # 验证结果
        assert result == 5
    
    def test_filter_records(self, database_service: DatabaseService, mock_session_factory, sample_model_data):
        """测试过滤记录"""
        mock_session = mock_session_factory.return_value
        mock_record = Mock()
        mock_record.to_dict.return_value = sample_model_data
        mock_session.query.return_value.filter_by.return_value.all.return_value = [mock_record]
        
        # 过滤记录
        filters = {"provider": "openai", "is_active": True}
        result = database_service.filter_records("test_table", filters)
        
        # 验证结果
        assert len(result) == 1
        assert result[0] == sample_model_data
    
    def test_paginate_records(self, database_service: DatabaseService, mock_session_factory, sample_model_data):
        """测试分页记录"""
        mock_session = mock_session_factory.return_value
        mock_query = mock_session.query.return_value
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        
        mock_record = Mock()
        mock_record.to_dict.return_value = sample_model_data
        mock_query.all.return_value = [mock_record]
        mock_session.query.return_value.count.return_value = 10
        
        # 分页记录
        result, total = database_service.paginate_records("test_table", page=1, page_size=5)
        
        # 验证结果
        assert len(result) == 1
        assert total == 10
        mock_query.offset.assert_called_once_with(0)
        mock_query.limit.assert_called_once_with(5)
    
    def test_search_records(self, database_service: DatabaseService, mock_session_factory, sample_model_data):
        """测试搜索记录"""
        mock_session = mock_session_factory.return_value
        mock_record = Mock()
        mock_record.to_dict.return_value = sample_model_data
        mock_session.query.return_value.filter.return_value.all.return_value = [mock_record]
        
        # 搜索记录
        result = database_service.search_records("test_table", "name", "Test")
        
        # 验证结果
        assert len(result) == 1
        assert result[0] == sample_model_data
    
    def test_bulk_insert_success(self, database_service: DatabaseService, mock_session_factory):
        """测试批量插入成功"""
        mock_session = mock_session_factory.return_value
        
        # 批量插入数据
        records = [
            {"name": "Model 1", "provider": "openai"},
            {"name": "Model 2", "provider": "anthropic"},
            {"name": "Model 3", "provider": "google"}
        ]
        
        result = database_service.bulk_insert("test_table", records)
        
        # 验证结果
        assert result is True
        assert mock_session.add.call_count == 3
        mock_session.commit.assert_called_once()
    
    def test_bulk_update_success(self, database_service: DatabaseService, mock_session_factory):
        """测试批量更新成功"""
        mock_session = mock_session_factory.return_value
        mock_records = [Mock(), Mock(), Mock()]
        mock_session.query.return_value.filter.return_value.all.return_value = mock_records
        
        # 批量更新数据
        record_ids = ["id1", "id2", "id3"]
        update_data = {"is_active": False}
        
        result = database_service.bulk_update("test_table", record_ids, update_data)
        
        # 验证结果
        assert result == 3
        mock_session.commit.assert_called_once()
    
    def test_bulk_delete_success(self, database_service: DatabaseService, mock_session_factory):
        """测试批量删除成功"""
        mock_session = mock_session_factory.return_value
        mock_records = [Mock(), Mock(), Mock()]
        mock_session.query.return_value.filter.return_value.all.return_value = mock_records
        
        # 批量删除数据
        record_ids = ["id1", "id2", "id3"]
        
        result = database_service.bulk_delete("test_table", record_ids)
        
        # 验证结果
        assert result == 3
        assert mock_session.delete.call_count == 3
        mock_session.commit.assert_called_once()
    
    def test_transaction_success(self, database_service: DatabaseService, mock_session_factory):
        """测试事务成功"""
        mock_session = mock_session_factory.return_value
        
        # 使用事务
        def transaction_operations(session):
            session.add(Mock())
            session.add(Mock())
            return True
        
        result = database_service.execute_transaction(transaction_operations)
        
        # 验证结果
        assert result is True
        mock_session.commit.assert_called_once()
    
    def test_transaction_rollback(self, database_service: DatabaseService, mock_session_factory):
        """测试事务回滚"""
        mock_session = mock_session_factory.return_value
        
        # 使用事务（抛出异常）
        def transaction_operations(session):
            session.add(Mock())
            raise Exception("Transaction error")
        
        with pytest.raises(Exception):
            database_service.execute_transaction(transaction_operations)
        
        # 验证回滚
        mock_session.rollback.assert_called_once()
    
    def test_backup_database(self, database_service: DatabaseService, mock_engine):
        """测试数据库备份"""
        backup_path = "/tmp/test_backup.sql"
        
        # 执行备份
        result = database_service.backup_database(backup_path)
        
        # 验证结果
        assert result is True
    
    def test_restore_database(self, database_service: DatabaseService, mock_engine):
        """测试数据库恢复"""
        backup_path = "/tmp/test_backup.sql"
        
        # 执行恢复
        result = database_service.restore_database(backup_path)
        
        # 验证结果
        assert result is True
    
    def test_get_database_info(self, database_service: DatabaseService, mock_engine):
        """测试获取数据库信息"""
        # 获取数据库信息
        info = database_service.get_database_info()
        
        # 验证结果
        assert "database_url" in info
        assert "engine_info" in info
        assert "connection_pool" in info
    
    def test_check_connection_health(self, database_service: DatabaseService, mock_engine):
        """测试检查连接健康状态"""
        # 检查连接健康状态
        is_healthy = database_service.check_connection_health()
        
        # 验证结果
        assert is_healthy is True
    
    def test_optimize_database(self, database_service: DatabaseService, mock_session_factory):
        """测试数据库优化"""
        mock_session = mock_session_factory.return_value
        
        # 执行数据库优化
        result = database_service.optimize_database()
        
        # 验证结果
        assert result is True
    
    def test_get_table_statistics(self, database_service: DatabaseService, mock_session_factory):
        """测试获取表统计信息"""
        mock_session = mock_session_factory.return_value
        mock_result = Mock()
        mock_result.fetchone.return_value = (100, 50, 25)
        mock_session.execute.return_value = mock_result
        
        # 获取表统计信息
        stats = database_service.get_table_statistics("test_table")
        
        # 验证结果
        assert "total_rows" in stats
        assert "active_rows" in stats
        assert "inactive_rows" in stats
    
    def test_create_index(self, database_service: DatabaseService, mock_session_factory):
        """测试创建索引"""
        mock_session = mock_session_factory.return_value
        
        # 创建索引
        result = database_service.create_index("test_table", "name", "idx_test_name")
        
        # 验证结果
        assert result is True
        mock_session.execute.assert_called_once()
        mock_session.commit.assert_called_once()
    
    def test_drop_index(self, database_service: DatabaseService, mock_session_factory):
        """测试删除索引"""
        mock_session = mock_session_factory.return_value
        
        # 删除索引
        result = database_service.drop_index("idx_test_name")
        
        # 验证结果
        assert result is True
        mock_session.execute.assert_called_once()
        mock_session.commit.assert_called_once()


@pytest.mark.service
@pytest.mark.unit
class TestDatabaseServiceEdgeCases(MockTestCase):
    """数据库服务边界情况测试类"""
    
    @pytest.fixture
    def mock_engine(self):
        """模拟数据库引擎"""
        engine = Mock()
        return engine
    
    @pytest.fixture
    def mock_session_factory(self):
        """模拟会话工厂"""
        session = Mock()
        factory = Mock(return_value=session)
        return factory
    
    @pytest.fixture
    def database_service(self, mock_engine, mock_session_factory):
        """创建数据库服务实例"""
        with patch('services.database_service.create_engine', return_value=mock_engine), \
             patch('services.database_service.sessionmaker', return_value=mock_session_factory):
            service = DatabaseService(database_url="sqlite:///test.db")
            service.engine = mock_engine
            service.SessionLocal = mock_session_factory
            return service
        return None
        return None

    def test_connection_pool_exhaustion(self, database_service: DatabaseService, mock_engine):
        """测试连接池耗尽"""
        from sqlalchemy.exc import TimeoutError
        
        # 模拟连接池耗尽
        mock_engine.connect.side_effect = TimeoutError("Connection pool exhausted")
        
        # 尝试获取连接
        with pytest.raises(DatabaseError) as exc_info:
            database_service.get_connection()
        
        assert "Connection pool exhausted" in str(exc_info.value)
    
    def test_database_lock_timeout(self, database_service: DatabaseService, mock_session_factory):
        """测试数据库锁超时"""
        from sqlalchemy.exc import OperationalError
        
        mock_session = mock_session_factory.return_value
        mock_session.commit.side_effect = OperationalError("Database is locked", None, None)
        
        # 尝试提交事务
        with pytest.raises(DatabaseError) as exc_info:
            database_service.insert_record("test_table", {"name": "test"})
        
        assert "Database is locked" in str(exc_info.value)
    
    def test_large_dataset_handling(self, database_service: DatabaseService, mock_session_factory, memory_monitor):
        """测试大数据集处理"""
        mock_session = mock_session_factory.return_value
        
        # 创建大数据集
        large_dataset = []
        for i in range(100000):
            record = {
                "id": str(uuid.uuid4()),
                "name": f"Record {i}",
                "data": "x" * 1000  # 1KB per record
            }
            large_dataset.append(record)
        
        with memory_monitor:
            # 批量插入大数据集
            database_service.bulk_insert("test_table", large_dataset)
        
        # 验证内存使用在合理范围内
        memory_usage = memory_monitor.get_peak_memory()
        assert memory_usage < 500 * 1024 * 1024  # 小于500MB
    
    def test_concurrent_database_operations(self, database_service: DatabaseService, mock_session_factory):
        """测试并发数据库操作"""
        import threading
        import time
        
        mock_session = mock_session_factory.return_value
        results = []
        errors = []
        
        def database_operation(index):
            try:
                # 执行数据库操作
                data = {"name": f"Record {index}", "value": index}
                result = database_service.insert_record("test_table", data)
                results.append(result)
            except Exception as e:
                errors.append(e)
        
        # 创建多个线程同时执行数据库操作
        threads = []
        for i in range(50):
            thread = threading.Thread(target=database_operation, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证结果
        assert len(errors) == 0
        assert len(results) == 50
    
    def test_sql_injection_prevention(self, database_service: DatabaseService, mock_session_factory):
        """测试SQL注入防护"""
        mock_session = mock_session_factory.return_value
        
        # 尝试SQL注入攻击
        malicious_input = "'; DROP TABLE test_table; --"
        
        # 执行查询（应该被参数化查询保护）
        try:
            database_service.search_records("test_table", "name", malicious_input)
        except Exception:
            pass  # 预期可能抛出异常，但不应该执行恶意SQL
        
        # 验证没有执行DROP TABLE命令
        executed_queries = [call[0][0] for call in mock_session.execute.call_args_list]
        for query in executed_queries:
            assert "DROP TABLE" not in query.upper()
    
    def test_database_migration_simulation(self, database_service: DatabaseService, mock_session_factory):
        """测试数据库迁移模拟"""
        mock_session = mock_session_factory.return_value
        
        # 模拟数据库迁移
        migration_scripts = [
            "ALTER TABLE test_table ADD COLUMN new_field VARCHAR(255)",
            "CREATE INDEX idx_new_field ON test_table(new_field)",
            "UPDATE test_table SET new_field = 'default_value'"
        ]
        
        # 执行迁移
        result = database_service.execute_migration(migration_scripts)
        
        # 验证结果
        assert result is True
        assert mock_session.execute.call_count == len(migration_scripts)
        mock_session.commit.assert_called_once()
    
    def test_database_performance_monitoring(self, database_service: DatabaseService, mock_session_factory, performance_timer):
        """测试数据库性能监控"""
        mock_session = mock_session_factory.return_value
        
        with performance_timer:
            # 执行大量数据库操作
            for i in range(1000):
                database_service.get_record_by_id("test_table", f"id_{i}")
        
        # 验证性能在可接受范围内
        elapsed_time = performance_timer.elapsed
        assert elapsed_time < 2.0  # 1000次查询应该在2秒内完成
    
    def test_connection_recovery(self, database_service: DatabaseService, mock_engine):
        """测试连接恢复"""
        from sqlalchemy.exc import DisconnectionError
        
        # 模拟连接断开
        mock_engine.connect.side_effect = [DisconnectionError("Connection lost"), Mock()]
        
        # 尝试重新连接
        connection = database_service.get_connection_with_retry(max_retries=2)
        
        # 验证连接恢复
        assert connection is not None
        assert mock_engine.connect.call_count == 2
    
    def test_database_schema_validation(self, database_service: DatabaseService, mock_session_factory):
        """测试数据库模式验证"""
        mock_session = mock_session_factory.return_value
        
        # 验证表结构
        expected_schema = {
            "test_table": {
                "columns": ["id", "name", "provider", "created_at"],
                "indexes": ["idx_name", "idx_provider"]
            }
        }
        
        # 执行模式验证
        is_valid = database_service.validate_schema(expected_schema)
        
        # 验证结果
        assert is_valid is True
    
    def test_data_integrity_check(self, database_service: DatabaseService, mock_session_factory):
        """测试数据完整性检查"""
        mock_session = mock_session_factory.return_value
        mock_result = Mock()
        mock_result.fetchall.return_value = []  # 没有完整性错误
        mock_session.execute.return_value = mock_result
        
        # 执行数据完整性检查
        integrity_issues = database_service.check_data_integrity("test_table")
        
        # 验证结果
        assert len(integrity_issues) == 0
    
    def test_database_cleanup(self, database_service: DatabaseService, mock_session_factory):
        """测试数据库清理"""
        mock_session = mock_session_factory.return_value
        
        # 执行数据库清理
        cleanup_result = database_service.cleanup_old_records(
            table_name="test_table",
            date_field="created_at",
            days_old=30
        )
        
        # 验证结果
        assert cleanup_result["deleted_count"] >= 0
        assert "cleanup_time" in cleanup_result
    
    def test_database_export_import(self, database_service: DatabaseService, mock_session_factory):
        """测试数据库导出导入"""
        mock_session = mock_session_factory.return_value
        
        # 导出数据
        export_data = database_service.export_table_data("test_table")
        
        # 验证导出数据格式
        assert isinstance(export_data, list)
        
        # 导入数据
        import_result = database_service.import_table_data("test_table", export_data)
        
        # 验证导入结果
        assert import_result is True