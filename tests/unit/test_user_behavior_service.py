from datetime import datetime, timedelta
from unittest.mock import Mock, patch

import pytest

from services.user_behavior_service import UserBehaviorService
from tests.utils.test_helpers import MockTestCase


@pytest.mark.service
@pytest.mark.unit
class TestUserBehaviorService(MockTestCase):
    """用户行为服务测试类"""
    
    @pytest.fixture
    def mock_db_session(self):
        """模拟数据库会话"""
        session = Mock()
        session.query = Mock()
        session.add = Mock()
        session.commit = Mock()
        session.rollback = Mock()
        session.close = Mock()
        return session
    
    @pytest.fixture
    def mock_cache_service(self):
        """模拟缓存服务"""
        cache = Mock()
        cache.get = Mock(return_value=None)
        cache.set = Mock(return_value=True)
        cache.delete = Mock(return_value=True)
        cache.increment = Mock(return_value=1)
        return cache
    
    @pytest.fixture
    def mock_analytics_service(self):
        """模拟分析服务"""
        analytics = Mock()
        analytics.track_event = Mock()
        analytics.get_user_metrics = Mock()
        return analytics
    
    @pytest.fixture
    def user_behavior_service(self, mock_db_session, mock_cache_service, mock_analytics_service):
        """创建用户行为服务实例"""
        service = UserBehaviorService()
        service.db_session = mock_db_session
        service.cache_service = mock_cache_service
        service.analytics_service = mock_analytics_service
        return service
    
    @pytest.fixture
    def sample_behavior_event(self):
        """示例行为事件数据"""
        return {
            "user_id": "user123",
            "session_id": "session456",
            "event_type": "message_sent",
            "event_data": {
                "message_length": 150,
                "model_used": "gpt-4",
                "response_time": 2.5
            },
            "timestamp": datetime.now(),
            "ip_address": "***********",
            "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "metadata": {
                "source": "web",
                "feature_flags": ["new_ui", "beta_features"]
            }
        }
    
    @pytest.fixture
    def sample_user_preferences(self):
        """示例用户偏好数据"""
        return {
            "user_id": "user123",
            "preferences": {
                "theme": "dark",
                "language": "zh-CN",
                "model_preference": "gpt-4",
                "temperature": 0.7,
                "max_tokens": 2000,
                "auto_save": True,
                "notifications": {
                    "email": True,
                    "push": False,
                    "in_app": True
                }
            },
            "updated_at": datetime.now()
        }
    
    def test_track_user_event_success(self, user_behavior_service: UserBehaviorService, mock_db_session, sample_behavior_event):
        """测试成功跟踪用户事件"""
        # 跟踪事件
        result = user_behavior_service.track_user_event(sample_behavior_event)
        
        # 验证数据库操作
        mock_db_session.add.assert_called_once()
        mock_db_session.commit.assert_called_once()
        assert result is True
    
    def test_track_user_event_database_error(self, user_behavior_service: UserBehaviorService, mock_db_session, sample_behavior_event):
        """测试跟踪用户事件时数据库错误"""
        # 模拟数据库错误
        mock_db_session.commit.side_effect = Exception("Database error")
        
        # 跟踪事件
        result = user_behavior_service.track_user_event(sample_behavior_event)
        
        # 验证错误处理
        mock_db_session.rollback.assert_called_once()
        assert result is False
    
    def test_get_user_activity_summary_success(self, user_behavior_service: UserBehaviorService, mock_db_session):
        """测试成功获取用户活动摘要"""
        user_id = "user123"
        start_date = datetime.now() - timedelta(days=30)
        end_date = datetime.now()
        
        # 模拟查询结果
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.group_by.return_value = mock_query
        mock_query.all.return_value = [
            ("message_sent", 150),
            ("session_created", 25),
            ("model_switched", 10)
        ]
        mock_db_session.query.return_value = mock_query
        
        # 获取活动摘要
        summary = user_behavior_service.get_user_activity_summary(user_id, start_date, end_date)
        
        # 验证结果
        assert "total_events" in summary
        assert "event_breakdown" in summary
        assert "period" in summary
        assert len(summary["event_breakdown"]) == 3
    
    def test_get_user_preferences_success(self, user_behavior_service: UserBehaviorService, mock_db_session, sample_user_preferences):
        """测试成功获取用户偏好"""
        user_id = "user123"
        
        # 模拟数据库查询
        mock_preferences = Mock()
        mock_preferences.preferences = sample_user_preferences["preferences"]
        mock_preferences.updated_at = sample_user_preferences["updated_at"]
        
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_preferences
        mock_db_session.query.return_value = mock_query
        
        # 获取用户偏好
        preferences = user_behavior_service.get_user_preferences(user_id)
        
        # 验证结果
        assert preferences == sample_user_preferences["preferences"]
    
    def test_get_user_preferences_not_found(self, user_behavior_service: UserBehaviorService, mock_db_session):
        """测试获取不存在的用户偏好"""
        user_id = "nonexistent"
        
        # 模拟数据库查询返回None
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = None
        mock_db_session.query.return_value = mock_query
        
        # 获取用户偏好
        preferences = user_behavior_service.get_user_preferences(user_id)
        
        # 验证返回默认偏好
        assert preferences is not None
        assert "theme" in preferences
        assert "language" in preferences
    
    def test_update_user_preferences_success(self, user_behavior_service: UserBehaviorService, mock_db_session):
        """测试成功更新用户偏好"""
        user_id = "user123"
        new_preferences = {
            "theme": "light",
            "language": "en-US",
            "model_preference": "gpt-3.5-turbo"
        }
        
        # 模拟现有偏好
        mock_existing = Mock()
        mock_existing.preferences = {"theme": "dark", "language": "zh-CN"}
        
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_existing
        mock_db_session.query.return_value = mock_query
        
        # 更新偏好
        result = user_behavior_service.update_user_preferences(user_id, new_preferences)
        
        # 验证数据库操作
        mock_db_session.commit.assert_called_once()
        assert result is True
    
    def test_get_user_behavior_patterns_success(self, user_behavior_service: UserBehaviorService, mock_db_session):
        """测试成功获取用户行为模式"""
        user_id = "user123"
        
        # 模拟查询结果
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.group_by.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.all.return_value = [
            (9, 25),   # 9点，25次活动
            (14, 30),  # 14点，30次活动
            (20, 15)   # 20点，15次活动
        ]
        mock_db_session.query.return_value = mock_query
        
        # 获取行为模式
        patterns = user_behavior_service.get_user_behavior_patterns(user_id)
        
        # 验证结果
        assert "hourly_activity" in patterns
        assert "peak_hours" in patterns
        assert "activity_score" in patterns
        assert len(patterns["hourly_activity"]) == 3
    
    def test_get_model_usage_statistics_success(self, user_behavior_service: UserBehaviorService, mock_db_session):
        """测试成功获取模型使用统计"""
        user_id = "user123"
        
        # 模拟查询结果
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.group_by.return_value = mock_query
        mock_query.all.return_value = [
            ("gpt-4", 100, 50000, 25000),
            ("gpt-3.5-turbo", 200, 80000, 40000)
        ]
        mock_db_session.query.return_value = mock_query
        
        # 获取模型使用统计
        stats = user_behavior_service.get_model_usage_statistics(user_id)
        
        # 验证结果
        assert len(stats) == 2
        assert stats[0]["model_name"] == "gpt-4"
        assert stats[0]["usage_count"] == 100
        assert stats[1]["model_name"] == "gpt-3.5-turbo"
        assert stats[1]["usage_count"] == 200
    
    def test_track_feature_usage_success(self, user_behavior_service: UserBehaviorService, mock_cache_service):
        """测试成功跟踪功能使用"""
        user_id = "user123"
        feature_name = "export_conversation"
        
        # 跟踪功能使用
        result = user_behavior_service.track_feature_usage(user_id, feature_name)
        
        # 验证缓存操作
        mock_cache_service.increment.assert_called()
        assert result is True
    
    def test_get_feature_usage_stats_success(self, user_behavior_service: UserBehaviorService, mock_cache_service):
        """测试成功获取功能使用统计"""
        user_id = "user123"
        
        # 模拟缓存数据
        mock_cache_service.get.side_effect = lambda key: {
            f"feature_usage:{user_id}:export_conversation": 15,
            f"feature_usage:{user_id}:import_conversation": 5,
            f"feature_usage:{user_id}:share_conversation": 8
        }.get(key, 0)
        
        # 获取功能使用统计
        stats = user_behavior_service.get_feature_usage_stats(user_id)
        
        # 验证结果
        assert "export_conversation" in stats
        assert "import_conversation" in stats
        assert "share_conversation" in stats
    
    def test_analyze_user_engagement_success(self, user_behavior_service: UserBehaviorService, mock_db_session):
        """测试成功分析用户参与度"""
        user_id = "user123"
        
        # 模拟查询结果
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.scalar.return_value = 150  # 总事件数
        mock_db_session.query.return_value = mock_query
        
        # 模拟其他统计查询
        with patch.object(user_behavior_service, '_get_session_duration_avg', return_value=25.5):
            with patch.object(user_behavior_service, '_get_daily_active_days', return_value=20):
                engagement = user_behavior_service.analyze_user_engagement(user_id)
        
        # 验证结果
        assert "engagement_score" in engagement
        assert "activity_level" in engagement
        assert "metrics" in engagement
        assert engagement["metrics"]["total_events"] == 150
    
    def test_get_user_journey_success(self, user_behavior_service: UserBehaviorService, mock_db_session):
        """测试成功获取用户旅程"""
        user_id = "user123"
        
        # 模拟查询结果
        mock_events = [
            Mock(event_type="user_registered", timestamp=datetime.now() - timedelta(days=30)),
            Mock(event_type="first_session", timestamp=datetime.now() - timedelta(days=29)),
            Mock(event_type="first_message", timestamp=datetime.now() - timedelta(days=29)),
            Mock(event_type="model_switched", timestamp=datetime.now() - timedelta(days=20)),
            Mock(event_type="feature_discovered", timestamp=datetime.now() - timedelta(days=10))
        ]
        
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.all.return_value = mock_events
        mock_db_session.query.return_value = mock_query
        
        # 获取用户旅程
        journey = user_behavior_service.get_user_journey(user_id)
        
        # 验证结果
        assert "milestones" in journey
        assert "timeline" in journey
        assert "user_lifecycle_stage" in journey
        assert len(journey["milestones"]) == 5
    
    def test_predict_user_churn_success(self, user_behavior_service: UserBehaviorService, mock_db_session):
        """测试成功预测用户流失"""
        user_id = "user123"
        
        # 模拟用户活动数据
        with patch.object(user_behavior_service, '_get_recent_activity_score', return_value=0.3):
            with patch.object(user_behavior_service, '_get_engagement_trend', return_value=-0.2):
                with patch.object(user_behavior_service, '_get_feature_adoption_rate', return_value=0.4):
                    churn_prediction = user_behavior_service.predict_user_churn(user_id)
        
        # 验证结果
        assert "churn_probability" in churn_prediction
        assert "risk_level" in churn_prediction
        assert "factors" in churn_prediction
        assert "recommendations" in churn_prediction
        assert 0 <= churn_prediction["churn_probability"] <= 1
    
    def test_get_user_segments_success(self, user_behavior_service: UserBehaviorService, mock_db_session):
        """测试成功获取用户细分"""
        user_id = "user123"
        
        # 模拟用户数据
        with patch.object(user_behavior_service, '_calculate_user_metrics', return_value={
            "activity_score": 0.8,
            "feature_adoption": 0.6,
            "engagement_level": 0.7,
            "tenure_days": 45
        }):
            segments = user_behavior_service.get_user_segments(user_id)
        
        # 验证结果
        assert "primary_segment" in segments
        assert "secondary_segments" in segments
        assert "segment_scores" in segments
        assert segments["primary_segment"] in ["power_user", "regular_user", "casual_user", "new_user", "at_risk"]
    
    def test_generate_personalized_recommendations_success(self, user_behavior_service: UserBehaviorService):
        """测试成功生成个性化推荐"""
        user_id = "user123"
        
        # 模拟用户数据
        with patch.object(user_behavior_service, 'get_user_preferences', return_value={"model_preference": "gpt-4"}):
            with patch.object(user_behavior_service, 'get_user_behavior_patterns', return_value={"peak_hours": [9, 14]}):
                with patch.object(user_behavior_service, 'get_feature_usage_stats', return_value={"export": 0}):
                    recommendations = user_behavior_service.generate_personalized_recommendations(user_id)
        
        # 验证结果
        assert "feature_recommendations" in recommendations
        assert "usage_tips" in recommendations
        assert "optimization_suggestions" in recommendations
        assert len(recommendations["feature_recommendations"]) > 0
    
    def test_export_user_behavior_data_success(self, user_behavior_service: UserBehaviorService, mock_db_session):
        """测试成功导出用户行为数据"""
        user_id = "user123"
        start_date = datetime.now() - timedelta(days=30)
        end_date = datetime.now()
        
        # 模拟查询结果
        mock_events = [
            Mock(event_type="message_sent", timestamp=datetime.now(), event_data={"length": 100}),
            Mock(event_type="session_created", timestamp=datetime.now(), event_data={"source": "web"})
        ]
        
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.all.return_value = mock_events
        mock_db_session.query.return_value = mock_query
        
        # 导出数据
        export_data = user_behavior_service.export_user_behavior_data(user_id, start_date, end_date)
        
        # 验证结果
        assert "user_id" in export_data
        assert "period" in export_data
        assert "events" in export_data
        assert "summary" in export_data
        assert len(export_data["events"]) == 2
    
    def test_import_user_behavior_data_success(self, user_behavior_service: UserBehaviorService, mock_db_session):
        """测试成功导入用户行为数据"""
        import_data = {
            "user_id": "user123",
            "events": [
                {
                    "event_type": "message_sent",
                    "timestamp": datetime.now().isoformat(),
                    "event_data": {"length": 100}
                },
                {
                    "event_type": "session_created",
                    "timestamp": datetime.now().isoformat(),
                    "event_data": {"source": "web"}
                }
            ]
        }
        
        # 导入数据
        result = user_behavior_service.import_user_behavior_data(import_data)
        
        # 验证数据库操作
        assert mock_db_session.add.call_count == 2
        mock_db_session.commit.assert_called_once()
        assert result is True
    
    def test_get_behavior_analytics_dashboard_success(self, user_behavior_service: UserBehaviorService):
        """测试成功获取行为分析仪表板"""
        user_id = "user123"
        
        # 模拟各种数据
        with patch.object(user_behavior_service, 'get_user_activity_summary', return_value={"total_events": 100}):
            with patch.object(user_behavior_service, 'get_user_behavior_patterns', return_value={"peak_hours": [9, 14]}):
                with patch.object(user_behavior_service, 'analyze_user_engagement', return_value={"engagement_score": 0.8}):
                    with patch.object(user_behavior_service, 'get_model_usage_statistics', return_value=[{"model_name": "gpt-4"}]):
                        dashboard = user_behavior_service.get_behavior_analytics_dashboard(user_id)
        
        # 验证结果
        assert "overview" in dashboard
        assert "activity_patterns" in dashboard
        assert "engagement_metrics" in dashboard
        assert "model_usage" in dashboard
        assert "recommendations" in dashboard


@pytest.mark.service
@pytest.mark.unit
class TestUserBehaviorServiceEdgeCases(MockTestCase):
    """用户行为服务边界情况测试类"""
    
    @pytest.fixture
    def mock_db_session(self):
        """模拟数据库会话"""
        session = Mock()
        return session
    
    @pytest.fixture
    def mock_cache_service(self):
        """模拟缓存服务"""
        cache = Mock()
        return cache
    
    @pytest.fixture
    def mock_analytics_service(self):
        """模拟分析服务"""
        analytics = Mock()
        return analytics
    
    @pytest.fixture
    def user_behavior_service(self, mock_db_session, mock_cache_service, mock_analytics_service):
        """创建用户行为服务实例"""
        service = UserBehaviorService()
        service.db_session = mock_db_session
        service.cache_service = mock_cache_service
        service.analytics_service = mock_analytics_service
        return service
    
    @pytest.fixture
    def memory_monitor(self):
        """内存监控器"""
        return self.create_memory_monitor()
    
    @pytest.fixture
    def performance_timer(self):
        """性能计时器"""
        return self.create_performance_timer()
    
    def test_track_event_with_none_data(self, user_behavior_service: UserBehaviorService, mock_db_session):
        """测试跟踪包含None数据的事件"""
        event_data = {
            "user_id": "user123",
            "session_id": None,
            "event_type": "test_event",
            "event_data": None,
            "timestamp": datetime.now(),
            "metadata": None
        }
        
        # 跟踪事件
        result = user_behavior_service.track_user_event(event_data)
        
        # 验证None值被正确处理
        mock_db_session.add.assert_called_once()
        assert result is True
    
    def test_track_event_with_large_data(self, user_behavior_service: UserBehaviorService, mock_db_session):
        """测试跟踪大数据事件"""
        large_event_data = {
            "user_id": "user123",
            "event_type": "large_data_event",
            "event_data": {
                "large_text": "x" * 100000,  # 100KB文本
                "large_list": list(range(10000)),
                "nested_data": {
                    "level1": {
                        "level2": {
                            "level3": {"data": "y" * 50000}
                        }
                    }
                }
            },
            "timestamp": datetime.now()
        }
        
        # 跟踪事件
        result = user_behavior_service.track_user_event(large_event_data)
        
        # 验证大数据被正确处理
        mock_db_session.add.assert_called_once()
        assert result is True
    
    def test_track_event_with_special_characters(self, user_behavior_service: UserBehaviorService, mock_db_session):
        """测试跟踪包含特殊字符的事件"""
        special_event_data = {
            "user_id": "user@#$%^&*()123",
            "event_type": "special_chars_event",
            "event_data": {
                "text": "包含特殊字符 @#$%^&*()_+ 和 Unicode 🚀🎉",
                "html": "<script>alert('xss')</script>",
                "sql": "'; DROP TABLE users; --",
                "unicode": "\u0000\u001f\u007f"
            },
            "timestamp": datetime.now()
        }
        
        # 跟踪事件
        result = user_behavior_service.track_user_event(special_event_data)
        
        # 验证特殊字符被正确处理
        mock_db_session.add.assert_called_once()
        assert result is True
    
    def test_database_connection_failure(self, user_behavior_service: UserBehaviorService, mock_db_session):
        """测试数据库连接失败"""
        # 模拟数据库连接失败
        mock_db_session.add.side_effect = Exception("Database connection failed")
        
        event_data = {
            "user_id": "user123",
            "event_type": "test_event",
            "timestamp": datetime.now()
        }
        
        # 跟踪事件
        result = user_behavior_service.track_user_event(event_data)
        
        # 验证错误处理
        mock_db_session.rollback.assert_called_once()
        assert result is False
    
    def test_cache_service_failure(self, user_behavior_service: UserBehaviorService, mock_cache_service):
        """测试缓存服务失败"""
        user_id = "user123"
        feature_name = "test_feature"
        
        # 模拟缓存服务失败
        mock_cache_service.increment.side_effect = Exception("Cache service failed")
        
        # 跟踪功能使用
        result = user_behavior_service.track_feature_usage(user_id, feature_name)
        
        # 应该优雅处理缓存失败
        assert result is False
    
    def test_concurrent_event_tracking(self, user_behavior_service: UserBehaviorService, mock_db_session):
        """测试并发事件跟踪"""
        import threading

        results = []
        errors = []
        
        def track_event(index):
            try:
                event_data = {
                    "user_id": f"user{index}",
                    "event_type": "concurrent_event",
                    "event_data": {"index": index},
                    "timestamp": datetime.now()
                }
                result = user_behavior_service.track_user_event(event_data)
                results.append(result)
            except Exception as e:
                errors.append(e)
        
        # 创建多个线程同时跟踪事件
        threads = []
        for i in range(20):
            thread = threading.Thread(target=track_event, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证没有错误发生
        assert len(errors) == 0
        assert len(results) == 20
        assert all(result is True for result in results)
    
    def test_memory_usage_large_behavior_dataset(self, user_behavior_service: UserBehaviorService, memory_monitor):
        """测试大行为数据集内存使用"""
        with memory_monitor:
            # 创建大量行为事件数据
            events_data = []
            for i in range(10000):
                event_data = {
                    "user_id": f"user{i % 100}",  # 100个用户
                    "event_type": f"event_type_{i % 10}",  # 10种事件类型
                    "event_data": {
                        "index": i,
                        "data": "x" * 1000,  # 1KB数据
                        "metadata": {
                            "timestamp": datetime.now().isoformat(),
                            "session_id": f"session_{i // 100}"
                        }
                    },
                    "timestamp": datetime.now()
                }
                events_data.append(event_data)
        
        # 验证内存使用在合理范围内
        memory_usage = memory_monitor.get_peak_memory()
        assert memory_usage < 100 * 1024 * 1024  # 小于100MB
    
    def test_performance_behavior_analysis_benchmark(self, user_behavior_service: UserBehaviorService, performance_timer):
        """测试行为分析性能基准"""
        user_id = "user123"
        
        # 模拟大量数据分析
        with performance_timer:
            with patch.object(user_behavior_service, '_analyze_large_dataset', return_value={"result": "success"}):
                for i in range(100):
                    # 模拟各种分析操作
                    user_behavior_service._calculate_engagement_score(user_id)
                    user_behavior_service._analyze_behavior_patterns(user_id)
                    user_behavior_service._generate_insights(user_id)
        
        # 验证性能在可接受范围内
        elapsed_time = performance_timer.elapsed
        assert elapsed_time < 2.0  # 100次分析操作应该在2秒内完成
    
    def test_user_preferences_circular_reference(self, user_behavior_service: UserBehaviorService, mock_db_session):
        """测试用户偏好循环引用处理"""
        user_id = "user123"
        
        # 创建循环引用的偏好数据
        circular_prefs = {"theme": "dark"}
        circular_prefs["self_ref"] = circular_prefs  # 循环引用
        
        # 模拟现有偏好
        mock_existing = Mock()
        mock_existing.preferences = {"theme": "light"}
        
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_existing
        mock_db_session.query.return_value = mock_query
        
        # 更新偏好
        result = user_behavior_service.update_user_preferences(user_id, circular_prefs)
        
        # 验证循环引用被正确处理
        assert result is True or result is False  # 应该被处理而不是崩溃
    
    def test_behavior_pattern_analysis_edge_cases(self, user_behavior_service: UserBehaviorService, mock_db_session):
        """测试行为模式分析边界情况"""
        user_id = "user123"
        
        # 测试空数据情况
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.group_by.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.all.return_value = []  # 空结果
        mock_db_session.query.return_value = mock_query
        
        # 获取行为模式
        patterns = user_behavior_service.get_user_behavior_patterns(user_id)
        
        # 验证空数据被正确处理
        assert "hourly_activity" in patterns
        assert "peak_hours" in patterns
        assert patterns["hourly_activity"] == [] or len(patterns["hourly_activity"]) == 0
    
    def test_churn_prediction_extreme_values(self, user_behavior_service: UserBehaviorService):
        """测试流失预测极端值处理"""
        user_id = "user123"
        
        # 测试极端值情况
        with patch.object(user_behavior_service, '_get_recent_activity_score', return_value=float('inf')):
            with patch.object(user_behavior_service, '_get_engagement_trend', return_value=float('-inf')):
                with patch.object(user_behavior_service, '_get_feature_adoption_rate', return_value=float('nan')):
                    churn_prediction = user_behavior_service.predict_user_churn(user_id)
        
        # 验证极端值被正确处理
        assert "churn_probability" in churn_prediction
        assert 0 <= churn_prediction["churn_probability"] <= 1  # 应该被规范化
        assert churn_prediction["risk_level"] in ["low", "medium", "high"]
    
    def test_data_export_import_integrity(self, user_behavior_service: UserBehaviorService, mock_db_session):
        """测试数据导出导入完整性"""
        user_id = "user123"
        
        # 创建测试数据
        original_events = [
            {
                "event_type": "test_event_1",
                "timestamp": datetime.now(),
                "event_data": {"key1": "value1", "unicode": "测试数据"}
            },
            {
                "event_type": "test_event_2",
                "timestamp": datetime.now(),
                "event_data": {"key2": "value2", "special": "@#$%^&*()"}
            }
        ]
        
        # 模拟导出查询
        mock_events = [Mock(**event) for event in original_events]
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.all.return_value = mock_events
        mock_db_session.query.return_value = mock_query
        
        # 导出数据
        export_data = user_behavior_service.export_user_behavior_data(
            user_id, 
            datetime.now() - timedelta(days=1), 
            datetime.now()
        )
        
        # 导入数据
        import_result = user_behavior_service.import_user_behavior_data(export_data)
        
        # 验证数据完整性
        assert import_result is True
        assert len(export_data["events"]) == 2
    
    def test_analytics_dashboard_performance_optimization(self, user_behavior_service: UserBehaviorService, performance_timer):
        """测试分析仪表板性能优化"""
        user_id = "user123"
        
        # 模拟缓存优化
        with patch.object(user_behavior_service.cache_service, 'get', return_value=None):
            with patch.object(user_behavior_service.cache_service, 'set', return_value=True):
                with performance_timer:
                    # 多次调用仪表板（应该有缓存优化）
                    for i in range(10):
                        dashboard = user_behavior_service.get_behavior_analytics_dashboard(user_id)
        
        # 验证性能优化效果
        elapsed_time = performance_timer.elapsed
        assert elapsed_time < 1.0  # 10次调用应该在1秒内完成（有缓存）
    
    def test_user_segmentation_boundary_conditions(self, user_behavior_service: UserBehaviorService):
        """测试用户细分边界条件"""
        user_id = "user123"
        
        # 测试边界值
        boundary_metrics = [
            {"activity_score": 0.0, "feature_adoption": 0.0, "engagement_level": 0.0, "tenure_days": 0},
            {"activity_score": 1.0, "feature_adoption": 1.0, "engagement_level": 1.0, "tenure_days": 365},
            {"activity_score": 0.5, "feature_adoption": 0.5, "engagement_level": 0.5, "tenure_days": 30}
        ]
        
        for metrics in boundary_metrics:
            with patch.object(user_behavior_service, '_calculate_user_metrics', return_value=metrics):
                segments = user_behavior_service.get_user_segments(user_id)
                
                # 验证边界条件被正确处理
                assert "primary_segment" in segments
                assert segments["primary_segment"] in ["power_user", "regular_user", "casual_user", "new_user", "at_risk"]
                assert "segment_scores" in segments
                assert all(0 <= score <= 1 for score in segments["segment_scores"].values())