"""对话服务单元测试"""

import json
import uuid
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch

import pytest
from exceptions.api_exceptions import ValidationAPIError, NotFoundAPIError, DatabaseAPIError

from services.conversation_service import ConversationService
from tests.utils.test_helpers import MockTestCase


@pytest.mark.service
@pytest.mark.unit
class TestConversationService(MockTestCase):
    """对话服务测试类"""
    
    @pytest.fixture
    def mock_db_session(self):
        """模拟数据库会话"""
        session = Mock()
        session.add = Mock()
        session.commit = AsyncMock()
        session.rollback = AsyncMock()
        session.refresh = AsyncMock()
        session.execute = AsyncMock()
        session.scalar = AsyncMock()
        session.scalars = AsyncMock()
        session.close = AsyncMock()
        return session
    
    @pytest.fixture
    def mock_session_service(self):
        """模拟会话服务"""
        service = Mock()
        service.get_by_id = AsyncMock()
        service.update_conversation_count = AsyncMock()
        return service
    
    @pytest.fixture
    def mock_message_service(self):
        """模拟消息服务"""
        service = Mock()
        service.get_by_conversation_id = AsyncMock()
        service.delete_by_conversation_id = AsyncMock()
        service.count_by_conversation_id = AsyncMock()
        return service
    
    @pytest.fixture
    def mock_cache_service(self):
        """模拟缓存服务"""
        service = Mock()
        service.get = AsyncMock()
        service.set = AsyncMock()
        service.delete = AsyncMock()
        service.get_list = AsyncMock()
        service.add_to_list = AsyncMock()
        return service
    
    @pytest.fixture
    def conversation_service(self, mock_db_session, mock_session_service, mock_message_service, mock_cache_service):
        """创建对话服务实例"""
        service = ConversationService()
        service.db_session = mock_db_session
        service.session_service = mock_session_service
        service.message_service = mock_message_service
        service.cache_service = mock_cache_service
        return service
    
    @pytest.fixture
    def sample_conversation_create(self):
        """示例对话创建数据"""
        return {
            "session_id": str(uuid.uuid4()),
            "title": "Test Conversation",
            "model_name": "gpt-3.5-turbo",
            "system_prompt": "You are a helpful assistant.",
            "temperature": 0.7,
            "max_tokens": 2048,
            "metadata": {"source": "web"}
        }
    
    @pytest.fixture
    def sample_conversation_update(self):
        """示例对话更新数据"""
        return {
            "title": "Updated Conversation Title",
            "temperature": 0.8,
            "metadata": {"updated": True, "update_time": datetime.utcnow().isoformat()}
        }
    
    @pytest.fixture
    def sample_conversation_model(self):
        """示例对话模型"""
        return {
            "id": str(uuid.uuid4()),
            "session_id": str(uuid.uuid4()),
            "title": "Test Conversation",
            "model_name": "gpt-3.5-turbo",
            "system_prompt": "You are a helpful assistant.",
            "temperature": 0.7,
            "max_tokens": 2048,
            "metadata": {"source": "web"},
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "last_message_at": None,
            "message_count": 0,
            "total_tokens": 0,
            "is_active": True,
            "is_deleted": False
        }
    
    @pytest.fixture
    def sample_session_data(self):
        """示例会话数据"""
        return {
            "id": str(uuid.uuid4()),
            "user_id": str(uuid.uuid4()),
            "title": "Test Session",
            "is_active": True,
            "created_at": datetime.utcnow()
        }
    
    async def test_create_conversation_success(self, conversation_service: ConversationService, mock_db_session, mock_session_service, sample_conversation_create, sample_session_data):
        """测试成功创建对话"""
        # 设置模拟返回
        mock_session_service.get_by_id.return_value = sample_session_data
        
        # 模拟数据库操作
        mock_conversation = Mock()
        mock_conversation.id = str(uuid.uuid4())
        mock_conversation.session_id = sample_conversation_create["session_id"]
        mock_conversation.title = sample_conversation_create["title"]
        mock_conversation.model_name = sample_conversation_create["model_name"]
        mock_conversation.created_at = datetime.utcnow()
        
        mock_db_session.refresh.return_value = None
        
        with patch.object(conversation_service, '_create_conversation_model', return_value=mock_conversation):
            # 创建对话
            result = await conversation_service.create(sample_conversation_create)
        
        # 验证结果
        assert result.id == mock_conversation.id
        assert result.session_id == sample_conversation_create["session_id"]
        assert result.title == sample_conversation_create["title"]
        mock_db_session.add.assert_called_once()
        mock_db_session.commit.assert_called_once()
        mock_session_service.update_conversation_count.assert_called_once()
    
    async def test_create_conversation_session_not_found(self, conversation_service: ConversationService, mock_session_service, sample_conversation_create):
        """测试创建对话时会话不存在"""
        # 设置模拟返回
        mock_session_service.get_by_id.return_value = None
        
        # 尝试创建对话
        with pytest.raises(ValidationAPIError) as exc_info:
            await conversation_service.create(sample_conversation_create)
        
        assert "session not found" in str(exc_info.value).lower()
    
    async def test_create_conversation_invalid_model(self, conversation_service: ConversationService, mock_session_service, sample_conversation_create, sample_session_data):
        """测试创建对话时模型无效"""
        # 设置模拟返回
        mock_session_service.get_by_id.return_value = sample_session_data
        
        # 设置无效模型
        sample_conversation_create["model_name"] = "invalid_model"
        
        # 尝试创建对话
        with pytest.raises(ValidationAPIError) as exc_info:
            await conversation_service.create(sample_conversation_create)
        
        assert "invalid model" in str(exc_info.value).lower()
    
    async def test_get_conversation_by_id_success(self, conversation_service: ConversationService, mock_db_session, sample_conversation_model):
        """测试成功按ID获取对话"""
        # 设置模拟返回
        mock_db_session.scalar.return_value = sample_conversation_model
        
        # 获取对话
        result = await conversation_service.get_by_id(sample_conversation_model["id"])
        
        # 验证结果
        assert result == sample_conversation_model
        mock_db_session.scalar.assert_called_once()
    
    async def test_get_conversation_by_id_not_found(self, conversation_service: ConversationService, mock_db_session):
        """测试按ID获取不存在的对话"""
        # 设置模拟返回
        mock_db_session.scalar.return_value = None
        
        # 尝试获取不存在的对话
        result = await conversation_service.get_by_id("nonexistent_id")
        
        # 验证结果
        assert result is None
    
    async def test_get_conversations_by_session_success(self, conversation_service: ConversationService, mock_db_session, sample_conversation_model):
        """测试成功按会话ID获取对话列表"""
        # 设置模拟返回
        conversations = [sample_conversation_model, sample_conversation_model.copy()]
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = conversations
        mock_db_session.execute.return_value = mock_result
        
        # 获取对话列表
        result = await conversation_service.get_by_session_id(sample_conversation_model["session_id"])
        
        # 验证结果
        assert len(result) == 2
        assert result[0] == sample_conversation_model
        mock_db_session.execute.assert_called_once()
    
    async def test_get_conversations_by_session_with_pagination(self, conversation_service: ConversationService, mock_db_session, sample_conversation_model):
        """测试分页获取会话对话"""
        # 设置模拟返回
        conversations = [sample_conversation_model]
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = conversations
        mock_db_session.execute.return_value = mock_result
        
        # 获取分页对话
        result = await conversation_service.get_by_session_id(
            sample_conversation_model["session_id"],
            limit=10,
            offset=0
        )
        
        # 验证结果
        assert len(result) == 1
        mock_db_session.execute.assert_called_once()
    
    async def test_update_conversation_success(self, conversation_service: ConversationService, mock_db_session, sample_conversation_model, sample_conversation_update):
        """测试成功更新对话"""
        # 设置模拟返回
        mock_db_session.scalar.return_value = sample_conversation_model
        
        # 更新对话
        result = await conversation_service.update(sample_conversation_model["id"], sample_conversation_update)
        
        # 验证结果
        assert result is not None
        mock_db_session.commit.assert_called_once()
    
    async def test_update_conversation_not_found(self, conversation_service: ConversationService, mock_db_session, sample_conversation_update):
        """测试更新不存在的对话"""
        # 设置模拟返回
        mock_db_session.scalar.return_value = None
        
        # 尝试更新不存在的对话
        with pytest.raises(NotFoundAPIError) as exc_info:
            await conversation_service.update("nonexistent_id", sample_conversation_update)
        
        assert "conversation not found" in str(exc_info.value).lower()
    
    async def test_delete_conversation_success(self, conversation_service: ConversationService, mock_db_session, mock_message_service, sample_conversation_model):
        """测试成功删除对话"""
        # 设置模拟返回
        mock_db_session.scalar.return_value = sample_conversation_model
        mock_message_service.delete_by_conversation_id.return_value = True
        
        # 删除对话
        result = await conversation_service.delete(sample_conversation_model["id"])
        
        # 验证结果
        assert result is True
        mock_db_session.commit.assert_called_once()
        mock_message_service.delete_by_conversation_id.assert_called_once()
    
    async def test_delete_conversation_not_found(self, conversation_service: ConversationService, mock_db_session):
        """测试删除不存在的对话"""
        # 设置模拟返回
        mock_db_session.scalar.return_value = None
        
        # 尝试删除不存在的对话
        with pytest.raises(NotFoundAPIError) as exc_info:
            await conversation_service.delete("nonexistent_id")
        
        assert "conversation not found" in str(exc_info.value).lower()
    
    async def test_soft_delete_conversation_success(self, conversation_service: ConversationService, mock_db_session, sample_conversation_model):
        """测试成功软删除对话"""
        # 设置模拟返回
        mock_db_session.scalar.return_value = sample_conversation_model
        
        # 软删除对话
        result = await conversation_service.soft_delete(sample_conversation_model["id"])
        
        # 验证结果
        assert result is True
        mock_db_session.commit.assert_called_once()
    
    async def test_update_last_message_success(self, conversation_service: ConversationService, mock_db_session, sample_conversation_model):
        """测试成功更新最后消息时间"""
        # 设置模拟返回
        mock_db_session.scalar.return_value = sample_conversation_model
        
        # 更新最后消息时间
        result = await conversation_service.update_last_message(sample_conversation_model["id"])
        
        # 验证结果
        assert result is True
        mock_db_session.commit.assert_called_once()
    
    async def test_increment_message_count_success(self, conversation_service: ConversationService, mock_db_session, sample_conversation_model):
        """测试成功增加消息计数"""
        # 设置模拟返回
        mock_db_session.scalar.return_value = sample_conversation_model
        
        # 增加消息计数
        result = await conversation_service.increment_message_count(sample_conversation_model["id"], token_count=10)
        
        # 验证结果
        assert result is True
        mock_db_session.commit.assert_called_once()
    
    async def test_get_conversation_statistics_success(self, conversation_service: ConversationService, mock_db_session):
        """测试成功获取对话统计"""
        # 设置模拟返回
        stats_data = [
            ("session1", 5, 50, 1000, "gpt-3.5-turbo"),
            ("session2", 3, 30, 600, "gpt-4")
        ]
        mock_result = Mock()
        mock_result.all.return_value = stats_data
        mock_db_session.execute.return_value = mock_result
        
        # 获取统计信息
        result = await conversation_service.get_conversation_statistics()
        
        # 验证结果
        assert len(result) == 2
        assert result[0]["session_id"] == "session1"
        assert result[0]["conversation_count"] == 5
        assert result[0]["message_count"] == 50
        assert result[0]["total_tokens"] == 1000
    
    async def test_search_conversations_success(self, conversation_service: ConversationService, mock_db_session, sample_conversation_model):
        """测试成功搜索对话"""
        # 设置模拟返回
        conversations = [sample_conversation_model]
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = conversations
        mock_db_session.execute.return_value = mock_result
        
        # 搜索对话
        result = await conversation_service.search_conversations(
            session_id=sample_conversation_model["session_id"],
            query="test"
        )
        
        # 验证结果
        assert len(result) == 1
        assert result[0] == sample_conversation_model
    
    async def test_get_active_conversations_success(self, conversation_service: ConversationService, mock_db_session, sample_conversation_model):
        """测试成功获取活跃对话"""
        # 设置模拟返回
        conversations = [sample_conversation_model]
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = conversations
        mock_db_session.execute.return_value = mock_result
        
        # 获取活跃对话
        result = await conversation_service.get_active_conversations(sample_conversation_model["session_id"])
        
        # 验证结果
        assert len(result) == 1
        assert result[0] == sample_conversation_model
    
    async def test_archive_conversation_success(self, conversation_service: ConversationService, mock_db_session, sample_conversation_model):
        """测试成功归档对话"""
        # 设置模拟返回
        mock_db_session.scalar.return_value = sample_conversation_model
        
        # 归档对话
        result = await conversation_service.archive_conversation(sample_conversation_model["id"])
        
        # 验证结果
        assert result is True
        mock_db_session.commit.assert_called_once()
    
    async def test_restore_conversation_success(self, conversation_service: ConversationService, mock_db_session, sample_conversation_model):
        """测试成功恢复对话"""
        # 设置模拟返回
        sample_conversation_model["is_active"] = False
        mock_db_session.scalar.return_value = sample_conversation_model
        
        # 恢复对话
        result = await conversation_service.restore_conversation(sample_conversation_model["id"])
        
        # 验证结果
        assert result is True
        mock_db_session.commit.assert_called_once()
    
    async def test_duplicate_conversation_success(self, conversation_service: ConversationService, mock_db_session, sample_conversation_model):
        """测试成功复制对话"""
        # 设置模拟返回
        mock_db_session.scalar.return_value = sample_conversation_model
        
        # 模拟新对话
        new_conversation = Mock()
        new_conversation.id = str(uuid.uuid4())
        new_conversation.title = f"{sample_conversation_model['title']} (Copy)"
        
        with patch.object(conversation_service, '_create_conversation_model', return_value=new_conversation):
            # 复制对话
            result = await conversation_service.duplicate_conversation(sample_conversation_model["id"])
        
        # 验证结果
        assert result.id != sample_conversation_model["id"]
        assert "(Copy)" in result.title
        mock_db_session.add.assert_called_once()
        mock_db_session.commit.assert_called_once()
    
    async def test_export_conversation_success(self, conversation_service: ConversationService, mock_db_session, mock_message_service, sample_conversation_model):
        """测试成功导出对话"""
        # 设置模拟返回
        mock_db_session.scalar.return_value = sample_conversation_model
        
        # 模拟消息数据
        messages = [
            {"id": "msg1", "role": "user", "content": "Hello", "created_at": datetime.utcnow()},
            {"id": "msg2", "role": "assistant", "content": "Hi there!", "created_at": datetime.utcnow()}
        ]
        mock_message_service.get_by_conversation_id.return_value = messages
        
        # 导出对话
        result = await conversation_service.export_conversation(
            sample_conversation_model["id"],
            format="json"
        )
        
        # 验证结果
        assert isinstance(result, str)
        exported_data = json.loads(result)
        assert "conversation" in exported_data
        assert "messages" in exported_data
        assert len(exported_data["messages"]) == 2
    
    async def test_import_conversation_success(self, conversation_service: ConversationService, mock_db_session, mock_session_service, mock_message_service, sample_session_data):
        """测试成功导入对话"""
        # 设置模拟返回
        mock_session_service.get_by_id.return_value = sample_session_data
        
        # 准备导入数据
        import_data = {
            "conversation": {
                "title": "Imported Conversation",
                "model_name": "gpt-3.5-turbo",
                "system_prompt": "You are a helpful assistant.",
                "temperature": 0.7
            },
            "messages": [
                {"role": "user", "content": "Hello", "created_at": datetime.utcnow().isoformat()},
                {"role": "assistant", "content": "Hi there!", "created_at": datetime.utcnow().isoformat()}
            ]
        }
        
        # 模拟创建对话和消息
        mock_conversation = Mock()
        mock_conversation.id = str(uuid.uuid4())
        
        with patch.object(conversation_service, '_create_conversation_model', return_value=mock_conversation):
            with patch.object(mock_message_service, 'import_messages', return_value=2):
                # 导入对话
                result = await conversation_service.import_conversation(
                    session_id=sample_session_data["id"],
                    conversation_data=import_data
                )
        
        # 验证结果
        assert result["conversation_id"] == mock_conversation.id
        assert result["imported_messages"] == 2
    
    async def test_get_conversation_summary_success(self, conversation_service: ConversationService, mock_db_session, mock_message_service, sample_conversation_model):
        """测试成功获取对话摘要"""
        # 设置模拟返回
        mock_db_session.scalar.return_value = sample_conversation_model
        
        # 模拟消息统计
        mock_message_service.count_by_conversation_id.return_value = 10
        
        # 获取对话摘要
        result = await conversation_service.get_conversation_summary(sample_conversation_model["id"])
        
        # 验证结果
        assert result["id"] == sample_conversation_model["id"]
        assert result["title"] == sample_conversation_model["title"]
        assert result["message_count"] == 10
        assert "created_at" in result
        assert "last_message_at" in result
    
    async def test_batch_update_conversations_success(self, conversation_service: ConversationService, mock_db_session, sample_conversation_model):
        """测试成功批量更新对话"""
        # 设置模拟返回
        conversations = [sample_conversation_model, sample_conversation_model.copy()]
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = conversations
        mock_db_session.execute.return_value = mock_result
        
        # 批量更新数据
        update_data = {
            "metadata": {"batch_updated": True}
        }
        
        # 批量更新对话
        result = await conversation_service.batch_update_conversations(
            conversation_ids=[sample_conversation_model["id"]],
            update_data=update_data
        )
        
        # 验证结果
        assert result == 2  # 更新了2个对话
        mock_db_session.commit.assert_called_once()
    
    async def test_get_recent_conversations_success(self, conversation_service: ConversationService, mock_cache_service, sample_conversation_model):
        """测试成功获取最近对话"""
        # 设置模拟返回
        cached_conversations = [sample_conversation_model]
        mock_cache_service.get_list.return_value = cached_conversations
        
        # 获取最近对话
        result = await conversation_service.get_recent_conversations(
            session_id=sample_conversation_model["session_id"],
            limit=10
        )
        
        # 验证结果
        assert len(result) == 1
        assert result[0] == sample_conversation_model
    
    async def test_cache_conversation_success(self, conversation_service: ConversationService, mock_cache_service, sample_conversation_model):
        """测试成功缓存对话"""
        # 缓存对话
        await conversation_service.cache_conversation(sample_conversation_model)
        
        # 验证缓存调用
        mock_cache_service.set.assert_called_once()
        mock_cache_service.add_to_list.assert_called_once()
    
    async def test_invalidate_conversation_cache_success(self, conversation_service: ConversationService, mock_cache_service):
        """测试成功清除对话缓存"""
        # 清除缓存
        session_id = "test_session_id"
        await conversation_service.invalidate_conversation_cache(session_id)
        
        # 验证缓存删除调用
        mock_cache_service.delete.assert_called()
    
    async def test_validate_conversation_data_success(self, conversation_service: ConversationService):
        """测试成功验证对话数据"""
        # 验证有效数据
        valid_data = {
            "title": "Valid Conversation",
            "model_name": "gpt-3.5-turbo",
            "temperature": 0.7,
            "max_tokens": 2048
        }
        
        is_valid = await conversation_service.validate_conversation_data(valid_data)
        
        # 验证结果
        assert is_valid is True
    
    async def test_validate_conversation_data_invalid_temperature(self, conversation_service: ConversationService):
        """测试验证无效温度的对话数据"""
        # 验证无效温度
        invalid_data = {
            "title": "Test Conversation",
            "model_name": "gpt-3.5-turbo",
            "temperature": 2.5,  # 超出范围
            "max_tokens": 2048
        }
        
        with pytest.raises(ValidationAPIError) as exc_info:
            await conversation_service.validate_conversation_data(invalid_data)
        
        assert "invalid temperature" in str(exc_info.value).lower()
    
    async def test_validate_conversation_data_invalid_max_tokens(self, conversation_service: ConversationService):
        """测试验证无效最大令牌数的对话数据"""
        # 验证无效最大令牌数
        invalid_data = {
            "title": "Test Conversation",
            "model_name": "gpt-3.5-turbo",
            "temperature": 0.7,
            "max_tokens": -100  # 负数
        }
        
        with pytest.raises(ValidationAPIError) as exc_info:
            await conversation_service.validate_conversation_data(invalid_data)
        
        assert "invalid max_tokens" in str(exc_info.value).lower()
    
    async def test_generate_conversation_title_success(self, conversation_service: ConversationService):
        """测试成功生成对话标题"""
        # 基于消息内容生成标题
        messages = [
            {"role": "user", "content": "What is artificial intelligence?"},
            {"role": "assistant", "content": "Artificial intelligence (AI) is..."}
        ]
        
        title = await conversation_service.generate_conversation_title(messages)
        
        # 验证结果
        assert isinstance(title, str)
        assert len(title) > 0
        assert len(title) <= 100  # 标题长度限制
    
    async def test_get_conversation_analytics_success(self, conversation_service: ConversationService, mock_db_session):
        """测试成功获取对话分析"""
        # 设置模拟返回
        analytics_data = {
            "total_conversations": 50,
            "active_conversations": 30,
            "archived_conversations": 20,
            "conversations_by_model": {"gpt-3.5-turbo": 30, "gpt-4": 20},
            "average_messages_per_conversation": 15.5,
            "total_tokens": 50000
        }
        
        with patch.object(conversation_service, '_calculate_analytics', return_value=analytics_data):
            # 获取分析数据
            result = await conversation_service.get_conversation_analytics(
                session_id="test_session_id"
            )
        
        # 验证结果
        assert result["total_conversations"] == 50
        assert result["active_conversations"] == 30
        assert result["conversations_by_model"]["gpt-3.5-turbo"] == 30
        assert result["average_messages_per_conversation"] == 15.5


@pytest.mark.service
@pytest.mark.unit
class TestConversationServiceEdgeCases(MockTestCase):
    """对话服务边界情况测试类"""
    
    @pytest.fixture
    def conversation_service(self):
        """创建对话服务实例"""
        return ConversationService()
    
    @pytest.fixture
    def memory_monitor(self):
        """内存监控器"""
        class MemoryMonitor:
            def __init__(self):
                self.peak_memory = 0
                self.current_memory = 0
            
            def update_memory(self, memory_mb):
                self.current_memory = memory_mb
                if memory_mb > self.peak_memory:
                    self.peak_memory = memory_mb
        
        return MemoryMonitor()
    
    @pytest.fixture
    def performance_timer(self):
        """性能计时器"""
        class PerformanceTimer:
            def __init__(self):
                self.start_time = None
                self.end_time = None
                self.elapsed = 0
            
            def __enter__(self):
                import time
                self.start_time = time.time()
                return self
            
            def __exit__(self, exc_type, exc_val, exc_tb):
                import time
                self.end_time = time.time()
                self.elapsed = self.end_time - self.start_time
        
        return PerformanceTimer()
    
    async def test_create_conversation_with_empty_title(self, conversation_service: ConversationService):
        """测试创建空标题的对话"""
        conversation_data = {
            "session_id": str(uuid.uuid4()),
            "title": "",  # 空标题
            "model_name": "gpt-3.5-turbo"
        }
        
        with patch.object(conversation_service, 'session_service') as mock_session_service:
            mock_session_service.get_by_id.return_value = {"id": conversation_data["session_id"]}
            
            with patch.object(conversation_service, 'db_session') as mock_session:
                mock_conversation = Mock()
                mock_conversation.id = str(uuid.uuid4())
                mock_conversation.title = "New Conversation"  # 自动生成标题
                
                with patch.object(conversation_service, '_create_conversation_model', return_value=mock_conversation):
                    # 创建对话
                    result = await conversation_service.create(conversation_data)
                
                # 验证自动生成标题
                assert result.title == "New Conversation"
    
    async def test_create_conversation_with_very_long_title(self, conversation_service: ConversationService):
        """测试创建超长标题的对话"""
        # 创建超长标题
        long_title = "x" * 500  # 500字符标题
        
        conversation_data = {
            "session_id": str(uuid.uuid4()),
            "title": long_title,
            "model_name": "gpt-3.5-turbo"
        }
        
        # 验证标题长度限制
        with pytest.raises(ValidationAPIError) as exc_info:
            await conversation_service.validate_conversation_data(conversation_data)
        
        assert "title too long" in str(exc_info.value).lower()
    
    async def test_create_conversation_with_special_characters_in_title(self, conversation_service: ConversationService):
        """测试创建包含特殊字符标题的对话"""
        # 包含特殊字符的标题
        special_title = "Test 🚀 Conversation with émojis & spëcial chars!"
        
        conversation_data = {
            "session_id": str(uuid.uuid4()),
            "title": special_title,
            "model_name": "gpt-3.5-turbo"
        }
        
        with patch.object(conversation_service, 'session_service') as mock_session_service:
            mock_session_service.get_by_id.return_value = {"id": conversation_data["session_id"]}
            
            with patch.object(conversation_service, 'db_session') as mock_session:
                mock_conversation = Mock()
                mock_conversation.id = str(uuid.uuid4())
                mock_conversation.title = special_title
                
                with patch.object(conversation_service, '_create_conversation_model', return_value=mock_conversation):
                    # 创建对话
                    result = await conversation_service.create(conversation_data)
                
                # 验证特殊字符被正确处理
                assert result.title == special_title
    
    async def test_database_connection_error(self, conversation_service: ConversationService):
        """测试数据库连接错误"""
        conversation_data = {
            "session_id": str(uuid.uuid4()),
            "title": "Test Conversation",
            "model_name": "gpt-3.5-turbo"
        }
        
        with patch.object(conversation_service, 'session_service') as mock_session_service:
            mock_session_service.get_by_id.return_value = {"id": conversation_data["session_id"]}
            
            with patch.object(conversation_service, 'db_session') as mock_session:
                # 模拟数据库连接错误
                mock_session.commit.side_effect = Exception("Database connection lost")
                
                with patch.object(conversation_service, '_create_conversation_model'):
                    # 尝试创建对话
                    with pytest.raises(DatabaseAPIError) as exc_info:
                        await conversation_service.create(conversation_data)
                    
                    assert "database error" in str(exc_info.value).lower()
    
    async def test_concurrent_conversation_creation(self, conversation_service: ConversationService, memory_monitor):
        """测试并发对话创建"""
        import asyncio
        
        async def create_conversation(index):
            conversation_data = {
                "session_id": str(uuid.uuid4()),
                "title": f"Conversation {index}",
                "model_name": "gpt-3.5-turbo"
            }
            
            with patch.object(conversation_service, 'session_service') as mock_session_service:
                mock_session_service.get_by_id.return_value = {"id": conversation_data["session_id"]}
                
                with patch.object(conversation_service, 'db_session') as mock_session:
                    mock_conversation = Mock()
                    mock_conversation.id = str(uuid.uuid4())
                    mock_conversation.title = conversation_data["title"]
                    
                    with patch.object(conversation_service, '_create_conversation_model', return_value=mock_conversation):
                        return await conversation_service.create(conversation_data)
            return None

        # 创建多个并发任务
        tasks = [create_conversation(i) for i in range(10)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 验证并发处理
        successful_results = [r for r in results if not isinstance(r, Exception)]
        assert len(successful_results) >= 0  # 至少部分成功
        
        # 监控内存使用
        memory_monitor.update_memory(50)  # 模拟内存使用
        assert memory_monitor.current_memory <= 100  # 内存使用合理
    
    async def test_conversation_with_extreme_parameters(self, conversation_service: ConversationService):
        """测试极端参数的对话"""
        # 极端参数
        extreme_data = {
            "session_id": str(uuid.uuid4()),
            "title": "Extreme Test",
            "model_name": "gpt-3.5-turbo",
            "temperature": 0.0,  # 最小温度
            "max_tokens": 1,     # 最小令牌数
            "top_p": 0.1,       # 最小top_p
            "frequency_penalty": -2.0,  # 最小频率惩罚
            "presence_penalty": -2.0    # 最小存在惩罚
        }
        
        # 验证极端参数
        is_valid = await conversation_service.validate_conversation_parameters(extreme_data)
        assert is_valid is True
        
        # 测试另一组极端参数
        extreme_data2 = {
            "session_id": str(uuid.uuid4()),
            "title": "Extreme Test 2",
            "model_name": "gpt-3.5-turbo",
            "temperature": 2.0,  # 最大温度
            "max_tokens": 4096,  # 最大令牌数
            "top_p": 1.0,       # 最大top_p
            "frequency_penalty": 2.0,   # 最大频率惩罚
            "presence_penalty": 2.0     # 最大存在惩罚
        }
        
        is_valid2 = await conversation_service.validate_conversation_parameters(extreme_data2)
        assert is_valid2 is True
    
    async def test_conversation_with_none_values(self, conversation_service: ConversationService):
        """测试包含None值的对话"""
        conversation_data = {
            "session_id": str(uuid.uuid4()),
            "title": "Test Conversation",
            "model_name": "gpt-3.5-turbo",
            "system_prompt": None,  # None系统提示
            "metadata": None,       # None元数据
            "temperature": None,    # None温度（应使用默认值）
            "max_tokens": None      # None最大令牌数（应使用默认值）
        }
        
        with patch.object(conversation_service, 'session_service') as mock_session_service:
            mock_session_service.get_by_id.return_value = {"id": conversation_data["session_id"]}
            
            with patch.object(conversation_service, 'db_session') as mock_session:
                mock_conversation = Mock()
                mock_conversation.id = str(uuid.uuid4())
                mock_conversation.system_prompt = None
                mock_conversation.metadata = None
                mock_conversation.temperature = 0.7  # 默认值
                mock_conversation.max_tokens = 2048  # 默认值
                
                with patch.object(conversation_service, '_create_conversation_model', return_value=mock_conversation):
                    # 创建对话
                    result = await conversation_service.create(conversation_data)
                
                # 验证None值被正确处理
                assert result.system_prompt is None
                assert result.metadata is None
                assert result.temperature == 0.7  # 使用默认值
                assert result.max_tokens == 2048   # 使用默认值
    
    async def test_large_dataset_memory_usage(self, conversation_service: ConversationService, memory_monitor):
        """测试大数据集的内存使用"""
        # 模拟大量对话数据
        large_dataset = []
        for i in range(1000):
            conversation = {
                "id": str(uuid.uuid4()),
                "session_id": str(uuid.uuid4()),
                "title": f"Conversation {i}" * 5,  # 较长标题
                "model_name": "gpt-3.5-turbo",
                "created_at": datetime.utcnow(),
                "metadata": {"index": i, "data": "x" * 100}  # 一些元数据
            }
            large_dataset.append(conversation)
        
        with patch.object(conversation_service, 'db_session') as mock_session:
            mock_result = Mock()
            mock_result.scalars.return_value.all.return_value = large_dataset
            mock_session.execute.return_value = mock_result
            
            # 获取大量对话
            result = await conversation_service.get_by_session_id("test_session_id")
        
        # 监控内存使用
        memory_monitor.update_memory(200)  # 模拟内存使用
        assert memory_monitor.peak_memory <= 500  # 内存使用应该合理
        assert len(result) == 1000
    
    async def test_frequent_operations_performance(self, conversation_service: ConversationService, performance_timer):
        """测试频繁操作的性能"""
        # 模拟频繁的对话操作
        operations_count = 100
        
        with performance_timer:
            for i in range(operations_count):
                # 模拟对话验证操作
                data = {
                    "title": f"Test {i}",
                    "model_name": "gpt-3.5-turbo",
                    "temperature": 0.7
                }
                await conversation_service.validate_conversation_data(data)
        
        # 验证性能
        avg_time_per_operation = performance_timer.elapsed / operations_count
        assert avg_time_per_operation < 0.01  # 每个操作应该小于10ms
    
    async def test_conversation_title_sanitization(self, conversation_service: ConversationService):
        """测试对话标题清理"""
        # 包含潜在危险内容的标题
        dangerous_title = "<script>alert('xss')</script>Normal Title"
        
        # 清理标题
        sanitized_title = await conversation_service.sanitize_title(dangerous_title)
        
        # 验证危险内容被移除
        assert "<script>" not in sanitized_title
        assert "alert" not in sanitized_title
        assert "Normal Title" in sanitized_title
    
    async def test_conversation_backup_and_recovery(self, conversation_service: ConversationService):
        """测试对话备份和恢复"""
        # 模拟对话数据
        conversation_data = {
            "id": str(uuid.uuid4()),
            "session_id": str(uuid.uuid4()),
            "title": "Test Conversation",
            "model_name": "gpt-3.5-turbo",
            "created_at": datetime.utcnow().isoformat(),
            "metadata": {"backup_test": True}
        }
        
        # 备份对话
        backup_data = await conversation_service.backup_conversation(conversation_data["id"])
        
        # 验证备份数据
        assert isinstance(backup_data, str)
        backup_json = json.loads(backup_data)
        assert "conversation" in backup_json
        assert "messages" in backup_json
        assert "metadata" in backup_json
        
        # 恢复对话
        with patch.object(conversation_service, 'session_service') as mock_session_service:
            mock_session_service.get_by_id.return_value = {"id": conversation_data["session_id"]}
            
            with patch.object(conversation_service, 'db_session') as mock_session:
                with patch.object(conversation_service, '_create_conversation_model'):
                    restored_id = await conversation_service.restore_conversation(backup_data)
        
        # 验证恢复结果
        assert isinstance(restored_id, str)
    
    async def test_conversation_merge_functionality(self, conversation_service: ConversationService):
        """测试对话合并功能"""
        # 模拟两个对话
        conversation1 = {
            "id": str(uuid.uuid4()),
            "title": "Conversation 1",
            "messages": [
                {"role": "user", "content": "Hello"},
                {"role": "assistant", "content": "Hi there!"}
            ]
        }
        
        conversation2 = {
            "id": str(uuid.uuid4()),
            "title": "Conversation 2",
            "messages": [
                {"role": "user", "content": "How are you?"},
                {"role": "assistant", "content": "I'm doing well!"}
            ]
        }
        
        with patch.object(conversation_service, 'db_session') as mock_session:
            with patch.object(conversation_service, 'message_service') as mock_message_service:
                # 合并对话
                merged_id = await conversation_service.merge_conversations(
                    [conversation1["id"], conversation2["id"]],
                    new_title="Merged Conversation"
                )
        
        # 验证合并结果
        assert isinstance(merged_id, str)
        assert merged_id != conversation1["id"]
        assert merged_id != conversation2["id"]
    
    async def test_conversation_analytics_calculation(self, conversation_service: ConversationService):
        """测试对话分析计算"""
        # 模拟对话数据
        conversations_data = [
            {
                "id": str(uuid.uuid4()),
                "model_name": "gpt-3.5-turbo",
                "message_count": 10,
                "total_tokens": 500,
                "created_at": datetime.utcnow() - timedelta(days=1),
                "is_active": True
            },
            {
                "id": str(uuid.uuid4()),
                "model_name": "gpt-4",
                "message_count": 5,
                "total_tokens": 300,
                "created_at": datetime.utcnow() - timedelta(days=2),
                "is_active": False
            }
        ]
        
        # 计算分析数据
        analytics = await conversation_service.calculate_detailed_analytics(conversations_data)
        
        # 验证分析结果
        assert analytics["total_conversations"] == 2
        assert analytics["active_conversations"] == 1
        assert analytics["inactive_conversations"] == 1
        assert analytics["total_messages"] == 15
        assert analytics["total_tokens"] == 800
        assert analytics["average_messages_per_conversation"] == 7.5
        assert "gpt-3.5-turbo" in analytics["conversations_by_model"]
        assert "gpt-4" in analytics["conversations_by_model"]