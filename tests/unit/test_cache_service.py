"""缓存服务单元测试"""

import pytest
from unittest.mock import Mo<PERSON>, AsyncMock, patch
import json
import time
from datetime import datetime, timedelta

from services.cache_service import CacheService
from tests.utils.test_helpers import AsyncTestCase, MockTestCase


@pytest.mark.service
@pytest.mark.unit
class TestCacheService(MockTestCase):
    """缓存服务测试类"""
    
    @pytest.fixture
    def mock_redis(self):
        """模拟Redis客户端"""
        redis_mock = Mock()
        redis_mock.get = Mock(return_value=None)
        redis_mock.set = Mock(return_value=True)
        redis_mock.delete = Mock(return_value=1)
        redis_mock.exists = Mock(return_value=False)
        redis_mock.expire = Mock(return_value=True)
        redis_mock.ttl = Mock(return_value=-1)
        redis_mock.keys = Mock(return_value=[])
        redis_mock.flushdb = Mock(return_value=True)
        redis_mock.ping = Mock(return_value=True)
        return redis_mock
    
    @pytest.fixture
    def cache_service(self, mock_redis):
        """创建缓存服务实例"""
        with patch('services.cache_service.redis.Redis', return_value=mock_redis):
            service = CacheService()
            service.redis_client = mock_redis
            return service
        return None

    @pytest.fixture
    def sample_data(self):
        """示例缓存数据"""
        return {
            "user_id": "123",
            "username": "testuser",
            "email": "<EMAIL>",
            "created_at": "2024-01-01T00:00:00Z",
            "preferences": {
                "theme": "dark",
                "language": "zh-CN"
            }
        }
    
    def test_set_cache_success(self, cache_service: CacheService, mock_redis, sample_data):
        """测试成功设置缓存"""
        key = "user:123"
        ttl = 3600
        
        # 设置缓存
        result = cache_service.set(key, sample_data, ttl)
        
        # 验证调用
        mock_redis.set.assert_called_once()
        call_args = mock_redis.set.call_args
        assert call_args[0][0] == key
        assert json.loads(call_args[0][1]) == sample_data
        assert call_args[1]['ex'] == ttl
        
        assert result is True
    
    def test_set_cache_without_ttl(self, cache_service: CacheService, mock_redis, sample_data):
        """测试设置缓存不指定TTL"""
        key = "user:123"
        
        # 设置缓存
        cache_service.set(key, sample_data)
        
        # 验证调用（不应该有ex参数）
        mock_redis.set.assert_called_once()
        call_args = mock_redis.set.call_args
        assert 'ex' not in call_args[1]
    
    def test_get_cache_success(self, cache_service: CacheService, mock_redis, sample_data):
        """测试成功获取缓存"""
        key = "user:123"
        
        # 模拟Redis返回数据
        mock_redis.get.return_value = json.dumps(sample_data).encode('utf-8')
        
        # 获取缓存
        result = cache_service.get(key)
        
        # 验证调用和结果
        mock_redis.get.assert_called_once_with(key)
        assert result == sample_data
    
    def test_get_cache_not_found(self, cache_service: CacheService, mock_redis):
        """测试获取不存在的缓存"""
        key = "nonexistent:key"
        
        # 模拟Redis返回None
        mock_redis.get.return_value = None
        
        # 获取缓存
        result = cache_service.get(key)
        
        # 验证结果
        assert result is None
    
    def test_get_cache_invalid_json(self, cache_service: CacheService, mock_redis):
        """测试获取无效JSON缓存"""
        key = "invalid:json"
        
        # 模拟Redis返回无效JSON
        mock_redis.get.return_value = b"invalid json data"
        
        # 获取缓存
        result = cache_service.get(key)
        
        # 应该返回None而不是抛出异常
        assert result is None
    
    def test_delete_cache_success(self, cache_service: CacheService, mock_redis):
        """测试成功删除缓存"""
        key = "user:123"
        
        # 模拟删除成功
        mock_redis.delete.return_value = 1
        
        # 删除缓存
        result = cache_service.delete(key)
        
        # 验证调用和结果
        mock_redis.delete.assert_called_once_with(key)
        assert result is True
    
    def test_delete_cache_not_found(self, cache_service: CacheService, mock_redis):
        """测试删除不存在的缓存"""
        key = "nonexistent:key"
        
        # 模拟删除失败（键不存在）
        mock_redis.delete.return_value = 0
        
        # 删除缓存
        result = cache_service.delete(key)
        
        # 验证结果
        assert result is False
    
    def test_exists_cache_true(self, cache_service: CacheService, mock_redis):
        """测试检查缓存存在"""
        key = "user:123"
        
        # 模拟键存在
        mock_redis.exists.return_value = True
        
        # 检查存在性
        result = cache_service.exists(key)
        
        # 验证调用和结果
        mock_redis.exists.assert_called_once_with(key)
        assert result is True
    
    def test_exists_cache_false(self, cache_service: CacheService, mock_redis):
        """测试检查缓存不存在"""
        key = "nonexistent:key"
        
        # 模拟键不存在
        mock_redis.exists.return_value = False
        
        # 检查存在性
        result = cache_service.exists(key)
        
        # 验证结果
        assert result is False
    
    def test_set_ttl_success(self, cache_service: CacheService, mock_redis):
        """测试成功设置TTL"""
        key = "user:123"
        ttl = 1800
        
        # 模拟设置TTL成功
        mock_redis.expire.return_value = True
        
        # 设置TTL
        result = cache_service.set_ttl(key, ttl)
        
        # 验证调用和结果
        mock_redis.expire.assert_called_once_with(key, ttl)
        assert result is True
    
    def test_get_ttl_success(self, cache_service: CacheService, mock_redis):
        """测试成功获取TTL"""
        key = "user:123"
        expected_ttl = 1800
        
        # 模拟获取TTL
        mock_redis.ttl.return_value = expected_ttl
        
        # 获取TTL
        result = cache_service.get_ttl(key)
        
        # 验证调用和结果
        mock_redis.ttl.assert_called_once_with(key)
        assert result == expected_ttl
    
    def test_get_ttl_no_expiry(self, cache_service: CacheService, mock_redis):
        """测试获取无过期时间的键的TTL"""
        key = "persistent:key"
        
        # 模拟无过期时间（返回-1）
        mock_redis.ttl.return_value = -1
        
        # 获取TTL
        result = cache_service.get_ttl(key)
        
        # 验证结果
        assert result == -1
    
    def test_get_keys_by_pattern(self, cache_service: CacheService, mock_redis):
        """测试按模式获取键"""
        pattern = "user:*"
        expected_keys = [b"user:123", b"user:456", b"user:789"]
        
        # 模拟获取键
        mock_redis.keys.return_value = expected_keys
        
        # 获取键
        result = cache_service.get_keys(pattern)
        
        # 验证调用和结果
        mock_redis.keys.assert_called_once_with(pattern)
        assert result == [key.decode('utf-8') for key in expected_keys]
    
    def test_clear_cache_success(self, cache_service: CacheService, mock_redis):
        """测试成功清空缓存"""
        # 模拟清空成功
        mock_redis.flushdb.return_value = True
        
        # 清空缓存
        result = cache_service.clear()
        
        # 验证调用和结果
        mock_redis.flushdb.assert_called_once()
        assert result is True
    
    def test_ping_success(self, cache_service: CacheService, mock_redis):
        """测试成功ping Redis"""
        # 模拟ping成功
        mock_redis.ping.return_value = True
        
        # Ping Redis
        result = cache_service.ping()
        
        # 验证调用和结果
        mock_redis.ping.assert_called_once()
        assert result is True
    
    def test_get_or_set_cache_hit(self, cache_service: CacheService, mock_redis, sample_data):
        """测试缓存命中的get_or_set"""
        key = "user:123"
        
        # 模拟缓存命中
        mock_redis.get.return_value = json.dumps(sample_data).encode('utf-8')
        
        # 定义回调函数（不应该被调用）
        callback = Mock(return_value={"new": "data"})
        
        # 获取或设置缓存
        result = cache_service.get_or_set(key, callback, ttl=3600)
        
        # 验证结果和回调未被调用
        assert result == sample_data
        callback.assert_not_called()
        mock_redis.set.assert_not_called()
    
    def test_get_or_set_cache_miss(self, cache_service: CacheService, mock_redis):
        """测试缓存未命中的get_or_set"""
        key = "user:123"
        new_data = {"new": "data"}
        
        # 模拟缓存未命中
        mock_redis.get.return_value = None
        
        # 定义回调函数
        callback = Mock(return_value=new_data)
        
        # 获取或设置缓存
        result = cache_service.get_or_set(key, callback, ttl=3600)
        
        # 验证结果和回调被调用
        assert result == new_data
        callback.assert_called_once()
        mock_redis.set.assert_called_once()
    
    def test_increment_counter(self, cache_service: CacheService, mock_redis):
        """测试递增计数器"""
        key = "counter:api_calls"
        
        # 模拟递增操作
        mock_redis.incr = Mock(return_value=5)
        
        # 递增计数器
        result = cache_service.increment(key)
        
        # 验证调用和结果
        mock_redis.incr.assert_called_once_with(key)
        assert result == 5
    
    def test_increment_counter_with_amount(self, cache_service: CacheService, mock_redis):
        """测试按指定数量递增计数器"""
        key = "counter:api_calls"
        amount = 10
        
        # 模拟递增操作
        mock_redis.incr = Mock(return_value=15)
        
        # 递增计数器
        result = cache_service.increment(key, amount)
        
        # 验证调用和结果
        mock_redis.incr.assert_called_once_with(key, amount)
        assert result == 15
    
    def test_decrement_counter(self, cache_service: CacheService, mock_redis):
        """测试递减计数器"""
        key = "counter:api_calls"
        
        # 模拟递减操作
        mock_redis.decr = Mock(return_value=3)
        
        # 递减计数器
        result = cache_service.decrement(key)
        
        # 验证调用和结果
        mock_redis.decr.assert_called_once_with(key)
        assert result == 3
    
    def test_hash_operations(self, cache_service: CacheService, mock_redis):
        """测试哈希操作"""
        key = "user:123:profile"
        field = "email"
        value = "<EMAIL>"
        
        # 模拟哈希操作
        mock_redis.hset = Mock(return_value=1)
        mock_redis.hget = Mock(return_value=value.encode('utf-8'))
        mock_redis.hdel = Mock(return_value=1)
        mock_redis.hexists = Mock(return_value=True)
        
        # 设置哈希字段
        set_result = cache_service.hset(key, field, value)
        assert set_result is True
        mock_redis.hset.assert_called_once_with(key, field, value)
        
        # 获取哈希字段
        get_result = cache_service.hget(key, field)
        assert get_result == value
        mock_redis.hget.assert_called_once_with(key, field)
        
        # 检查哈希字段存在
        exists_result = cache_service.hexists(key, field)
        assert exists_result is True
        mock_redis.hexists.assert_called_once_with(key, field)
        
        # 删除哈希字段
        del_result = cache_service.hdel(key, field)
        assert del_result is True
        mock_redis.hdel.assert_called_once_with(key, field)
    
    def test_list_operations(self, cache_service: CacheService, mock_redis):
        """测试列表操作"""
        key = "queue:tasks"
        value = "task_data"
        
        # 模拟列表操作
        mock_redis.lpush = Mock(return_value=1)
        mock_redis.rpush = Mock(return_value=2)
        mock_redis.lpop = Mock(return_value=value.encode('utf-8'))
        mock_redis.rpop = Mock(return_value=value.encode('utf-8'))
        mock_redis.llen = Mock(return_value=5)
        
        # 左推
        lpush_result = cache_service.lpush(key, value)
        assert lpush_result == 1
        mock_redis.lpush.assert_called_once_with(key, value)
        
        # 右推
        rpush_result = cache_service.rpush(key, value)
        assert rpush_result == 2
        mock_redis.rpush.assert_called_once_with(key, value)
        
        # 左弹
        lpop_result = cache_service.lpop(key)
        assert lpop_result == value
        mock_redis.lpop.assert_called_once_with(key)
        
        # 右弹
        rpop_result = cache_service.rpop(key)
        assert rpop_result == value
        mock_redis.rpop.assert_called_once_with(key)
        
        # 获取长度
        llen_result = cache_service.llen(key)
        assert llen_result == 5
        mock_redis.llen.assert_called_once_with(key)
    
    def test_set_operations(self, cache_service: CacheService, mock_redis):
        """测试集合操作"""
        key = "set:tags"
        member = "python"
        
        # 模拟集合操作
        mock_redis.sadd = Mock(return_value=1)
        mock_redis.srem = Mock(return_value=1)
        mock_redis.sismember = Mock(return_value=True)
        mock_redis.smembers = Mock(return_value={b"python", b"javascript"})
        mock_redis.scard = Mock(return_value=2)
        
        # 添加成员
        sadd_result = cache_service.sadd(key, member)
        assert sadd_result == 1
        mock_redis.sadd.assert_called_once_with(key, member)
        
        # 检查成员存在
        sismember_result = cache_service.sismember(key, member)
        assert sismember_result is True
        mock_redis.sismember.assert_called_once_with(key, member)
        
        # 获取所有成员
        smembers_result = cache_service.smembers(key)
        assert smembers_result == {"python", "javascript"}
        mock_redis.smembers.assert_called_once_with(key)
        
        # 获取集合大小
        scard_result = cache_service.scard(key)
        assert scard_result == 2
        mock_redis.scard.assert_called_once_with(key)
        
        # 删除成员
        srem_result = cache_service.srem(key, member)
        assert srem_result == 1
        mock_redis.srem.assert_called_once_with(key, member)


@pytest.mark.service
@pytest.mark.unit
class TestCacheServiceEdgeCases(MockTestCase):
    """缓存服务边界情况测试类"""
    
    @pytest.fixture
    def mock_redis(self):
        """模拟Redis客户端"""
        redis_mock = Mock()
        return redis_mock
    
    @pytest.fixture
    def cache_service(self, mock_redis):
        """创建缓存服务实例"""
        with patch('services.cache_service.redis.Redis', return_value=mock_redis):
            service = CacheService()
            service.redis_client = mock_redis
            return service
        return None

    def test_redis_connection_error(self, cache_service: CacheService, mock_redis):
        """测试Redis连接错误"""
        from redis.exceptions import ConnectionError
        
        # 模拟连接错误
        mock_redis.get.side_effect = ConnectionError("Connection failed")
        
        # 获取缓存应该返回None而不是抛出异常
        result = cache_service.get("test:key")
        assert result is None
    
    def test_redis_timeout_error(self, cache_service: CacheService, mock_redis):
        """测试Redis超时错误"""
        from redis.exceptions import TimeoutError
        
        # 模拟超时错误
        mock_redis.set.side_effect = TimeoutError("Operation timed out")
        
        # 设置缓存应该返回False而不是抛出异常
        result = cache_service.set("test:key", {"data": "value"})
        assert result is False
    
    def test_large_data_serialization(self, cache_service: CacheService, mock_redis):
        """测试大数据序列化"""
        # 创建大数据对象
        large_data = {"data": "x" * 10000, "list": list(range(1000))}
        
        # 设置缓存
        cache_service.set("large:data", large_data)
        
        # 验证数据被正确序列化
        mock_redis.set.assert_called_once()
        call_args = mock_redis.set.call_args
        serialized_data = json.loads(call_args[0][1])
        assert serialized_data == large_data
    
    def test_unicode_data_handling(self, cache_service: CacheService, mock_redis):
        """测试Unicode数据处理"""
        unicode_data = {
            "chinese": "你好世界",
            "japanese": "こんにちは",
            "emoji": "😀🎉🚀",
            "special": "\u0000\u001f\u007f"
        }
        
        # 设置缓存
        cache_service.set("unicode:data", unicode_data)
        
        # 验证Unicode数据被正确处理
        mock_redis.set.assert_called_once()
        call_args = mock_redis.set.call_args
        serialized_data = json.loads(call_args[0][1])
        assert serialized_data == unicode_data
    
    def test_none_value_handling(self, cache_service: CacheService, mock_redis):
        """测试None值处理"""
        # 设置None值
        cache_service.set("none:value", None)
        
        # 验证None值被正确序列化
        mock_redis.set.assert_called_once()
        call_args = mock_redis.set.call_args
        assert call_args[0][1] == "null"
    
    def test_empty_string_key(self, cache_service: CacheService, mock_redis):
        """测试空字符串键"""
        # 尝试使用空字符串作为键
        result = cache_service.set("", {"data": "value"})
        
        # 应该正常处理
        assert result is True
        mock_redis.set.assert_called_once_with("", '{"data": "value"}')
    
    def test_very_long_key(self, cache_service: CacheService, mock_redis):
        """测试超长键名"""
        long_key = "x" * 1000
        
        # 设置缓存
        cache_service.set(long_key, {"data": "value"})
        
        # 验证长键名被正确处理
        mock_redis.set.assert_called_once()
        call_args = mock_redis.set.call_args
        assert call_args[0][0] == long_key
    
    def test_negative_ttl(self, cache_service: CacheService, mock_redis):
        """测试负数TTL"""
        # 使用负数TTL
        cache_service.set("test:key", {"data": "value"}, ttl=-1)
        
        # 验证负数TTL被正确处理（应该不设置过期时间）
        mock_redis.set.assert_called_once()
        call_args = mock_redis.set.call_args
        assert 'ex' not in call_args[1] or call_args[1]['ex'] >= 0
    
    def test_zero_ttl(self, cache_service: CacheService, mock_redis):
        """测试零TTL"""
        # 使用零TTL
        cache_service.set("test:key", {"data": "value"}, ttl=0)
        
        # 验证零TTL被正确处理
        mock_redis.set.assert_called_once()
        call_args = mock_redis.set.call_args
        assert call_args[1]['ex'] == 0
    
    def test_concurrent_access(self, cache_service: CacheService, mock_redis):
        """测试并发访问"""
        import threading
        import time
        
        results = []
        errors = []
        
        def cache_operation(index):
            try:
                # 设置缓存
                cache_service.set(f"concurrent:key:{index}", {"index": index})
                # 获取缓存
                result = cache_service.get(f"concurrent:key:{index}")
                results.append(result)
            except Exception as e:
                errors.append(e)
        
        # 创建多个线程同时访问缓存
        threads = []
        for i in range(10):
            thread = threading.Thread(target=cache_operation, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证没有错误发生
        assert len(errors) == 0
        assert len(results) == 10
    
    def test_memory_usage_monitoring(self, cache_service: CacheService, mock_redis, memory_monitor):
        """测试内存使用监控"""
        large_data = {"data": "x" * 100000}  # 大约100KB数据
        
        with memory_monitor:
            # 设置大量缓存数据
            for i in range(100):
                cache_service.set(f"memory:test:{i}", large_data)
        
        # 验证内存使用在合理范围内
        memory_usage = memory_monitor.get_peak_memory()
        assert memory_usage < 50 * 1024 * 1024  # 小于50MB
    
    def test_performance_benchmarking(self, cache_service: CacheService, mock_redis, performance_timer):
        """测试性能基准"""
        test_data = {"key": "value", "number": 123}
        
        with performance_timer:
            # 执行大量缓存操作
            for i in range(1000):
                cache_service.set(f"perf:test:{i}", test_data)
                cache_service.get(f"perf:test:{i}")
        
        # 验证性能在可接受范围内
        elapsed_time = performance_timer.elapsed
        assert elapsed_time < 1.0  # 1000次操作应该在1秒内完成