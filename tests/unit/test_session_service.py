from datetime import datetime, timedelta
from unittest.mock import Mock

import pytest

from services.session_service import SessionService
from tests.utils.test_helpers import MockTestCase


@pytest.mark.service
@pytest.mark.unit
class TestSessionService(MockTestCase):
    """会话服务测试类"""
    
    @pytest.fixture
    def mock_db_session(self):
        """模拟数据库会话"""
        session = Mock()
        session.query = Mock()
        session.add = Mock()
        session.commit = Mock()
        session.rollback = Mock()
        session.close = Mock()
        session.merge = Mock()
        return session
    
    @pytest.fixture
    def mock_cache_service(self):
        """模拟缓存服务"""
        cache = Mock()
        cache.get = Mock(return_value=None)
        cache.set = Mock(return_value=True)
        cache.delete = Mock(return_value=True)
        cache.exists = Mock(return_value=False)
        return cache
    
    @pytest.fixture
    def mock_user_service(self):
        """模拟用户服务"""
        user_service = Mock()
        user_service.get_user_by_id = Mock()
        return user_service
    
    @pytest.fixture
    def session_service(self, mock_db_session, mock_cache_service, mock_user_service):
        """创建会话服务实例"""
        service = SessionService()
        service.db_session = mock_db_session
        service.cache_service = mock_cache_service
        service.user_service = mock_user_service
        return service
    
    @pytest.fixture
    def sample_session_create(self):
        """示例会话创建数据"""
        return {
            "user_id": "user123",
            "title": "测试会话",
            "description": "这是一个测试会话",
            "settings": {
                "model": "gpt-4",
                "temperature": 0.7,
                "max_tokens": 2000
            },
            "metadata": {
                "source": "web",
                "client_version": "1.0.0"
            }
        }
    
    @pytest.fixture
    def sample_session_update(self):
        """示例会话更新数据"""
        return {
            "title": "更新的会话标题",
            "description": "更新的会话描述",
            "settings": {
                "model": "gpt-3.5-turbo",
                "temperature": 0.5,
                "max_tokens": 1500
            }
        }
    
    @pytest.fixture
    def sample_session_model(self):
        """示例会话模型"""
        session = Mock()
        session.id = "session123"
        session.user_id = "user123"
        session.title = "测试会话"
        session.description = "这是一个测试会话"
        session.settings = {
            "model": "gpt-4",
            "temperature": 0.7,
            "max_tokens": 2000
        }
        session.metadata = {
            "source": "web",
            "client_version": "1.0.0"
        }
        session.created_at = datetime.now()
        session.updated_at = datetime.now()
        session.last_activity_at = datetime.now()
        session.is_active = True
        session.conversation_count = 0
        session.message_count = 0
        return session
    
    def test_create_session_success(self, session_service: SessionService, mock_db_session, mock_user_service, sample_session_create):
        """测试成功创建会话"""
        # 模拟用户存在
        mock_user_service.get_user_by_id.return_value = Mock(id="user123")
        
        # 创建会话
        session = session_service.create_session(sample_session_create)
        
        # 验证数据库操作
        mock_db_session.add.assert_called_once()
        mock_db_session.commit.assert_called_once()
        assert session is not None
    
    def test_create_session_user_not_found(self, session_service: SessionService, mock_user_service, sample_session_create):
        """测试用户不存在时创建会话"""
        # 模拟用户不存在
        mock_user_service.get_user_by_id.return_value = None
        
        # 创建会话应该失败
        session = session_service.create_session(sample_session_create)
        
        # 验证返回None
        assert session is None
    
    def test_create_session_invalid_data(self, session_service: SessionService, mock_user_service):
        """测试无效数据创建会话"""
        # 模拟用户存在
        mock_user_service.get_user_by_id.return_value = Mock(id="user123")
        
        # 无效数据（缺少必需字段）
        invalid_data = {"title": ""}
        
        # 创建会话应该失败
        session = session_service.create_session(invalid_data)
        
        # 验证返回None
        assert session is None
    
    def test_get_session_by_id_success(self, session_service: SessionService, mock_db_session, sample_session_model):
        """测试成功通过ID获取会话"""
        session_id = "session123"
        
        # 模拟数据库查询
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = sample_session_model
        mock_db_session.query.return_value = mock_query
        
        # 获取会话
        session = session_service.get_session_by_id(session_id)
        
        # 验证结果
        assert session == sample_session_model
        mock_query.filter.assert_called_once()
    
    def test_get_session_by_id_not_found(self, session_service: SessionService, mock_db_session):
        """测试获取不存在的会话"""
        session_id = "nonexistent"
        
        # 模拟数据库查询返回None
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = None
        mock_db_session.query.return_value = mock_query
        
        # 获取会话
        session = session_service.get_session_by_id(session_id)
        
        # 验证返回None
        assert session is None
    
    def test_get_user_sessions_success(self, session_service: SessionService, mock_db_session, sample_session_model):
        """测试成功获取用户会话列表"""
        user_id = "user123"
        page = 1
        page_size = 10
        
        # 模拟数据库查询
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = [sample_session_model]
        mock_db_session.query.return_value = mock_query
        
        # 获取用户会话
        sessions = session_service.get_user_sessions(user_id, page, page_size)
        
        # 验证结果
        assert len(sessions) == 1
        assert sessions[0] == sample_session_model
    
    def test_update_session_success(self, session_service: SessionService, mock_db_session, sample_session_model, sample_session_update):
        """测试成功更新会话"""
        session_id = "session123"
        
        # 模拟数据库查询
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = sample_session_model
        mock_db_session.query.return_value = mock_query
        
        # 更新会话
        updated_session = session_service.update_session(session_id, sample_session_update)
        
        # 验证数据库操作
        mock_db_session.commit.assert_called_once()
        assert updated_session is not None
    
    def test_update_session_not_found(self, session_service: SessionService, mock_db_session, sample_session_update):
        """测试更新不存在的会话"""
        session_id = "nonexistent"
        
        # 模拟数据库查询返回None
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = None
        mock_db_session.query.return_value = mock_query
        
        # 更新会话
        updated_session = session_service.update_session(session_id, sample_session_update)
        
        # 验证返回None
        assert updated_session is None
    
    def test_delete_session_success(self, session_service: SessionService, mock_db_session, sample_session_model):
        """测试成功删除会话"""
        session_id = "session123"
        
        # 模拟数据库查询
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = sample_session_model
        mock_db_session.query.return_value = mock_query
        
        # 删除会话
        result = session_service.delete_session(session_id)
        
        # 验证数据库操作
        mock_db_session.delete.assert_called_once_with(sample_session_model)
        mock_db_session.commit.assert_called_once()
        assert result is True
    
    def test_delete_session_not_found(self, session_service: SessionService, mock_db_session):
        """测试删除不存在的会话"""
        session_id = "nonexistent"
        
        # 模拟数据库查询返回None
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = None
        mock_db_session.query.return_value = mock_query
        
        # 删除会话
        result = session_service.delete_session(session_id)
        
        # 验证返回False
        assert result is False
    
    def test_update_last_activity_success(self, session_service: SessionService, mock_db_session, sample_session_model):
        """测试成功更新最后活动时间"""
        session_id = "session123"
        
        # 模拟数据库查询
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = sample_session_model
        mock_db_session.query.return_value = mock_query
        
        # 更新最后活动时间
        result = session_service.update_last_activity(session_id)
        
        # 验证数据库操作
        mock_db_session.commit.assert_called_once()
        assert result is True
    
    def test_get_active_sessions_success(self, session_service: SessionService, mock_db_session, sample_session_model):
        """测试成功获取活跃会话"""
        # 模拟数据库查询
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.all.return_value = [sample_session_model]
        mock_db_session.query.return_value = mock_query
        
        # 获取活跃会话
        sessions = session_service.get_active_sessions()
        
        # 验证结果
        assert len(sessions) == 1
        assert sessions[0] == sample_session_model
    
    def test_get_session_statistics_success(self, session_service: SessionService, mock_db_session):
        """测试成功获取会话统计"""
        user_id = "user123"
        
        # 模拟统计查询
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.count.return_value = 5
        mock_query.scalar.return_value = 25
        mock_db_session.query.return_value = mock_query
        
        # 获取统计信息
        stats = session_service.get_session_statistics(user_id)
        
        # 验证结果
        assert "total_sessions" in stats
        assert "total_conversations" in stats
        assert "total_messages" in stats
    
    def test_search_sessions_success(self, session_service: SessionService, mock_db_session, sample_session_model):
        """测试成功搜索会话"""
        user_id = "user123"
        query = "测试"
        
        # 模拟数据库查询
        mock_query_obj = Mock()
        mock_query_obj.filter.return_value = mock_query_obj
        mock_query_obj.order_by.return_value = mock_query_obj
        mock_query_obj.all.return_value = [sample_session_model]
        mock_db_session.query.return_value = mock_query_obj
        
        # 搜索会话
        sessions = session_service.search_sessions(user_id, query)
        
        # 验证结果
        assert len(sessions) == 1
        assert sessions[0] == sample_session_model
    
    def test_archive_session_success(self, session_service: SessionService, mock_db_session, sample_session_model):
        """测试成功归档会话"""
        session_id = "session123"
        
        # 模拟数据库查询
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = sample_session_model
        mock_db_session.query.return_value = mock_query
        
        # 归档会话
        result = session_service.archive_session(session_id)
        
        # 验证数据库操作
        mock_db_session.commit.assert_called_once()
        assert result is True
        assert sample_session_model.is_active is False
    
    def test_restore_session_success(self, session_service: SessionService, mock_db_session, sample_session_model):
        """测试成功恢复会话"""
        session_id = "session123"
        sample_session_model.is_active = False
        
        # 模拟数据库查询
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = sample_session_model
        mock_db_session.query.return_value = mock_query
        
        # 恢复会话
        result = session_service.restore_session(session_id)
        
        # 验证数据库操作
        mock_db_session.commit.assert_called_once()
        assert result is True
        assert sample_session_model.is_active is True
    
    def test_duplicate_session_success(self, session_service: SessionService, mock_db_session, sample_session_model):
        """测试成功复制会话"""
        session_id = "session123"
        
        # 模拟数据库查询
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = sample_session_model
        mock_db_session.query.return_value = mock_query
        
        # 复制会话
        new_session = session_service.duplicate_session(session_id)
        
        # 验证数据库操作
        mock_db_session.add.assert_called_once()
        mock_db_session.commit.assert_called_once()
        assert new_session is not None
    
    def test_export_session_success(self, session_service: SessionService, mock_db_session, sample_session_model):
        """测试成功导出会话"""
        session_id = "session123"
        
        # 模拟数据库查询
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = sample_session_model
        mock_db_session.query.return_value = mock_query
        
        # 导出会话
        export_data = session_service.export_session(session_id)
        
        # 验证结果
        assert "session" in export_data
        assert "conversations" in export_data
        assert "messages" in export_data
    
    def test_import_session_success(self, session_service: SessionService, mock_db_session, mock_user_service):
        """测试成功导入会话"""
        user_id = "user123"
        import_data = {
            "session": {
                "title": "导入的会话",
                "description": "这是一个导入的会话",
                "settings": {"model": "gpt-4"}
            },
            "conversations": [],
            "messages": []
        }
        
        # 模拟用户存在
        mock_user_service.get_user_by_id.return_value = Mock(id=user_id)
        
        # 导入会话
        session = session_service.import_session(user_id, import_data)
        
        # 验证数据库操作
        mock_db_session.add.assert_called()
        mock_db_session.commit.assert_called()
        assert session is not None
    
    def test_get_session_summary_success(self, session_service: SessionService, mock_db_session, sample_session_model):
        """测试成功获取会话摘要"""
        session_id = "session123"
        
        # 模拟数据库查询
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = sample_session_model
        mock_db_session.query.return_value = mock_query
        
        # 获取会话摘要
        summary = session_service.get_session_summary(session_id)
        
        # 验证结果
        assert "session_id" in summary
        assert "title" in summary
        assert "conversation_count" in summary
        assert "message_count" in summary
        assert "created_at" in summary
        assert "last_activity_at" in summary
    
    def test_update_session_settings_success(self, session_service: SessionService, mock_db_session, sample_session_model):
        """测试成功更新会话设置"""
        session_id = "session123"
        new_settings = {
            "model": "gpt-3.5-turbo",
            "temperature": 0.5,
            "max_tokens": 1500
        }
        
        # 模拟数据库查询
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = sample_session_model
        mock_db_session.query.return_value = mock_query
        
        # 更新设置
        result = session_service.update_session_settings(session_id, new_settings)
        
        # 验证数据库操作
        mock_db_session.commit.assert_called_once()
        assert result is True
    
    def test_get_recent_sessions_success(self, session_service: SessionService, mock_db_session, sample_session_model):
        """测试成功获取最近会话"""
        user_id = "user123"
        limit = 5
        
        # 模拟数据库查询
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = [sample_session_model]
        mock_db_session.query.return_value = mock_query
        
        # 获取最近会话
        sessions = session_service.get_recent_sessions(user_id, limit)
        
        # 验证结果
        assert len(sessions) == 1
        assert sessions[0] == sample_session_model


@pytest.mark.service
@pytest.mark.unit
class TestSessionServiceEdgeCases(MockTestCase):
    """会话服务边界情况测试类"""
    
    @pytest.fixture
    def mock_db_session(self):
        """模拟数据库会话"""
        session = Mock()
        return session
    
    @pytest.fixture
    def mock_cache_service(self):
        """模拟缓存服务"""
        cache = Mock()
        return cache
    
    @pytest.fixture
    def mock_user_service(self):
        """模拟用户服务"""
        user_service = Mock()
        return user_service
    
    @pytest.fixture
    def session_service(self, mock_db_session, mock_cache_service, mock_user_service):
        """创建会话服务实例"""
        service = SessionService()
        service.db_session = mock_db_session
        service.cache_service = mock_cache_service
        service.user_service = mock_user_service
        return service
    
    @pytest.fixture
    def memory_monitor(self):
        """内存监控器"""
        return self.create_memory_monitor()
    
    @pytest.fixture
    def performance_timer(self):
        """性能计时器"""
        return self.create_performance_timer()
    
    def test_create_session_empty_title(self, session_service: SessionService, mock_user_service):
        """测试创建空标题会话"""
        # 模拟用户存在
        mock_user_service.get_user_by_id.return_value = Mock(id="user123")
        
        # 空标题数据
        session_data = {
            "user_id": "user123",
            "title": "",
            "description": "测试描述"
        }
        
        # 创建会话应该失败或使用默认标题
        session = session_service.create_session(session_data)
        
        # 验证处理结果
        assert session is None or session.title != ""
    
    def test_create_session_very_long_title(self, session_service: SessionService, mock_user_service, mock_db_session):
        """测试创建超长标题会话"""
        # 模拟用户存在
        mock_user_service.get_user_by_id.return_value = Mock(id="user123")
        
        # 超长标题数据
        long_title = "x" * 1000
        session_data = {
            "user_id": "user123",
            "title": long_title,
            "description": "测试描述"
        }
        
        # 创建会话
        session = session_service.create_session(session_data)
        
        # 验证标题被截断或处理
        if session:
            assert len(session.title) <= 255  # 假设最大长度为255
    
    def test_create_session_special_characters(self, session_service: SessionService, mock_user_service, mock_db_session):
        """测试创建包含特殊字符的会话"""
        # 模拟用户存在
        mock_user_service.get_user_by_id.return_value = Mock(id="user123")
        
        # 包含特殊字符的数据
        session_data = {
            "user_id": "user123",
            "title": "测试会话 @#$%^&*()_+ 🚀🎉",
            "description": "包含特殊字符的描述 <script>alert('xss')</script>",
            "metadata": {
                "special_chars": "\u0000\u001f\u007f"
            }
        }
        
        # 创建会话
        session = session_service.create_session(session_data)
        
        # 验证特殊字符被正确处理
        assert session is not None
        mock_db_session.add.assert_called_once()
    
    def test_database_connection_error(self, session_service: SessionService, mock_db_session, mock_user_service):
        """测试数据库连接错误"""
        # 模拟用户存在
        mock_user_service.get_user_by_id.return_value = Mock(id="user123")
        
        # 模拟数据库错误
        mock_db_session.commit.side_effect = Exception("Database connection failed")
        
        session_data = {
            "user_id": "user123",
            "title": "测试会话",
            "description": "测试描述"
        }
        
        # 创建会话
        session = session_service.create_session(session_data)
        
        # 验证错误处理
        mock_db_session.rollback.assert_called_once()
        assert session is None
    
    def test_concurrent_session_creation(self, session_service: SessionService, mock_user_service, mock_db_session):
        """测试并发会话创建"""
        import threading

        # 模拟用户存在
        mock_user_service.get_user_by_id.return_value = Mock(id="user123")
        
        results = []
        errors = []
        
        def create_session(index):
            try:
                session_data = {
                    "user_id": "user123",
                    "title": f"并发会话 {index}",
                    "description": f"并发创建的会话 {index}"
                }
                session = session_service.create_session(session_data)
                results.append(session)
            except Exception as e:
                errors.append(e)
        
        # 创建多个线程同时创建会话
        threads = []
        for i in range(10):
            thread = threading.Thread(target=create_session, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证没有错误发生
        assert len(errors) == 0
        assert len(results) == 10
    
    def test_unicode_content_handling(self, session_service: SessionService, mock_user_service, mock_db_session):
        """测试Unicode内容处理"""
        # 模拟用户存在
        mock_user_service.get_user_by_id.return_value = Mock(id="user123")
        
        # Unicode内容
        unicode_data = {
            "user_id": "user123",
            "title": "你好世界 こんにちは 🌍",
            "description": "多语言描述 Multilingual description",
            "metadata": {
                "chinese": "中文内容",
                "japanese": "日本語の内容",
                "emoji": "😀🎉🚀"
            }
        }
        
        # 创建会话
        session = session_service.create_session(unicode_data)
        
        # 验证Unicode内容被正确处理
        assert session is not None
        mock_db_session.add.assert_called_once()
    
    def test_none_values_handling(self, session_service: SessionService, mock_user_service, mock_db_session):
        """测试None值处理"""
        # 模拟用户存在
        mock_user_service.get_user_by_id.return_value = Mock(id="user123")
        
        # 包含None值的数据
        session_data = {
            "user_id": "user123",
            "title": "测试会话",
            "description": None,
            "settings": None,
            "metadata": None
        }
        
        # 创建会话
        session = session_service.create_session(session_data)
        
        # 验证None值被正确处理
        assert session is not None
        mock_db_session.add.assert_called_once()
    
    def test_large_metadata_handling(self, session_service: SessionService, mock_user_service, mock_db_session):
        """测试大元数据处理"""
        # 模拟用户存在
        mock_user_service.get_user_by_id.return_value = Mock(id="user123")
        
        # 大元数据
        large_metadata = {
            "large_data": "x" * 100000,  # 100KB数据
            "nested_data": {
                "level1": {
                    "level2": {
                        "level3": list(range(1000))
                    }
                }
            }
        }
        
        session_data = {
            "user_id": "user123",
            "title": "大元数据会话",
            "description": "包含大量元数据的会话",
            "metadata": large_metadata
        }
        
        # 创建会话
        session = session_service.create_session(session_data)
        
        # 验证大元数据被正确处理
        assert session is not None
        mock_db_session.add.assert_called_once()
    
    def test_memory_usage_large_dataset(self, session_service: SessionService, memory_monitor):
        """测试大数据集内存使用"""
        with memory_monitor:
            # 创建大量会话数据
            sessions_data = []
            for i in range(1000):
                session_data = {
                    "user_id": f"user{i}",
                    "title": f"会话 {i}",
                    "description": f"这是第 {i} 个会话",
                    "settings": {
                        "model": "gpt-4",
                        "temperature": 0.7,
                        "max_tokens": 2000
                    },
                    "metadata": {
                        "index": i,
                        "data": "x" * 1000  # 1KB数据
                    }
                }
                sessions_data.append(session_data)
        
        # 验证内存使用在合理范围内
        memory_usage = memory_monitor.get_peak_memory()
        assert memory_usage < 50 * 1024 * 1024  # 小于50MB
    
    def test_performance_session_operations_benchmark(self, session_service: SessionService, mock_user_service, mock_db_session, performance_timer):
        """测试会话操作性能基准"""
        # 模拟用户存在
        mock_user_service.get_user_by_id.return_value = Mock(id="user123")
        
        with performance_timer:
            # 执行大量会话操作
            for i in range(100):
                session_data = {
                    "user_id": "user123",
                    "title": f"性能测试会话 {i}",
                    "description": f"性能测试描述 {i}"
                }
                session_service.create_session(session_data)
        
        # 验证性能在可接受范围内
        elapsed_time = performance_timer.elapsed
        assert elapsed_time < 1.0  # 100次操作应该在1秒内完成
    
    def test_session_data_validation_edge_cases(self, session_service: SessionService, mock_user_service):
        """测试会话数据验证边界情况"""
        # 模拟用户存在
        mock_user_service.get_user_by_id.return_value = Mock(id="user123")
        
        # 测试各种边界情况
        edge_cases = [
            {"user_id": "user123", "title": " "},  # 只有空格的标题
            {"user_id": "user123", "title": "\n\t\r"},  # 只有换行符的标题
            {"user_id": "user123", "title": "正常标题", "settings": "invalid_json"},  # 无效设置
            {"user_id": "user123", "title": "正常标题", "metadata": {"circular": None}},  # 循环引用
        ]
        
        for case in edge_cases:
            session = session_service.create_session(case)
            # 验证边界情况被正确处理（可能返回None或处理后的数据）
            assert session is None or hasattr(session, 'title')
    
    def test_session_cleanup_and_archival(self, session_service: SessionService, mock_db_session):
        """测试会话清理和归档"""
        # 模拟旧会话查询
        old_session = Mock()
        old_session.id = "old_session"
        old_session.last_activity_at = datetime.now() - timedelta(days=365)
        old_session.is_active = True
        
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.all.return_value = [old_session]
        mock_db_session.query.return_value = mock_query
        
        # 执行清理
        result = session_service.cleanup_old_sessions(days=180)
        
        # 验证清理操作
        assert result >= 0  # 返回清理的会话数量
        mock_db_session.commit.assert_called()
    
    def test_session_backup_and_recovery(self, session_service: SessionService, mock_db_session):
        """测试会话备份和恢复"""
        user_id = "user123"
        
        # 模拟会话数据
        session_data = Mock()
        session_data.id = "session123"
        session_data.title = "备份测试会话"
        session_data.description = "用于测试备份功能的会话"
        
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.all.return_value = [session_data]
        mock_db_session.query.return_value = mock_query
        
        # 备份用户会话
        backup_data = session_service.backup_user_sessions(user_id)
        
        # 验证备份数据
        assert "sessions" in backup_data
        assert "metadata" in backup_data
        assert len(backup_data["sessions"]) == 1
    
    def test_session_analytics_calculation(self, session_service: SessionService, mock_db_session):
        """测试会话分析计算"""
        user_id = "user123"
        start_date = datetime.now() - timedelta(days=30)
        end_date = datetime.now()
        
        # 模拟分析数据查询
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.group_by.return_value = mock_query
        mock_query.all.return_value = [
            (datetime.now().date(), 5, 25, 150),  # 日期, 会话数, 对话数, 消息数
            (datetime.now().date() - timedelta(days=1), 3, 15, 90)
        ]
        mock_db_session.query.return_value = mock_query
        
        # 获取分析数据
        analytics = session_service.get_session_analytics(user_id, start_date, end_date)
        
        # 验证分析结果
        assert "daily_stats" in analytics
        assert "summary" in analytics
        assert len(analytics["daily_stats"]) == 2