"""认证服务单元测试"""

import uuid
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock, patch

import jwt
import pytest
from exceptions.api_exceptions import AuthenticationError, AuthorizationError, ValidationAPIError
from services.auth_service import AuthService

from tests.utils.test_helpers import MockTestCase


@pytest.mark.service
@pytest.mark.unit
class TestAuthService(MockTestCase):
    """认证服务测试类"""
    
    @pytest.fixture
    def mock_user_service(self):
        """模拟用户服务"""
        service = Mock()
        service.get_by_username = AsyncMock()
        service.get_by_email = AsyncMock()
        service.get_by_id = AsyncMock()
        service.update_last_login = AsyncMock()
        service.create = AsyncMock()
        service.update = AsyncMock()
        return service
    
    @pytest.fixture
    def mock_cache_service(self):
        """模拟缓存服务"""
        service = Mock()
        service.get = AsyncMock()
        service.set = AsyncMock()
        service.delete = AsyncMock()
        service.exists = AsyncMock()
        return service
    
    @pytest.fixture
    def auth_service(self, mock_user_service, mock_cache_service):
        """创建认证服务实例"""
        service = AuthService(
            secret_key="test_secret_key_12345",
            algorithm="HS256",
            access_token_expire_minutes=30,
            refresh_token_expire_days=7
        )
        service.user_service = mock_user_service
        service.cache_service = mock_cache_service
        return service
    
    @pytest.fixture
    def sample_user_data(self):
        """示例用户数据"""
        return {
            "id": str(uuid.uuid4()),
            "username": "testuser",
            "email": "<EMAIL>",
            "password_hash": "$2b$12$hashed_password",
            "is_active": True,
            "is_verified": True,
            "created_at": datetime.utcnow(),
            "last_login": None,
            "roles": ["user"],
            "permissions": ["read", "write"]
        }
    
    @pytest.fixture
    def sample_login_data(self):
        """示例登录数据"""
        return {
            "username": "testuser",
            "password": "test_password_123"
        }
    
    def test_hash_password_success(self, auth_service: AuthService):
        """测试成功哈希密码"""
        # 哈希密码
        password = "test_password_123"
        hashed = auth_service.hash_password(password)
        
        # 验证哈希结果
        assert hashed != password
        assert hashed.startswith("$2b$")
        assert len(hashed) > 50
    
    def test_verify_password_success(self, auth_service: AuthService):
        """测试成功验证密码"""
        # 创建密码和哈希
        password = "test_password_123"
        hashed = auth_service.hash_password(password)
        
        # 验证密码
        is_valid = auth_service.verify_password(password, hashed)
        
        # 验证结果
        assert is_valid is True
    
    def test_verify_password_failure(self, auth_service: AuthService):
        """测试密码验证失败"""
        # 创建密码和哈希
        password = "test_password_123"
        wrong_password = "wrong_password"
        hashed = auth_service.hash_password(password)
        
        # 验证错误密码
        is_valid = auth_service.verify_password(wrong_password, hashed)
        
        # 验证结果
        assert is_valid is False
    
    def test_create_access_token_success(self, auth_service: AuthService, sample_user_data):
        """测试成功创建访问令牌"""
        # 创建访问令牌
        token = auth_service.create_access_token(sample_user_data)
        
        # 验证令牌
        assert isinstance(token, str)
        assert len(token) > 100
        
        # 解码令牌验证内容
        decoded = jwt.decode(token, auth_service.secret_key, algorithms=[auth_service.algorithm])
        assert decoded["sub"] == sample_user_data["id"]
        assert decoded["username"] == sample_user_data["username"]
        assert "exp" in decoded
    
    def test_create_refresh_token_success(self, auth_service: AuthService, sample_user_data):
        """测试成功创建刷新令牌"""
        # 创建刷新令牌
        token = auth_service.create_refresh_token(sample_user_data)
        
        # 验证令牌
        assert isinstance(token, str)
        assert len(token) > 100
        
        # 解码令牌验证内容
        decoded = jwt.decode(token, auth_service.secret_key, algorithms=[auth_service.algorithm])
        assert decoded["sub"] == sample_user_data["id"]
        assert decoded["type"] == "refresh"
        assert "exp" in decoded
    
    def test_verify_token_success(self, auth_service: AuthService, sample_user_data):
        """测试成功验证令牌"""
        # 创建令牌
        token = auth_service.create_access_token(sample_user_data)
        
        # 验证令牌
        payload = auth_service.verify_token(token)
        
        # 验证结果
        assert payload["sub"] == sample_user_data["id"]
        assert payload["username"] == sample_user_data["username"]
    
    def test_verify_token_expired(self, auth_service: AuthService, sample_user_data):
        """测试验证过期令牌"""
        # 创建过期令牌
        with patch('datetime.datetime') as mock_datetime:
            # 设置过去的时间
            past_time = datetime.utcnow() - timedelta(hours=2)
            mock_datetime.utcnow.return_value = past_time
            
            token = auth_service.create_access_token(sample_user_data)
        
        # 验证过期令牌
        with pytest.raises(AuthenticationError) as exc_info:
            auth_service.verify_token(token)
        
        assert "expired" in str(exc_info.value).lower()
    
    def test_verify_token_invalid(self, auth_service: AuthService):
        """测试验证无效令牌"""
        # 验证无效令牌
        invalid_token = "invalid.token.here"
        
        with pytest.raises(AuthenticationError) as exc_info:
            auth_service.verify_token(invalid_token)
        
        assert "invalid" in str(exc_info.value).lower()
    
    async def test_authenticate_user_success(self, auth_service: AuthService, mock_user_service, sample_user_data, sample_login_data):
        """测试成功认证用户"""
        # 设置模拟返回
        hashed_password = auth_service.hash_password(sample_login_data["password"])
        sample_user_data["password_hash"] = hashed_password
        mock_user_service.get_by_username.return_value = sample_user_data
        
        # 认证用户
        user = await auth_service.authenticate_user(
            sample_login_data["username"],
            sample_login_data["password"]
        )
        
        # 验证结果
        assert user == sample_user_data
        mock_user_service.get_by_username.assert_called_once_with(sample_login_data["username"])
    
    async def test_authenticate_user_not_found(self, auth_service: AuthService, mock_user_service, sample_login_data):
        """测试认证不存在的用户"""
        # 设置模拟返回
        mock_user_service.get_by_username.return_value = None
        
        # 尝试认证不存在的用户
        with pytest.raises(AuthenticationError) as exc_info:
            await auth_service.authenticate_user(
                sample_login_data["username"],
                sample_login_data["password"]
            )
        
        assert "invalid credentials" in str(exc_info.value).lower()
    
    async def test_authenticate_user_wrong_password(self, auth_service: AuthService, mock_user_service, sample_user_data, sample_login_data):
        """测试认证错误密码"""
        # 设置模拟返回（错误的密码哈希）
        wrong_hash = auth_service.hash_password("wrong_password")
        sample_user_data["password_hash"] = wrong_hash
        mock_user_service.get_by_username.return_value = sample_user_data
        
        # 尝试认证错误密码
        with pytest.raises(AuthenticationError) as exc_info:
            await auth_service.authenticate_user(
                sample_login_data["username"],
                sample_login_data["password"]
            )
        
        assert "invalid credentials" in str(exc_info.value).lower()
    
    async def test_authenticate_user_inactive(self, auth_service: AuthService, mock_user_service, sample_user_data, sample_login_data):
        """测试认证非活跃用户"""
        # 设置模拟返回（非活跃用户）
        hashed_password = auth_service.hash_password(sample_login_data["password"])
        sample_user_data["password_hash"] = hashed_password
        sample_user_data["is_active"] = False
        mock_user_service.get_by_username.return_value = sample_user_data
        
        # 尝试认证非活跃用户
        with pytest.raises(AuthenticationError) as exc_info:
            await auth_service.authenticate_user(
                sample_login_data["username"],
                sample_login_data["password"]
            )
        
        assert "account is disabled" in str(exc_info.value).lower()
    
    async def test_login_success(self, auth_service: AuthService, mock_user_service, mock_cache_service, sample_user_data, sample_login_data):
        """测试成功登录"""
        # 设置模拟返回
        hashed_password = auth_service.hash_password(sample_login_data["password"])
        sample_user_data["password_hash"] = hashed_password
        mock_user_service.get_by_username.return_value = sample_user_data
        mock_user_service.update_last_login.return_value = None
        
        # 登录
        result = await auth_service.login(
            sample_login_data["username"],
            sample_login_data["password"]
        )
        
        # 验证结果
        assert "access_token" in result
        assert "refresh_token" in result
        assert "token_type" in result
        assert "expires_in" in result
        assert result["token_type"] == "bearer"
        
        # 验证更新最后登录时间
        mock_user_service.update_last_login.assert_called_once_with(sample_user_data["id"])
    
    async def test_refresh_token_success(self, auth_service: AuthService, mock_user_service, sample_user_data):
        """测试成功刷新令牌"""
        # 创建刷新令牌
        refresh_token = auth_service.create_refresh_token(sample_user_data)
        
        # 设置模拟返回
        mock_user_service.get_by_id.return_value = sample_user_data
        
        # 刷新令牌
        result = await auth_service.refresh_token(refresh_token)
        
        # 验证结果
        assert "access_token" in result
        assert "refresh_token" in result
        assert "token_type" in result
        assert "expires_in" in result
    
    async def test_refresh_token_invalid_type(self, auth_service: AuthService, sample_user_data):
        """测试使用访问令牌刷新"""
        # 创建访问令牌（而不是刷新令牌）
        access_token = auth_service.create_access_token(sample_user_data)
        
        # 尝试使用访问令牌刷新
        with pytest.raises(AuthenticationError) as exc_info:
            await auth_service.refresh_token(access_token)
        
        assert "invalid refresh token" in str(exc_info.value).lower()
    
    async def test_logout_success(self, auth_service: AuthService, mock_cache_service, sample_user_data):
        """测试成功登出"""
        # 创建令牌
        access_token = auth_service.create_access_token(sample_user_data)
        refresh_token = auth_service.create_refresh_token(sample_user_data)
        
        # 登出
        await auth_service.logout(access_token, refresh_token)
        
        # 验证令牌被加入黑名单
        assert mock_cache_service.set.call_count >= 2
    
    async def test_is_token_blacklisted(self, auth_service: AuthService, mock_cache_service, sample_user_data):
        """测试检查令牌是否在黑名单"""
        # 创建令牌
        token = auth_service.create_access_token(sample_user_data)
        
        # 设置模拟返回
        mock_cache_service.exists.return_value = True
        
        # 检查黑名单
        is_blacklisted = await auth_service.is_token_blacklisted(token)
        
        # 验证结果
        assert is_blacklisted is True
    
    async def test_get_current_user_success(self, auth_service: AuthService, mock_user_service, sample_user_data):
        """测试成功获取当前用户"""
        # 创建令牌
        token = auth_service.create_access_token(sample_user_data)
        
        # 设置模拟返回
        mock_user_service.get_by_id.return_value = sample_user_data
        
        # 获取当前用户
        user = await auth_service.get_current_user(token)
        
        # 验证结果
        assert user == sample_user_data
        mock_user_service.get_by_id.assert_called_once_with(sample_user_data["id"])
    
    async def test_get_current_user_not_found(self, auth_service: AuthService, mock_user_service, sample_user_data):
        """测试获取不存在的当前用户"""
        # 创建令牌
        token = auth_service.create_access_token(sample_user_data)
        
        # 设置模拟返回
        mock_user_service.get_by_id.return_value = None
        
        # 尝试获取不存在的用户
        with pytest.raises(AuthenticationError) as exc_info:
            await auth_service.get_current_user(token)
        
        assert "user not found" in str(exc_info.value).lower()
    
    def test_check_permission_success(self, auth_service: AuthService, sample_user_data):
        """测试成功检查权限"""
        # 检查权限
        has_permission = auth_service.check_permission(sample_user_data, "read")
        
        # 验证结果
        assert has_permission is True
    
    def test_check_permission_failure(self, auth_service: AuthService, sample_user_data):
        """测试权限检查失败"""
        # 检查不存在的权限
        has_permission = auth_service.check_permission(sample_user_data, "admin")
        
        # 验证结果
        assert has_permission is False
    
    def test_check_role_success(self, auth_service: AuthService, sample_user_data):
        """测试成功检查角色"""
        # 检查角色
        has_role = auth_service.check_role(sample_user_data, "user")
        
        # 验证结果
        assert has_role is True
    
    def test_check_role_failure(self, auth_service: AuthService, sample_user_data):
        """测试角色检查失败"""
        # 检查不存在的角色
        has_role = auth_service.check_role(sample_user_data, "admin")
        
        # 验证结果
        assert has_role is False
    
    def test_require_permission_success(self, auth_service: AuthService, sample_user_data):
        """测试成功要求权限"""
        # 要求权限（应该成功）
        auth_service.require_permission(sample_user_data, "read")
        
        # 如果没有抛出异常，则测试通过
    
    def test_require_permission_failure(self, auth_service: AuthService, sample_user_data):
        """测试要求权限失败"""
        # 要求不存在的权限
        with pytest.raises(AuthorizationError) as exc_info:
            auth_service.require_permission(sample_user_data, "admin")
        
        assert "insufficient permissions" in str(exc_info.value).lower()
    
    def test_require_role_success(self, auth_service: AuthService, sample_user_data):
        """测试成功要求角色"""
        # 要求角色（应该成功）
        auth_service.require_role(sample_user_data, "user")
        
        # 如果没有抛出异常，则测试通过
    
    def test_require_role_failure(self, auth_service: AuthService, sample_user_data):
        """测试要求角色失败"""
        # 要求不存在的角色
        with pytest.raises(AuthorizationError) as exc_info:
            auth_service.require_role(sample_user_data, "admin")
        
        assert "insufficient role" in str(exc_info.value).lower()
    
    def test_generate_password_reset_token(self, auth_service: AuthService, sample_user_data):
        """测试生成密码重置令牌"""
        # 生成密码重置令牌
        token = auth_service.generate_password_reset_token(sample_user_data["email"])
        
        # 验证令牌
        assert isinstance(token, str)
        assert len(token) > 50
        
        # 解码令牌验证内容
        decoded = jwt.decode(token, auth_service.secret_key, algorithms=[auth_service.algorithm])
        assert decoded["email"] == sample_user_data["email"]
        assert decoded["type"] == "password_reset"
    
    def test_verify_password_reset_token_success(self, auth_service: AuthService, sample_user_data):
        """测试成功验证密码重置令牌"""
        # 生成令牌
        token = auth_service.generate_password_reset_token(sample_user_data["email"])
        
        # 验证令牌
        email = auth_service.verify_password_reset_token(token)
        
        # 验证结果
        assert email == sample_user_data["email"]
    
    def test_verify_password_reset_token_invalid_type(self, auth_service: AuthService, sample_user_data):
        """测试验证错误类型的密码重置令牌"""
        # 生成访问令牌（而不是密码重置令牌）
        token = auth_service.create_access_token(sample_user_data)
        
        # 尝试验证错误类型的令牌
        with pytest.raises(AuthenticationError) as exc_info:
            auth_service.verify_password_reset_token(token)
        
        assert "invalid reset token" in str(exc_info.value).lower()
    
    def test_generate_email_verification_token(self, auth_service: AuthService, sample_user_data):
        """测试生成邮箱验证令牌"""
        # 生成邮箱验证令牌
        token = auth_service.generate_email_verification_token(sample_user_data["email"])
        
        # 验证令牌
        assert isinstance(token, str)
        assert len(token) > 50
        
        # 解码令牌验证内容
        decoded = jwt.decode(token, auth_service.secret_key, algorithms=[auth_service.algorithm])
        assert decoded["email"] == sample_user_data["email"]
        assert decoded["type"] == "email_verification"
    
    def test_verify_email_verification_token_success(self, auth_service: AuthService, sample_user_data):
        """测试成功验证邮箱验证令牌"""
        # 生成令牌
        token = auth_service.generate_email_verification_token(sample_user_data["email"])
        
        # 验证令牌
        email = auth_service.verify_email_verification_token(token)
        
        # 验证结果
        assert email == sample_user_data["email"]
    
    async def test_change_password_success(self, auth_service: AuthService, mock_user_service, sample_user_data):
        """测试成功更改密码"""
        # 设置当前密码哈希
        current_password = "current_password"
        new_password = "new_password_123"
        sample_user_data["password_hash"] = auth_service.hash_password(current_password)
        
        # 设置模拟返回
        mock_user_service.get_by_id.return_value = sample_user_data
        mock_user_service.update.return_value = sample_user_data
        
        # 更改密码
        result = await auth_service.change_password(
            sample_user_data["id"],
            current_password,
            new_password
        )
        
        # 验证结果
        assert result is True
        mock_user_service.update.assert_called_once()
    
    async def test_change_password_wrong_current(self, auth_service: AuthService, mock_user_service, sample_user_data):
        """测试更改密码时当前密码错误"""
        # 设置当前密码哈希
        current_password = "current_password"
        wrong_current = "wrong_current"
        new_password = "new_password_123"
        sample_user_data["password_hash"] = auth_service.hash_password(current_password)
        
        # 设置模拟返回
        mock_user_service.get_by_id.return_value = sample_user_data
        
        # 尝试用错误的当前密码更改密码
        with pytest.raises(AuthenticationError) as exc_info:
            await auth_service.change_password(
                sample_user_data["id"],
                wrong_current,
                new_password
            )
        
        assert "current password is incorrect" in str(exc_info.value).lower()
    
    async def test_reset_password_success(self, auth_service: AuthService, mock_user_service, sample_user_data):
        """测试成功重置密码"""
        # 生成重置令牌
        reset_token = auth_service.generate_password_reset_token(sample_user_data["email"])
        new_password = "new_password_123"
        
        # 设置模拟返回
        mock_user_service.get_by_email.return_value = sample_user_data
        mock_user_service.update.return_value = sample_user_data
        
        # 重置密码
        result = await auth_service.reset_password(reset_token, new_password)
        
        # 验证结果
        assert result is True
        mock_user_service.update.assert_called_once()
    
    async def test_verify_email_success(self, auth_service: AuthService, mock_user_service, sample_user_data):
        """测试成功验证邮箱"""
        # 生成验证令牌
        verification_token = auth_service.generate_email_verification_token(sample_user_data["email"])
        
        # 设置模拟返回
        sample_user_data["is_verified"] = False
        mock_user_service.get_by_email.return_value = sample_user_data
        mock_user_service.update.return_value = sample_user_data
        
        # 验证邮箱
        result = await auth_service.verify_email(verification_token)
        
        # 验证结果
        assert result is True
        mock_user_service.update.assert_called_once()
    
    def test_generate_api_key(self, auth_service: AuthService, sample_user_data):
        """测试生成API密钥"""
        # 生成API密钥
        api_key = auth_service.generate_api_key(sample_user_data["id"])
        
        # 验证API密钥
        assert isinstance(api_key, str)
        assert len(api_key) >= 32
        assert api_key.startswith("ak_")  # API密钥前缀
    
    async def test_validate_api_key_success(self, auth_service: AuthService, mock_cache_service, sample_user_data):
        """测试成功验证API密钥"""
        # 生成API密钥
        api_key = auth_service.generate_api_key(sample_user_data["id"])
        
        # 设置模拟返回
        mock_cache_service.get.return_value = sample_user_data["id"]
        
        # 验证API密钥
        user_id = await auth_service.validate_api_key(api_key)
        
        # 验证结果
        assert user_id == sample_user_data["id"]
    
    async def test_validate_api_key_invalid(self, auth_service: AuthService, mock_cache_service):
        """测试验证无效API密钥"""
        # 设置模拟返回
        mock_cache_service.get.return_value = None
        
        # 验证无效API密钥
        with pytest.raises(AuthenticationError) as exc_info:
            await auth_service.validate_api_key("invalid_api_key")
        
        assert "invalid api key" in str(exc_info.value).lower()
    
    async def test_revoke_api_key_success(self, auth_service: AuthService, mock_cache_service, sample_user_data):
        """测试成功撤销API密钥"""
        # 生成API密钥
        api_key = auth_service.generate_api_key(sample_user_data["id"])
        
        # 撤销API密钥
        result = await auth_service.revoke_api_key(api_key)
        
        # 验证结果
        assert result is True
        mock_cache_service.delete.assert_called_once()
    
    def test_generate_session_id(self, auth_service: AuthService):
        """测试生成会话ID"""
        # 生成会话ID
        session_id = auth_service.generate_session_id()
        
        # 验证会话ID
        assert isinstance(session_id, str)
        assert len(session_id) >= 32
    
    async def test_create_session_success(self, auth_service: AuthService, mock_cache_service, sample_user_data):
        """测试成功创建会话"""
        # 创建会话
        session_id = await auth_service.create_session(sample_user_data)
        
        # 验证结果
        assert isinstance(session_id, str)
        mock_cache_service.set.assert_called_once()
    
    async def test_get_session_success(self, auth_service: AuthService, mock_cache_service, sample_user_data):
        """测试成功获取会话"""
        # 设置模拟返回
        session_data = {
            "user_id": sample_user_data["id"],
            "created_at": datetime.utcnow().isoformat(),
            "last_accessed": datetime.utcnow().isoformat()
        }
        mock_cache_service.get.return_value = session_data
        
        # 获取会话
        session_id = "test_session_id"
        result = await auth_service.get_session(session_id)
        
        # 验证结果
        assert result == session_data
    
    async def test_update_session_success(self, auth_service: AuthService, mock_cache_service):
        """测试成功更新会话"""
        # 更新会话
        session_id = "test_session_id"
        session_data = {"last_accessed": datetime.utcnow().isoformat()}
        
        result = await auth_service.update_session(session_id, session_data)
        
        # 验证结果
        assert result is True
        mock_cache_service.set.assert_called_once()
    
    async def test_delete_session_success(self, auth_service: AuthService, mock_cache_service):
        """测试成功删除会话"""
        # 删除会话
        session_id = "test_session_id"
        result = await auth_service.delete_session(session_id)
        
        # 验证结果
        assert result is True
        mock_cache_service.delete.assert_called_once_with(f"session:{session_id}")
    
    def test_validate_password_strength_success(self, auth_service: AuthService):
        """测试成功验证密码强度"""
        # 验证强密码
        strong_passwords = [
            "StrongPassword123!",
            "MySecure@Pass2023",
            "Complex#Password99"
        ]
        
        for password in strong_passwords:
            is_valid = auth_service.validate_password_strength(password)
            assert is_valid is True
    
    def test_validate_password_strength_failure(self, auth_service: AuthService):
        """测试密码强度验证失败"""
        # 验证弱密码
        weak_passwords = [
            "123456",
            "password",
            "abc",
            "12345678",
            "PASSWORD"
        ]
        
        for password in weak_passwords:
            is_valid = auth_service.validate_password_strength(password)
            assert is_valid is False
    
    async def test_get_user_sessions(self, auth_service: AuthService, mock_cache_service, sample_user_data):
        """测试获取用户会话"""
        # 设置模拟返回
        sessions = [
            {"session_id": "session1", "created_at": datetime.utcnow().isoformat()},
            {"session_id": "session2", "created_at": datetime.utcnow().isoformat()}
        ]
        mock_cache_service.get.return_value = sessions
        
        # 获取用户会话
        result = await auth_service.get_user_sessions(sample_user_data["id"])
        
        # 验证结果
        assert result == sessions
    
    async def test_revoke_all_sessions(self, auth_service: AuthService, mock_cache_service, sample_user_data):
        """测试撤销所有会话"""
        # 撤销所有会话
        result = await auth_service.revoke_all_sessions(sample_user_data["id"])
        
        # 验证结果
        assert result is True
        # 验证缓存删除调用
        mock_cache_service.delete.assert_called()


@pytest.mark.service
@pytest.mark.unit
class TestAuthServiceEdgeCases(MockTestCase):
    """认证服务边界情况测试类"""
    
    @pytest.fixture
    def auth_service(self):
        """创建认证服务实例"""
        return AuthService(
            secret_key="test_secret_key_12345",
            algorithm="HS256"
        )
    
    def test_hash_password_with_empty_string(self, auth_service: AuthService):
        """测试哈希空字符串密码"""
        # 尝试哈希空密码
        with pytest.raises(ValidationAPIError) as exc_info:
            auth_service.hash_password("")
        
        assert "password cannot be empty" in str(exc_info.value).lower()
    
    def test_hash_password_with_none(self, auth_service: AuthService):
        """测试哈希None密码"""
        # 尝试哈希None密码
        with pytest.raises(ValidationAPIError) as exc_info:
            auth_service.hash_password(None)
        
        assert "password cannot be none" in str(exc_info.value).lower()
    
    def test_create_token_with_invalid_user_data(self, auth_service: AuthService):
        """测试使用无效用户数据创建令牌"""
        # 尝试使用无效用户数据创建令牌
        invalid_user_data = {"username": "test"}  # 缺少必需的字段
        
        with pytest.raises(ValidationAPIError) as exc_info:
            auth_service.create_access_token(invalid_user_data)
        
        assert "invalid user data" in str(exc_info.value).lower()
    
    def test_verify_token_with_wrong_algorithm(self, auth_service: AuthService):
        """测试使用错误算法验证令牌"""
        # 使用不同算法创建令牌
        user_data = {"id": "user123", "username": "test"}
        wrong_algorithm_token = jwt.encode(
            {"sub": user_data["id"], "username": user_data["username"]},
            "wrong_secret",
            algorithm="HS512"
        )
        
        # 尝试验证令牌
        with pytest.raises(AuthenticationError) as exc_info:
            auth_service.verify_token(wrong_algorithm_token)
        
        assert "invalid token" in str(exc_info.value).lower()
    
    def test_token_tampering_detection(self, auth_service: AuthService):
        """测试令牌篡改检测"""
        # 创建有效令牌
        user_data = {"id": "user123", "username": "test"}
        token = auth_service.create_access_token(user_data)
        
        # 篡改令牌
        tampered_token = token[:-5] + "XXXXX"
        
        # 尝试验证篡改的令牌
        with pytest.raises(AuthenticationError) as exc_info:
            auth_service.verify_token(tampered_token)
        
        assert "invalid token" in str(exc_info.value).lower()
    
    async def test_brute_force_protection(self, auth_service: AuthService, mock_cache_service):
        """测试暴力破解保护"""
        # 启用暴力破解保护
        auth_service.enable_brute_force_protection(max_attempts=3, lockout_duration=300)
        
        # 模拟多次失败登录
        username = "testuser"
        
        # 设置缓存返回失败次数
        mock_cache_service.get.return_value = 3  # 已达到最大尝试次数
        
        # 尝试登录
        with pytest.raises(AuthenticationError) as exc_info:
            await auth_service.check_brute_force_protection(username)
        
        assert "account locked" in str(exc_info.value).lower()
    
    async def test_concurrent_login_attempts(self, auth_service: AuthService):
        """测试并发登录尝试"""
        import asyncio
        
        results = []
        errors = []
        
        async def login_attempt(username, password):
            try:
                # 模拟登录尝试
                result = await auth_service.authenticate_user(username, password)
                results.append(result)
            except Exception as e:
                errors.append(e)
        
        # 创建多个并发登录任务
        tasks = []
        for i in range(10):
            task = login_attempt(f"user{i}", "password")
            tasks.append(task)
        
        # 等待所有任务完成
        await asyncio.gather(*tasks, return_exceptions=True)
        
        # 验证并发处理
        assert len(errors) >= 0  # 可能有错误（用户不存在等）
    
    def test_token_payload_size_limit(self, auth_service: AuthService):
        """测试令牌载荷大小限制"""
        # 创建超大用户数据
        large_user_data = {
            "id": "user123",
            "username": "test",
            "large_data": "x" * 10000  # 10KB数据
        }
        
        # 尝试创建令牌
        token = auth_service.create_access_token(large_user_data)
        
        # 验证令牌大小合理
        assert len(token) < 20000  # 令牌大小应该合理
    
    def test_password_hash_collision_resistance(self, auth_service: AuthService):
        """测试密码哈希碰撞抵抗"""
        # 测试相同密码产生不同哈希
        password = "test_password_123"
        hash1 = auth_service.hash_password(password)
        hash2 = auth_service.hash_password(password)
        
        # 验证哈希不同（由于盐值）
        assert hash1 != hash2
        
        # 但都能验证原密码
        assert auth_service.verify_password(password, hash1) is True
        assert auth_service.verify_password(password, hash2) is True
    
    def test_timing_attack_resistance(self, auth_service: AuthService, performance_timer):
        """测试时序攻击抵抗"""
        # 测试验证存在和不存在用户的时间差异
        existing_hash = auth_service.hash_password("password")
        
        times = []
        
        # 测试验证正确密码的时间
        with performance_timer:
            auth_service.verify_password("password", existing_hash)
        times.append(performance_timer.elapsed)
        
        # 测试验证错误密码的时间
        with performance_timer:
            auth_service.verify_password("wrong_password", existing_hash)
        times.append(performance_timer.elapsed)
        
        # 时间差异应该很小（防止时序攻击）
        time_diff = abs(times[0] - times[1])
        assert time_diff < 0.1  # 时间差异小于100ms
    
    async def test_session_hijacking_protection(self, auth_service: AuthService, mock_cache_service):
        """测试会话劫持保护"""
        # 创建会话
        user_data = {"id": "user123", "username": "test"}
        session_id = await auth_service.create_session(user_data)
        
        # 模拟会话数据
        session_data = {
            "user_id": user_data["id"],
            "ip_address": "*************",
            "user_agent": "Mozilla/5.0...",
            "created_at": datetime.utcnow().isoformat()
        }
        mock_cache_service.get.return_value = session_data
        
        # 验证会话（相同IP和User-Agent）
        is_valid = await auth_service.validate_session(
            session_id,
            ip_address="*************",
            user_agent="Mozilla/5.0..."
        )
        assert is_valid is True
        
        # 验证会话（不同IP）
        is_valid = await auth_service.validate_session(
            session_id,
            ip_address="*************",  # 不同IP
            user_agent="Mozilla/5.0..."
        )
        assert is_valid is False  # 应该被拒绝
    
    def test_jwt_algorithm_confusion_attack(self, auth_service: AuthService):
        """测试JWT算法混淆攻击"""
        # 尝试使用'none'算法创建令牌
        user_data = {"id": "user123", "username": "test"}
        
        # 创建使用'none'算法的恶意令牌
        malicious_token = jwt.encode(
            {"sub": user_data["id"], "username": user_data["username"]},
            "",  # 空密钥
            algorithm="none"
        )
        
        # 尝试验证恶意令牌
        with pytest.raises(AuthenticationError) as exc_info:
            auth_service.verify_token(malicious_token)
        
        assert "invalid token" in str(exc_info.value).lower()
    
    async def test_token_replay_attack_protection(self, auth_service: AuthService, mock_cache_service):
        """测试令牌重放攻击保护"""
        # 创建令牌
        user_data = {"id": "user123", "username": "test"}
        token = auth_service.create_access_token(user_data)
        
        # 第一次使用令牌
        payload = auth_service.verify_token(token)
        assert payload is not None
        
        # 将令牌加入黑名单（模拟登出）
        await auth_service.blacklist_token(token)
        mock_cache_service.exists.return_value = True
        
        # 尝试重复使用令牌
        with pytest.raises(AuthenticationError) as exc_info:
            await auth_service.verify_token_not_blacklisted(token)
        
        assert "token is blacklisted" in str(exc_info.value).lower()
    
    def test_password_policy_enforcement(self, auth_service: AuthService):
        """测试密码策略执行"""
        # 设置密码策略
        policy = {
            "min_length": 8,
            "require_uppercase": True,
            "require_lowercase": True,
            "require_digits": True,
            "require_special_chars": True,
            "forbidden_patterns": ["password", "123456"]
        }
        auth_service.set_password_policy(policy)
        
        # 测试符合策略的密码
        valid_password = "SecurePass123!"
        assert auth_service.validate_password_policy(valid_password) is True
        
        # 测试不符合策略的密码
        invalid_passwords = [
            "short",  # 太短
            "nouppercase123!",  # 没有大写字母
            "NOLOWERCASE123!",  # 没有小写字母
            "NoDigits!",  # 没有数字
            "NoSpecialChars123",  # 没有特殊字符
            "password123!",  # 包含禁用模式
        ]
        
        for password in invalid_passwords:
            assert auth_service.validate_password_policy(password) is False
    
    async def test_rate_limiting(self, auth_service: AuthService, mock_cache_service):
        """测试速率限制"""
        # 启用速率限制
        auth_service.enable_rate_limiting(max_requests=5, window_seconds=60)
        
        # 模拟已达到速率限制
        mock_cache_service.get.return_value = 5  # 已达到最大请求数
        
        # 尝试超过速率限制
        with pytest.raises(AuthenticationError) as exc_info:
            await auth_service.check_rate_limit("*************")
        
        assert "rate limit exceeded" in str(exc_info.value).lower()
    
    def test_secure_random_generation(self, auth_service: AuthService):
        """测试安全随机数生成"""
        # 生成多个随机值
        random_values = []
        for _ in range(100):
            value = auth_service.generate_secure_random(32)
            random_values.append(value)
        
        # 验证随机性（没有重复）
        assert len(set(random_values)) == 100
        
        # 验证长度
        for value in random_values:
            assert len(value) == 64  # 32字节 = 64十六进制字符
    
    async def test_audit_logging(self, auth_service: AuthService):
        """测试审计日志"""
        audit_logs = []
        
        def audit_handler(event_type, user_id, details):
            audit_logs.append({
                "event_type": event_type,
                "user_id": user_id,
                "details": details,
                "timestamp": datetime.utcnow().isoformat()
            })
        
        # 设置审计处理器
        auth_service.set_audit_handler(audit_handler)
        
        # 执行需要审计的操作
        user_data = {"id": "user123", "username": "test"}
        await auth_service.audit_login_success(user_data["id"], "*************")
        await auth_service.audit_login_failure("testuser", "*************")
        
        # 验证审计日志
        assert len(audit_logs) == 2
        assert audit_logs[0]["event_type"] == "login_success"
        assert audit_logs[1]["event_type"] == "login_failure"