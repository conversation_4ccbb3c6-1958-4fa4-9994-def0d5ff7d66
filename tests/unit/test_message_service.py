"""消息服务单元测试"""

import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime, timedelta
import uuid
import json
from typing import List, Dict, Any

from services.message_service import MessageService
from exceptions.api_exceptions import Validation<PERSON>IError, NotFoundAPIError, DatabaseAPIError
from tests.utils.test_helpers import AsyncTestCase, MockTestCase


@pytest.mark.service
@pytest.mark.unit
class TestMessageService(MockTestCase):
    """消息服务测试类"""
    
    @pytest.fixture
    def mock_db_session(self):
        """模拟数据库会话"""
        session = Mock()
        session.add = Mock()
        session.commit = AsyncMock()
        session.rollback = AsyncMock()
        session.refresh = AsyncMock()
        session.execute = AsyncMock()
        session.scalar = AsyncMock()
        session.scalars = AsyncMock()
        session.close = AsyncMock()
        return session
    
    @pytest.fixture
    def mock_conversation_service(self):
        """模拟对话服务"""
        service = Mock()
        service.get_by_id = AsyncMock()
        service.update_last_message = AsyncMock()
        service.increment_message_count = AsyncMock()
        return service
    
    @pytest.fixture
    def mock_cache_service(self):
        """模拟缓存服务"""
        service = Mock()
        service.get = AsyncMock()
        service.set = AsyncMock()
        service.delete = AsyncMock()
        service.get_list = AsyncMock()
        service.add_to_list = AsyncMock()
        return service
    
    @pytest.fixture
    def message_service(self, mock_db_session, mock_conversation_service, mock_cache_service):
        """创建消息服务实例"""
        service = MessageService()
        service.db_session = mock_db_session
        service.conversation_service = mock_conversation_service
        service.cache_service = mock_cache_service
        return service
    
    @pytest.fixture
    def sample_message_create(self):
        """示例消息创建数据"""
        return {
            "conversation_id": str(uuid.uuid4()),
            "role": "user",
            "content": "Hello, how are you?",
            "message_type": "text",
            "metadata": {"source": "web"},
            "parent_message_id": None
        }
    
    @pytest.fixture
    def sample_message_update(self):
        """示例消息更新数据"""
        return {
            "content": "Updated message content",
            "metadata": {"edited": True, "edit_time": datetime.utcnow().isoformat()}
        }
    
    @pytest.fixture
    def sample_message_model(self):
        """示例消息模型"""
        return {
            "id": str(uuid.uuid4()),
            "conversation_id": str(uuid.uuid4()),
            "role": "user",
            "content": "Hello, how are you?",
            "message_type": "text",
            "metadata": {"source": "web"},
            "parent_message_id": None,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow(),
            "token_count": 5,
            "is_deleted": False
        }
    
    @pytest.fixture
    def sample_conversation_data(self):
        """示例对话数据"""
        return {
            "id": str(uuid.uuid4()),
            "session_id": str(uuid.uuid4()),
            "title": "Test Conversation",
            "is_active": True,
            "created_at": datetime.utcnow()
        }
    
    async def test_create_message_success(self, message_service: MessageService, mock_db_session, mock_conversation_service, sample_message_create, sample_conversation_data):
        """测试成功创建消息"""
        # 设置模拟返回
        mock_conversation_service.get_by_id.return_value = sample_conversation_data
        
        # 模拟数据库操作
        mock_message = Mock()
        mock_message.id = str(uuid.uuid4())
        mock_message.conversation_id = sample_message_create["conversation_id"]
        mock_message.role = sample_message_create["role"]
        mock_message.content = sample_message_create["content"]
        mock_message.created_at = datetime.utcnow()
        
        mock_db_session.refresh.return_value = None
        
        with patch.object(message_service, '_create_message_model', return_value=mock_message):
            # 创建消息
            result = await message_service.create(sample_message_create)
        
        # 验证结果
        assert result.id == mock_message.id
        assert result.conversation_id == sample_message_create["conversation_id"]
        mock_db_session.add.assert_called_once()
        mock_db_session.commit.assert_called_once()
        mock_conversation_service.update_last_message.assert_called_once()
    
    async def test_create_message_conversation_not_found(self, message_service: MessageService, mock_conversation_service, sample_message_create):
        """测试创建消息时对话不存在"""
        # 设置模拟返回
        mock_conversation_service.get_by_id.return_value = None
        
        # 尝试创建消息
        with pytest.raises(ValidationAPIError) as exc_info:
            await message_service.create(sample_message_create)
        
        assert "conversation not found" in str(exc_info.value).lower()
    
    async def test_create_message_invalid_role(self, message_service: MessageService, mock_conversation_service, sample_message_create, sample_conversation_data):
        """测试创建消息时角色无效"""
        # 设置模拟返回
        mock_conversation_service.get_by_id.return_value = sample_conversation_data
        
        # 设置无效角色
        sample_message_create["role"] = "invalid_role"
        
        # 尝试创建消息
        with pytest.raises(ValidationAPIError) as exc_info:
            await message_service.create(sample_message_create)
        
        assert "invalid role" in str(exc_info.value).lower()
    
    async def test_get_message_by_id_success(self, message_service: MessageService, mock_db_session, sample_message_model):
        """测试成功按ID获取消息"""
        # 设置模拟返回
        mock_db_session.scalar.return_value = sample_message_model
        
        # 获取消息
        result = await message_service.get_by_id(sample_message_model["id"])
        
        # 验证结果
        assert result == sample_message_model
        mock_db_session.scalar.assert_called_once()
    
    async def test_get_message_by_id_not_found(self, message_service: MessageService, mock_db_session):
        """测试按ID获取不存在的消息"""
        # 设置模拟返回
        mock_db_session.scalar.return_value = None
        
        # 尝试获取不存在的消息
        result = await message_service.get_by_id("nonexistent_id")
        
        # 验证结果
        assert result is None
    
    async def test_get_messages_by_conversation_success(self, message_service: MessageService, mock_db_session, sample_message_model):
        """测试成功按对话ID获取消息列表"""
        # 设置模拟返回
        messages = [sample_message_model, sample_message_model.copy()]
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = messages
        mock_db_session.execute.return_value = mock_result
        
        # 获取消息列表
        result = await message_service.get_by_conversation_id(sample_message_model["conversation_id"])
        
        # 验证结果
        assert len(result) == 2
        assert result[0] == sample_message_model
        mock_db_session.execute.assert_called_once()
    
    async def test_get_messages_by_conversation_with_pagination(self, message_service: MessageService, mock_db_session, sample_message_model):
        """测试分页获取对话消息"""
        # 设置模拟返回
        messages = [sample_message_model]
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = messages
        mock_db_session.execute.return_value = mock_result
        
        # 获取分页消息
        result = await message_service.get_by_conversation_id(
            sample_message_model["conversation_id"],
            limit=10,
            offset=0
        )
        
        # 验证结果
        assert len(result) == 1
        mock_db_session.execute.assert_called_once()
    
    async def test_update_message_success(self, message_service: MessageService, mock_db_session, sample_message_model, sample_message_update):
        """测试成功更新消息"""
        # 设置模拟返回
        mock_db_session.scalar.return_value = sample_message_model
        
        # 更新消息
        result = await message_service.update(sample_message_model["id"], sample_message_update)
        
        # 验证结果
        assert result is not None
        mock_db_session.commit.assert_called_once()
    
    async def test_update_message_not_found(self, message_service: MessageService, mock_db_session, sample_message_update):
        """测试更新不存在的消息"""
        # 设置模拟返回
        mock_db_session.scalar.return_value = None
        
        # 尝试更新不存在的消息
        with pytest.raises(NotFoundAPIError) as exc_info:
            await message_service.update("nonexistent_id", sample_message_update)
        
        assert "message not found" in str(exc_info.value).lower()
    
    async def test_delete_message_success(self, message_service: MessageService, mock_db_session, sample_message_model):
        """测试成功删除消息"""
        # 设置模拟返回
        mock_db_session.scalar.return_value = sample_message_model
        
        # 删除消息
        result = await message_service.delete(sample_message_model["id"])
        
        # 验证结果
        assert result is True
        mock_db_session.commit.assert_called_once()
    
    async def test_delete_message_not_found(self, message_service: MessageService, mock_db_session):
        """测试删除不存在的消息"""
        # 设置模拟返回
        mock_db_session.scalar.return_value = None
        
        # 尝试删除不存在的消息
        with pytest.raises(NotFoundAPIError) as exc_info:
            await message_service.delete("nonexistent_id")
        
        assert "message not found" in str(exc_info.value).lower()
    
    async def test_soft_delete_message_success(self, message_service: MessageService, mock_db_session, sample_message_model):
        """测试成功软删除消息"""
        # 设置模拟返回
        mock_db_session.scalar.return_value = sample_message_model
        
        # 软删除消息
        result = await message_service.soft_delete(sample_message_model["id"])
        
        # 验证结果
        assert result is True
        mock_db_session.commit.assert_called_once()
    
    async def test_get_message_thread_success(self, message_service: MessageService, mock_db_session, sample_message_model):
        """测试成功获取消息线程"""
        # 创建消息线程
        parent_message = sample_message_model.copy()
        child_message = sample_message_model.copy()
        child_message["id"] = str(uuid.uuid4())
        child_message["parent_message_id"] = parent_message["id"]
        
        # 设置模拟返回
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = [parent_message, child_message]
        mock_db_session.execute.return_value = mock_result
        
        # 获取消息线程
        result = await message_service.get_message_thread(parent_message["id"])
        
        # 验证结果
        assert len(result) == 2
        assert result[0] == parent_message
        assert result[1] == child_message
    
    async def test_search_messages_success(self, message_service: MessageService, mock_db_session, sample_message_model):
        """测试成功搜索消息"""
        # 设置模拟返回
        messages = [sample_message_model]
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = messages
        mock_db_session.execute.return_value = mock_result
        
        # 搜索消息
        result = await message_service.search_messages(
            conversation_id=sample_message_model["conversation_id"],
            query="hello"
        )
        
        # 验证结果
        assert len(result) == 1
        assert result[0] == sample_message_model
    
    async def test_get_message_statistics_success(self, message_service: MessageService, mock_db_session):
        """测试成功获取消息统计"""
        # 设置模拟返回
        stats_data = [
            ("conversation1", 10, 150, "gpt-3.5-turbo"),
            ("conversation2", 5, 75, "gpt-4")
        ]
        mock_result = Mock()
        mock_result.all.return_value = stats_data
        mock_db_session.execute.return_value = mock_result
        
        # 获取统计信息
        result = await message_service.get_message_statistics()
        
        # 验证结果
        assert len(result) == 2
        assert result[0]["conversation_id"] == "conversation1"
        assert result[0]["message_count"] == 10
        assert result[0]["total_tokens"] == 150
    
    async def test_count_tokens_success(self, message_service: MessageService):
        """测试成功计算令牌数"""
        # 计算令牌数
        content = "Hello, how are you today?"
        token_count = await message_service.count_tokens(content)
        
        # 验证结果
        assert isinstance(token_count, int)
        assert token_count > 0
    
    async def test_validate_message_content_success(self, message_service: MessageService):
        """测试成功验证消息内容"""
        # 验证有效内容
        valid_content = "This is a valid message content."
        is_valid = await message_service.validate_message_content(valid_content)
        
        # 验证结果
        assert is_valid is True
    
    async def test_validate_message_content_empty(self, message_service: MessageService):
        """测试验证空消息内容"""
        # 验证空内容
        with pytest.raises(ValidationAPIError) as exc_info:
            await message_service.validate_message_content("")
        
        assert "content cannot be empty" in str(exc_info.value).lower()
    
    async def test_validate_message_content_too_long(self, message_service: MessageService):
        """测试验证过长消息内容"""
        # 验证过长内容
        long_content = "x" * 100000  # 100KB内容
        
        with pytest.raises(ValidationAPIError) as exc_info:
            await message_service.validate_message_content(long_content)
        
        assert "content too long" in str(exc_info.value).lower()
    
    async def test_format_message_for_ai_success(self, message_service: MessageService, sample_message_model):
        """测试成功格式化消息给AI"""
        # 格式化消息
        formatted = await message_service.format_message_for_ai(sample_message_model)
        
        # 验证结果
        assert "role" in formatted
        assert "content" in formatted
        assert formatted["role"] == sample_message_model["role"]
        assert formatted["content"] == sample_message_model["content"]
    
    async def test_get_conversation_context_success(self, message_service: MessageService, mock_db_session, sample_message_model):
        """测试成功获取对话上下文"""
        # 设置模拟返回
        messages = [sample_message_model, sample_message_model.copy()]
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = messages
        mock_db_session.execute.return_value = mock_result
        
        # 获取对话上下文
        result = await message_service.get_conversation_context(
            sample_message_model["conversation_id"],
            max_messages=10
        )
        
        # 验证结果
        assert len(result) == 2
        assert all("role" in msg and "content" in msg for msg in result)
    
    async def test_export_messages_success(self, message_service: MessageService, mock_db_session, sample_message_model):
        """测试成功导出消息"""
        # 设置模拟返回
        messages = [sample_message_model]
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = messages
        mock_db_session.execute.return_value = mock_result
        
        # 导出消息
        result = await message_service.export_messages(
            conversation_id=sample_message_model["conversation_id"],
            format="json"
        )
        
        # 验证结果
        assert isinstance(result, str)
        exported_data = json.loads(result)
        assert len(exported_data) == 1
        assert exported_data[0]["id"] == sample_message_model["id"]
    
    async def test_import_messages_success(self, message_service: MessageService, mock_db_session, mock_conversation_service, sample_conversation_data):
        """测试成功导入消息"""
        # 设置模拟返回
        mock_conversation_service.get_by_id.return_value = sample_conversation_data
        
        # 准备导入数据
        import_data = [
            {
                "role": "user",
                "content": "Hello",
                "message_type": "text",
                "created_at": datetime.utcnow().isoformat()
            },
            {
                "role": "assistant",
                "content": "Hi there!",
                "message_type": "text",
                "created_at": datetime.utcnow().isoformat()
            }
        ]
        
        with patch.object(message_service, '_create_message_model') as mock_create:
            mock_create.return_value = Mock(id=str(uuid.uuid4()))
            
            # 导入消息
            result = await message_service.import_messages(
                conversation_id=sample_conversation_data["id"],
                messages_data=import_data
            )
        
        # 验证结果
        assert result == 2  # 导入了2条消息
        assert mock_db_session.add.call_count == 2
        mock_db_session.commit.assert_called_once()
    
    async def test_get_message_analytics_success(self, message_service: MessageService, mock_db_session):
        """测试成功获取消息分析"""
        # 设置模拟返回
        analytics_data = {
            "total_messages": 100,
            "messages_by_role": {"user": 60, "assistant": 40},
            "messages_by_type": {"text": 90, "image": 10},
            "average_tokens_per_message": 25.5,
            "total_tokens": 2550
        }
        
        with patch.object(message_service, '_calculate_analytics', return_value=analytics_data):
            # 获取分析数据
            result = await message_service.get_message_analytics(
                conversation_id="test_conversation_id"
            )
        
        # 验证结果
        assert result["total_messages"] == 100
        assert result["messages_by_role"]["user"] == 60
        assert result["average_tokens_per_message"] == 25.5
    
    async def test_batch_update_messages_success(self, message_service: MessageService, mock_db_session, sample_message_model):
        """测试成功批量更新消息"""
        # 设置模拟返回
        messages = [sample_message_model, sample_message_model.copy()]
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = messages
        mock_db_session.execute.return_value = mock_result
        
        # 批量更新数据
        update_data = {
            "metadata": {"batch_updated": True}
        }
        
        # 批量更新消息
        result = await message_service.batch_update_messages(
            message_ids=[sample_message_model["id"]],
            update_data=update_data
        )
        
        # 验证结果
        assert result == 2  # 更新了2条消息
        mock_db_session.commit.assert_called_once()
    
    async def test_get_recent_messages_success(self, message_service: MessageService, mock_cache_service, sample_message_model):
        """测试成功获取最近消息"""
        # 设置模拟返回
        cached_messages = [sample_message_model]
        mock_cache_service.get_list.return_value = cached_messages
        
        # 获取最近消息
        result = await message_service.get_recent_messages(
            conversation_id=sample_message_model["conversation_id"],
            limit=10
        )
        
        # 验证结果
        assert len(result) == 1
        assert result[0] == sample_message_model
    
    async def test_cache_message_success(self, message_service: MessageService, mock_cache_service, sample_message_model):
        """测试成功缓存消息"""
        # 缓存消息
        await message_service.cache_message(sample_message_model)
        
        # 验证缓存调用
        mock_cache_service.set.assert_called_once()
        mock_cache_service.add_to_list.assert_called_once()
    
    async def test_invalidate_message_cache_success(self, message_service: MessageService, mock_cache_service):
        """测试成功清除消息缓存"""
        # 清除缓存
        conversation_id = "test_conversation_id"
        await message_service.invalidate_message_cache(conversation_id)
        
        # 验证缓存删除调用
        mock_cache_service.delete.assert_called()


@pytest.mark.service
@pytest.mark.unit
class TestMessageServiceEdgeCases(MockTestCase):
    """消息服务边界情况测试类"""
    
    @pytest.fixture
    def message_service(self):
        """创建消息服务实例"""
        return MessageService()
    
    @pytest.fixture
    def memory_monitor(self):
        """内存监控器"""
        class MemoryMonitor:
            def __init__(self):
                self.peak_memory = 0
                self.current_memory = 0
            
            def update_memory(self, memory_mb):
                self.current_memory = memory_mb
                if memory_mb > self.peak_memory:
                    self.peak_memory = memory_mb
        
        return MemoryMonitor()
    
    @pytest.fixture
    def performance_timer(self):
        """性能计时器"""
        class PerformanceTimer:
            def __init__(self):
                self.start_time = None
                self.end_time = None
                self.elapsed = 0
            
            def __enter__(self):
                import time
                self.start_time = time.time()
                return self
            
            def __exit__(self, exc_type, exc_val, exc_tb):
                import time
                self.end_time = time.time()
                self.elapsed = self.end_time - self.start_time
        
        return PerformanceTimer()
    
    async def test_create_message_with_special_characters(self, message_service: MessageService):
        """测试创建包含特殊字符的消息"""
        # 包含特殊字符的内容
        special_content = "Hello! 🌟 This contains émojis and spëcial chars: @#$%^&*()_+{}|:<>?[]\\"
        
        message_data = {
            "conversation_id": str(uuid.uuid4()),
            "role": "user",
            "content": special_content,
            "message_type": "text"
        }
        
        with patch.object(message_service, 'conversation_service') as mock_conv_service:
            mock_conv_service.get_by_id.return_value = {"id": message_data["conversation_id"]}
            
            with patch.object(message_service, 'db_session') as mock_session:
                mock_message = Mock()
                mock_message.id = str(uuid.uuid4())
                mock_message.content = special_content
                
                with patch.object(message_service, '_create_message_model', return_value=mock_message):
                    # 创建消息
                    result = await message_service.create(message_data)
                
                # 验证特殊字符被正确处理
                assert result.content == special_content
    
    async def test_create_message_with_empty_metadata(self, message_service: MessageService):
        """测试创建带空元数据的消息"""
        message_data = {
            "conversation_id": str(uuid.uuid4()),
            "role": "user",
            "content": "Test message",
            "message_type": "text",
            "metadata": {}  # 空元数据
        }
        
        with patch.object(message_service, 'conversation_service') as mock_conv_service:
            mock_conv_service.get_by_id.return_value = {"id": message_data["conversation_id"]}
            
            with patch.object(message_service, 'db_session') as mock_session:
                mock_message = Mock()
                mock_message.id = str(uuid.uuid4())
                mock_message.metadata = {}
                
                with patch.object(message_service, '_create_message_model', return_value=mock_message):
                    # 创建消息
                    result = await message_service.create(message_data)
                
                # 验证空元数据被正确处理
                assert result.metadata == {}
    
    async def test_create_message_with_very_long_content(self, message_service: MessageService):
        """测试创建超长内容的消息"""
        # 创建超长内容（接近限制）
        long_content = "x" * 50000  # 50KB内容
        
        message_data = {
            "conversation_id": str(uuid.uuid4()),
            "role": "user",
            "content": long_content,
            "message_type": "text"
        }
        
        # 验证内容长度限制
        with pytest.raises(ValidationAPIError) as exc_info:
            await message_service.validate_message_content(long_content)
        
        assert "content too long" in str(exc_info.value).lower()
    
    async def test_database_connection_error(self, message_service: MessageService):
        """测试数据库连接错误"""
        message_data = {
            "conversation_id": str(uuid.uuid4()),
            "role": "user",
            "content": "Test message",
            "message_type": "text"
        }
        
        with patch.object(message_service, 'conversation_service') as mock_conv_service:
            mock_conv_service.get_by_id.return_value = {"id": message_data["conversation_id"]}
            
            with patch.object(message_service, 'db_session') as mock_session:
                # 模拟数据库连接错误
                mock_session.commit.side_effect = Exception("Database connection lost")
                
                with patch.object(message_service, '_create_message_model'):
                    # 尝试创建消息
                    with pytest.raises(DatabaseAPIError) as exc_info:
                        await message_service.create(message_data)
                    
                    assert "database error" in str(exc_info.value).lower()
    
    async def test_concurrent_message_creation(self, message_service: MessageService, memory_monitor):
        """测试并发消息创建"""
        import asyncio
        
        async def create_message(index):
            message_data = {
                "conversation_id": str(uuid.uuid4()),
                "role": "user",
                "content": f"Message {index}",
                "message_type": "text"
            }
            
            with patch.object(message_service, 'conversation_service') as mock_conv_service:
                mock_conv_service.get_by_id.return_value = {"id": message_data["conversation_id"]}
                
                with patch.object(message_service, 'db_session') as mock_session:
                    mock_message = Mock()
                    mock_message.id = str(uuid.uuid4())
                    mock_message.content = message_data["content"]
                    
                    with patch.object(message_service, '_create_message_model', return_value=mock_message):
                        return await message_service.create(message_data)
            return None

        # 创建多个并发任务
        tasks = [create_message(i) for i in range(10)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 验证并发处理
        successful_results = [r for r in results if not isinstance(r, Exception)]
        assert len(successful_results) >= 0  # 至少部分成功
        
        # 监控内存使用
        memory_monitor.update_memory(50)  # 模拟内存使用
        assert memory_monitor.current_memory <= 100  # 内存使用合理
    
    async def test_message_with_unicode_content(self, message_service: MessageService):
        """测试Unicode内容的消息"""
        # Unicode内容
        unicode_content = "Hello 世界! 🌍 Здравствуй мир! مرحبا بالعالم!"
        
        message_data = {
            "conversation_id": str(uuid.uuid4()),
            "role": "user",
            "content": unicode_content,
            "message_type": "text"
        }
        
        with patch.object(message_service, 'conversation_service') as mock_conv_service:
            mock_conv_service.get_by_id.return_value = {"id": message_data["conversation_id"]}
            
            with patch.object(message_service, 'db_session') as mock_session:
                mock_message = Mock()
                mock_message.id = str(uuid.uuid4())
                mock_message.content = unicode_content
                
                with patch.object(message_service, '_create_message_model', return_value=mock_message):
                    # 创建消息
                    result = await message_service.create(message_data)
                
                # 验证Unicode内容被正确处理
                assert result.content == unicode_content
    
    async def test_message_with_none_values(self, message_service: MessageService):
        """测试包含None值的消息"""
        message_data = {
            "conversation_id": str(uuid.uuid4()),
            "role": "user",
            "content": "Test message",
            "message_type": "text",
            "metadata": None,  # None元数据
            "parent_message_id": None  # None父消息ID
        }
        
        with patch.object(message_service, 'conversation_service') as mock_conv_service:
            mock_conv_service.get_by_id.return_value = {"id": message_data["conversation_id"]}
            
            with patch.object(message_service, 'db_session') as mock_session:
                mock_message = Mock()
                mock_message.id = str(uuid.uuid4())
                mock_message.metadata = None
                mock_message.parent_message_id = None
                
                with patch.object(message_service, '_create_message_model', return_value=mock_message):
                    # 创建消息
                    result = await message_service.create(message_data)
                
                # 验证None值被正确处理
                assert result.metadata is None
                assert result.parent_message_id is None
    
    async def test_large_dataset_memory_usage(self, message_service: MessageService, memory_monitor):
        """测试大数据集的内存使用"""
        # 模拟大量消息数据
        large_dataset = []
        for i in range(1000):
            message = {
                "id": str(uuid.uuid4()),
                "conversation_id": str(uuid.uuid4()),
                "role": "user" if i % 2 == 0 else "assistant",
                "content": f"Message content {i}" * 10,  # 较长内容
                "created_at": datetime.utcnow()
            }
            large_dataset.append(message)
        
        with patch.object(message_service, 'db_session') as mock_session:
            mock_result = Mock()
            mock_result.scalars.return_value.all.return_value = large_dataset
            mock_session.execute.return_value = mock_result
            
            # 获取大量消息
            result = await message_service.get_by_conversation_id("test_conversation_id")
        
        # 监控内存使用
        memory_monitor.update_memory(200)  # 模拟内存使用
        assert memory_monitor.peak_memory <= 500  # 内存使用应该合理
        assert len(result) == 1000
    
    async def test_frequent_operations_performance(self, message_service: MessageService, performance_timer):
        """测试频繁操作的性能"""
        # 模拟频繁的消息操作
        operations_count = 100
        
        with performance_timer:
            for i in range(operations_count):
                # 模拟消息验证操作
                content = f"Test message {i}"
                await message_service.validate_message_content(content)
        
        # 验证性能
        avg_time_per_operation = performance_timer.elapsed / operations_count
        assert avg_time_per_operation < 0.01  # 每个操作应该小于10ms
    
    async def test_message_content_sanitization(self, message_service: MessageService):
        """测试消息内容清理"""
        # 包含潜在危险内容的消息
        dangerous_content = "<script>alert('xss')</script>Hello world!"
        
        # 清理内容
        sanitized_content = await message_service.sanitize_content(dangerous_content)
        
        # 验证危险内容被移除
        assert "<script>" not in sanitized_content
        assert "alert" not in sanitized_content
        assert "Hello world!" in sanitized_content
    
    async def test_message_format_conversion(self, message_service: MessageService):
        """测试消息格式转换"""
        # 原始消息数据
        message_data = {
            "id": str(uuid.uuid4()),
            "role": "user",
            "content": "Hello, world!",
            "metadata": {"source": "web"},
            "created_at": datetime.utcnow()
        }
        
        # 转换为不同格式
        formats = ["openai", "anthropic", "google", "custom"]
        
        for format_type in formats:
            converted = await message_service.convert_message_format(message_data, format_type)
            
            # 验证转换结果
            assert isinstance(converted, dict)
            assert "role" in converted or "author" in converted  # 不同格式可能使用不同字段
            assert "content" in converted or "text" in converted
    
    async def test_message_backup_and_recovery(self, message_service: MessageService):
        """测试消息备份和恢复"""
        # 模拟消息数据
        messages = [
            {
                "id": str(uuid.uuid4()),
                "conversation_id": "conv1",
                "role": "user",
                "content": "Hello",
                "created_at": datetime.utcnow().isoformat()
            },
            {
                "id": str(uuid.uuid4()),
                "conversation_id": "conv1",
                "role": "assistant",
                "content": "Hi there!",
                "created_at": datetime.utcnow().isoformat()
            }
        ]
        
        # 备份消息
        backup_data = await message_service.backup_messages("conv1")
        
        # 验证备份数据
        assert isinstance(backup_data, str)
        backup_json = json.loads(backup_data)
        assert "messages" in backup_json
        assert "metadata" in backup_json
        
        # 恢复消息
        with patch.object(message_service, 'conversation_service') as mock_conv_service:
            mock_conv_service.get_by_id.return_value = {"id": "conv1"}
            
            with patch.object(message_service, 'db_session') as mock_session:
                with patch.object(message_service, '_create_message_model'):
                    restored_count = await message_service.restore_messages(backup_data)
        
        # 验证恢复结果
        assert restored_count >= 0
    
    async def test_message_compression_and_decompression(self, message_service: MessageService):
        """测试消息压缩和解压缩"""
        # 大量重复内容的消息
        large_content = "This is a repeated message content. " * 1000
        
        # 压缩内容
        compressed = await message_service.compress_content(large_content)
        
        # 验证压缩效果
        assert len(compressed) < len(large_content.encode('utf-8'))
        
        # 解压缩内容
        decompressed = await message_service.decompress_content(compressed)
        
        # 验证解压缩结果
        assert decompressed == large_content
    
    async def test_message_indexing_for_search(self, message_service: MessageService):
        """测试消息搜索索引"""
        # 创建消息索引
        message_data = {
            "id": str(uuid.uuid4()),
            "conversation_id": "conv1",
            "role": "user",
            "content": "This is a searchable message about artificial intelligence and machine learning",
            "created_at": datetime.utcnow()
        }
        
        # 构建搜索索引
        index_data = await message_service.build_search_index(message_data)
        
        # 验证索引数据
        assert "keywords" in index_data
        assert "artificial" in index_data["keywords"]
        assert "intelligence" in index_data["keywords"]
        assert "machine" in index_data["keywords"]
        assert "learning" in index_data["keywords"]
        
        # 测试搜索
        search_results = await message_service.search_by_index("artificial intelligence")
        assert isinstance(search_results, list)
    
    async def test_message_analytics_calculation(self, message_service: MessageService):
        """测试消息分析计算"""
        # 模拟消息数据
        messages_data = [
            {"role": "user", "content": "Hello", "token_count": 1, "created_at": datetime.utcnow()},
            {"role": "assistant", "content": "Hi there!", "token_count": 2, "created_at": datetime.utcnow()},
            {"role": "user", "content": "How are you?", "token_count": 3, "created_at": datetime.utcnow()},
            {"role": "assistant", "content": "I'm doing well, thank you!", "token_count": 6, "created_at": datetime.utcnow()}
        ]
        
        # 计算分析数据
        analytics = await message_service.calculate_detailed_analytics(messages_data)
        
        # 验证分析结果
        assert analytics["total_messages"] == 4
        assert analytics["user_messages"] == 2
        assert analytics["assistant_messages"] == 2
        assert analytics["total_tokens"] == 12
        assert analytics["average_tokens_per_message"] == 3.0
        assert analytics["user_to_assistant_ratio"] == 1.0