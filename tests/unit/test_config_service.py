"""配置服务单元测试"""

import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock, mock_open
from datetime import datetime, timedelta
import uuid
import json
import os
import tempfile

from services.config_service import ConfigService
from exceptions.api_exceptions import ConfigError, ValidationAPIError
from tests.utils.test_helpers import AsyncTestCase, MockTestCase


@pytest.mark.service
@pytest.mark.unit
class TestConfigService(MockTestCase):
    """配置服务测试类"""
    
    @pytest.fixture
    def mock_config_file(self):
        """模拟配置文件"""
        config_data = {
            "database": {
                "url": "sqlite:///test.db",
                "pool_size": 10,
                "echo": False
            },
            "redis": {
                "host": "localhost",
                "port": 6379,
                "db": 0
            },
            "api": {
                "host": "0.0.0.0",
                "port": 8000,
                "debug": True
            },
            "logging": {
                "level": "INFO",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            }
        }
        return json.dumps(config_data)
    
    @pytest.fixture
    def config_service(self, mock_config_file):
        """创建配置服务实例"""
        with patch('builtins.open', mock_open(read_data=mock_config_file)):
            service = ConfigService(config_file="test_config.json")
            return service
        return None

    @pytest.fixture
    def sample_config_data(self):
        """示例配置数据"""
        return {
            "app_name": "LLM Comparison Tool",
            "version": "1.0.0",
            "environment": "development",
            "features": {
                "enable_caching": True,
                "enable_logging": True,
                "max_requests_per_minute": 100
            }
        }
    
    def test_load_config_success(self, config_service: ConfigService):
        """测试成功加载配置"""
        # 验证配置加载
        assert config_service.config is not None
        assert "database" in config_service.config
        assert "redis" in config_service.config
        assert "api" in config_service.config
        assert "logging" in config_service.config
    
    def test_get_config_value_success(self, config_service: ConfigService):
        """测试成功获取配置值"""
        # 获取配置值
        db_url = config_service.get("database.url")
        redis_host = config_service.get("redis.host")
        api_port = config_service.get("api.port")
        
        # 验证结果
        assert db_url == "sqlite:///test.db"
        assert redis_host == "localhost"
        assert api_port == 8000
    
    def test_get_config_value_with_default(self, config_service: ConfigService):
        """测试获取配置值（带默认值）"""
        # 获取不存在的配置值（带默认值）
        value = config_service.get("nonexistent.key", "default_value")
        
        # 验证结果
        assert value == "default_value"
    
    def test_get_config_value_not_found(self, config_service: ConfigService):
        """测试获取不存在的配置值"""
        # 尝试获取不存在的配置值
        with pytest.raises(ConfigError) as exc_info:
            config_service.get("nonexistent.key")
        
        assert "Configuration key not found" in str(exc_info.value)
    
    def test_set_config_value_success(self, config_service: ConfigService):
        """测试成功设置配置值"""
        # 设置配置值
        config_service.set("new.key", "new_value")
        
        # 验证设置结果
        value = config_service.get("new.key")
        assert value == "new_value"
    
    def test_update_config_value_success(self, config_service: ConfigService):
        """测试成功更新配置值"""
        # 更新现有配置值
        config_service.set("database.pool_size", 20)
        
        # 验证更新结果
        value = config_service.get("database.pool_size")
        assert value == 20
    
    def test_delete_config_value_success(self, config_service: ConfigService):
        """测试成功删除配置值"""
        # 删除配置值
        config_service.delete("api.debug")
        
        # 验证删除结果
        with pytest.raises(ConfigError):
            config_service.get("api.debug")
    
    def test_delete_config_value_not_found(self, config_service: ConfigService):
        """测试删除不存在的配置值"""
        # 尝试删除不存在的配置值
        with pytest.raises(ConfigError) as exc_info:
            config_service.delete("nonexistent.key")
        
        assert "Configuration key not found" in str(exc_info.value)
    
    def test_has_config_key(self, config_service: ConfigService):
        """测试检查配置键是否存在"""
        # 检查存在的键
        assert config_service.has("database.url") is True
        assert config_service.has("redis.host") is True
        
        # 检查不存在的键
        assert config_service.has("nonexistent.key") is False
    
    def test_get_section_success(self, config_service: ConfigService):
        """测试成功获取配置段"""
        # 获取配置段
        database_config = config_service.get_section("database")
        redis_config = config_service.get_section("redis")
        
        # 验证结果
        assert isinstance(database_config, dict)
        assert "url" in database_config
        assert "pool_size" in database_config
        
        assert isinstance(redis_config, dict)
        assert "host" in redis_config
        assert "port" in redis_config
    
    def test_get_section_not_found(self, config_service: ConfigService):
        """测试获取不存在的配置段"""
        # 尝试获取不存在的配置段
        with pytest.raises(ConfigError) as exc_info:
            config_service.get_section("nonexistent_section")
        
        assert "Configuration section not found" in str(exc_info.value)
    
    def test_set_section_success(self, config_service: ConfigService, sample_config_data):
        """测试成功设置配置段"""
        # 设置新的配置段
        config_service.set_section("app", sample_config_data)
        
        # 验证设置结果
        app_config = config_service.get_section("app")
        assert app_config == sample_config_data
        assert config_service.get("app.app_name") == "LLM Comparison Tool"
    
    def test_update_section_success(self, config_service: ConfigService):
        """测试成功更新配置段"""
        # 更新现有配置段
        new_database_config = {
            "url": "postgresql://localhost/test",
            "pool_size": 15,
            "echo": True,
            "timeout": 30
        }
        config_service.set_section("database", new_database_config)
        
        # 验证更新结果
        database_config = config_service.get_section("database")
        assert database_config == new_database_config
        assert config_service.get("database.url") == "postgresql://localhost/test"
        assert config_service.get("database.timeout") == 30
    
    def test_merge_config_success(self, config_service: ConfigService):
        """测试成功合并配置"""
        # 合并配置
        additional_config = {
            "database": {
                "timeout": 30,
                "retry_count": 3
            },
            "cache": {
                "ttl": 3600,
                "max_size": 1000
            }
        }
        
        config_service.merge(additional_config)
        
        # 验证合并结果
        assert config_service.get("database.url") == "sqlite:///test.db"  # 原有值保持
        assert config_service.get("database.timeout") == 30  # 新增值
        assert config_service.get("database.retry_count") == 3  # 新增值
        assert config_service.get("cache.ttl") == 3600  # 新段
    
    def test_save_config_success(self, config_service: ConfigService):
        """测试成功保存配置"""
        with patch('builtins.open', mock_open()) as mock_file:
            # 保存配置
            config_service.save()
            
            # 验证文件写入
            mock_file.assert_called_once()
            handle = mock_file()
            handle.write.assert_called_once()
    
    def test_reload_config_success(self, config_service: ConfigService, mock_config_file):
        """测试成功重新加载配置"""
        # 修改配置
        config_service.set("api.port", 9000)
        assert config_service.get("api.port") == 9000
        
        # 重新加载配置
        with patch('builtins.open', mock_open(read_data=mock_config_file)):
            config_service.reload()
        
        # 验证重新加载结果
        assert config_service.get("api.port") == 8000  # 恢复原值
    
    def test_validate_config_success(self, config_service: ConfigService):
        """测试成功验证配置"""
        # 定义配置模式
        schema = {
            "database": {
                "required": ["url", "pool_size"],
                "properties": {
                    "url": {"type": "string"},
                    "pool_size": {"type": "integer", "minimum": 1}
                }
            },
            "api": {
                "required": ["host", "port"],
                "properties": {
                    "host": {"type": "string"},
                    "port": {"type": "integer", "minimum": 1, "maximum": 65535}
                }
            }
        }
        
        # 验证配置
        is_valid = config_service.validate(schema)
        
        # 验证结果
        assert is_valid is True
    
    def test_validate_config_failure(self, config_service: ConfigService):
        """测试配置验证失败"""
        # 设置无效配置
        config_service.set("api.port", "invalid_port")
        
        # 定义配置模式
        schema = {
            "api": {
                "required": ["port"],
                "properties": {
                    "port": {"type": "integer"}
                }
            }
        }
        
        # 验证配置
        with pytest.raises(ValidationAPIError) as exc_info:
            config_service.validate(schema)
        
        assert "Configuration validation failed" in str(exc_info.value)
    
    def test_get_environment_config(self, config_service: ConfigService):
        """测试获取环境配置"""
        with patch.dict(os.environ, {
            'DATABASE_URL': 'postgresql://localhost/prod',
            'API_PORT': '8080',
            'DEBUG': 'false'
        }):
            # 获取环境配置
            env_config = config_service.get_environment_config()
            
            # 验证结果
            assert 'DATABASE_URL' in env_config
            assert env_config['DATABASE_URL'] == 'postgresql://localhost/prod'
            assert env_config['API_PORT'] == '8080'
            assert env_config['DEBUG'] == 'false'
    
    def test_override_with_environment(self, config_service: ConfigService):
        """测试使用环境变量覆盖配置"""
        with patch.dict(os.environ, {
            'DATABASE_URL': 'postgresql://localhost/prod',
            'API_PORT': '8080'
        }):
            # 使用环境变量覆盖配置
            config_service.override_with_environment({
                'DATABASE_URL': 'database.url',
                'API_PORT': 'api.port'
            })
            
            # 验证覆盖结果
            assert config_service.get('database.url') == 'postgresql://localhost/prod'
            assert config_service.get('api.port') == '8080'
    
    def test_get_config_history(self, config_service: ConfigService):
        """测试获取配置历史"""
        # 修改配置多次
        config_service.set('api.port', 8001)
        config_service.set('api.port', 8002)
        config_service.set('database.pool_size', 15)
        
        # 获取配置历史
        history = config_service.get_history()
        
        # 验证历史记录
        assert len(history) >= 3
        assert any('api.port' in entry['key'] for entry in history)
        assert any('database.pool_size' in entry['key'] for entry in history)
    
    def test_rollback_config_changes(self, config_service: ConfigService):
        """测试回滚配置更改"""
        # 记录原始值
        original_port = config_service.get('api.port')
        
        # 修改配置
        config_service.set('api.port', 9000)
        assert config_service.get('api.port') == 9000
        
        # 回滚配置
        config_service.rollback(steps=1)
        
        # 验证回滚结果
        assert config_service.get('api.port') == original_port
    
    def test_export_config(self, config_service: ConfigService):
        """测试导出配置"""
        # 导出配置
        exported_config = config_service.export()
        
        # 验证导出结果
        assert isinstance(exported_config, dict)
        assert 'database' in exported_config
        assert 'redis' in exported_config
        assert 'api' in exported_config
        assert 'logging' in exported_config
    
    def test_import_config(self, config_service: ConfigService, sample_config_data):
        """测试导入配置"""
        # 导入配置
        config_service.import_config(sample_config_data)
        
        # 验证导入结果
        assert config_service.get('app_name') == 'LLM Comparison Tool'
        assert config_service.get('version') == '1.0.0'
        assert config_service.get('features.enable_caching') is True
    
    def test_watch_config_changes(self, config_service: ConfigService):
        """测试监听配置更改"""
        changes = []
        
        def on_change(key, old_value, new_value):
            changes.append({
                'key': key,
                'old_value': old_value,
                'new_value': new_value
            })
        
        # 注册监听器
        config_service.watch('api.port', on_change)
        
        # 修改配置
        config_service.set('api.port', 9000)
        
        # 验证监听结果
        assert len(changes) == 1
        assert changes[0]['key'] == 'api.port'
        assert changes[0]['new_value'] == 9000
    
    def test_config_encryption(self, config_service: ConfigService):
        """测试配置加密"""
        # 设置敏感配置
        sensitive_value = "secret_api_key_12345"
        config_service.set_encrypted('api.secret_key', sensitive_value)
        
        # 获取加密配置
        decrypted_value = config_service.get_encrypted('api.secret_key')
        
        # 验证加密/解密结果
        assert decrypted_value == sensitive_value
        
        # 验证原始存储是加密的
        raw_value = config_service.config.get('api', {}).get('secret_key')
        assert raw_value != sensitive_value  # 应该是加密后的值
    
    def test_config_templating(self, config_service: ConfigService):
        """测试配置模板"""
        # 设置模板配置
        config_service.set('app.name', 'MyApp')
        config_service.set('app.version', '1.0.0')
        config_service.set('app.title', '${app.name} v${app.version}')
        
        # 获取模板化配置
        title = config_service.get_templated('app.title')
        
        # 验证模板结果
        assert title == 'MyApp v1.0.0'
    
    def test_config_caching(self, config_service: ConfigService):
        """测试配置缓存"""
        # 启用缓存
        config_service.enable_cache(ttl=60)
        
        # 多次获取相同配置
        value1 = config_service.get('database.url')
        value2 = config_service.get('database.url')
        value3 = config_service.get('database.url')
        
        # 验证缓存效果
        assert value1 == value2 == value3
        
        # 验证缓存统计
        cache_stats = config_service.get_cache_stats()
        assert cache_stats['hits'] >= 2
        assert cache_stats['misses'] >= 1


@pytest.mark.service
@pytest.mark.unit
class TestConfigServiceEdgeCases(MockTestCase):
    """配置服务边界情况测试类"""
    
    @pytest.fixture
    def config_service(self):
        """创建配置服务实例"""
        return ConfigService()
    
    def test_load_invalid_json_config(self):
        """测试加载无效JSON配置"""
        invalid_json = '{"database": {"url": "test", "port": }}'  # 无效JSON
        
        with patch('builtins.open', mock_open(read_data=invalid_json)):
            with pytest.raises(ConfigError) as exc_info:
                ConfigService(config_file="invalid_config.json")
            
            assert "Invalid JSON configuration" in str(exc_info.value)
    
    def test_load_missing_config_file(self):
        """测试加载不存在的配置文件"""
        with patch('builtins.open', side_effect=FileNotFoundError("File not found")):
            with pytest.raises(ConfigError) as exc_info:
                ConfigService(config_file="missing_config.json")
            
            assert "Configuration file not found" in str(exc_info.value)
    
    def test_deep_nested_config_access(self, config_service: ConfigService):
        """测试深层嵌套配置访问"""
        # 设置深层嵌套配置
        nested_config = {
            "level1": {
                "level2": {
                    "level3": {
                        "level4": {
                            "value": "deep_value"
                        }
                    }
                }
            }
        }
        
        config_service.merge(nested_config)
        
        # 访问深层嵌套值
        value = config_service.get("level1.level2.level3.level4.value")
        assert value == "deep_value"
    
    def test_config_with_special_characters(self, config_service: ConfigService):
        """测试包含特殊字符的配置"""
        # 设置包含特殊字符的配置
        special_values = {
            "unicode_value": "测试中文配置",
            "emoji_value": "🚀 Rocket Config",
            "special_chars": "!@#$%^&*()_+-=[]{}|;':,.<>?",
            "newline_value": "Line 1\nLine 2\nLine 3",
            "json_string": '{"nested": "json"}'
        }
        
        for key, value in special_values.items():
            config_service.set(f"special.{key}", value)
        
        # 验证特殊字符配置
        for key, expected_value in special_values.items():
            actual_value = config_service.get(f"special.{key}")
            assert actual_value == expected_value
    
    def test_config_type_conversion(self, config_service: ConfigService):
        """测试配置类型转换"""
        # 设置不同类型的配置
        config_service.set("types.string_number", "123")
        config_service.set("types.string_boolean", "true")
        config_service.set("types.string_float", "3.14")
        
        # 测试类型转换
        assert config_service.get_int("types.string_number") == 123
        assert config_service.get_bool("types.string_boolean") is True
        assert config_service.get_float("types.string_float") == 3.14
    
    def test_config_with_none_values(self, config_service: ConfigService):
        """测试包含None值的配置"""
        # 设置None值配置
        config_service.set("nullable.value", None)
        config_service.set("nullable.empty_string", "")
        config_service.set("nullable.zero", 0)
        config_service.set("nullable.false", False)
        
        # 验证None值处理
        assert config_service.get("nullable.value") is None
        assert config_service.get("nullable.empty_string") == ""
        assert config_service.get("nullable.zero") == 0
        assert config_service.get("nullable.false") is False
        
        # 测试默认值处理
        assert config_service.get("nullable.value", "default") == "default"
        assert config_service.get("nullable.empty_string", "default") == ""
    
    def test_concurrent_config_access(self, config_service: ConfigService):
        """测试并发配置访问"""
        import threading
        import time
        
        results = []
        errors = []
        
        def config_operation(index):
            try:
                # 并发读写配置
                config_service.set(f"concurrent.key_{index}", f"value_{index}")
                value = config_service.get(f"concurrent.key_{index}")
                results.append(value)
                
                # 并发修改同一个键
                config_service.set("shared.counter", index)
                time.sleep(0.001)  # 模拟处理时间
                
            except Exception as e:
                errors.append(e)
        
        # 创建多个线程并发访问配置
        threads = []
        for i in range(50):
            thread = threading.Thread(target=config_operation, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证并发访问结果
        assert len(errors) == 0
        assert len(results) == 50
    
    def test_large_config_handling(self, config_service: ConfigService, memory_monitor):
        """测试大配置处理"""
        with memory_monitor:
            # 创建大配置
            large_config = {}
            for i in range(10000):
                large_config[f"key_{i}"] = {
                    "value": f"value_{i}" * 100,  # 较长的值
                    "metadata": {
                        "created_at": datetime.utcnow().isoformat(),
                        "index": i,
                        "tags": [f"tag_{j}" for j in range(10)]
                    }
                }
            
            # 合并大配置
            config_service.merge({"large_section": large_config})
            
            # 访问大配置中的值
            value = config_service.get("large_section.key_5000.value")
            assert "value_5000" in value
        
        # 验证内存使用
        memory_usage = memory_monitor.get_peak_memory()
        assert memory_usage < 100 * 1024 * 1024  # 小于100MB
    
    def test_config_circular_references(self, config_service: ConfigService):
        """测试配置循环引用"""
        # 设置可能导致循环引用的配置
        config_service.set("circular.a", "${circular.b}")
        config_service.set("circular.b", "${circular.c}")
        config_service.set("circular.c", "${circular.a}")
        
        # 尝试解析循环引用
        with pytest.raises(ConfigError) as exc_info:
            config_service.get_templated("circular.a")
        
        assert "Circular reference detected" in str(exc_info.value)
    
    def test_config_performance_benchmark(self, config_service: ConfigService, performance_timer):
        """测试配置性能基准"""
        # 设置测试配置
        for i in range(1000):
            config_service.set(f"benchmark.key_{i}", f"value_{i}")
        
        with performance_timer:
            # 执行大量配置读取操作
            for i in range(10000):
                key = f"benchmark.key_{i % 1000}"
                value = config_service.get(key)
                assert value == f"value_{i % 1000}"
        
        # 验证性能
        elapsed_time = performance_timer.elapsed
        assert elapsed_time < 1.0  # 10000次读取应该在1秒内完成
    
    def test_config_backup_and_restore(self, config_service: ConfigService):
        """测试配置备份和恢复"""
        # 设置初始配置
        config_service.set("backup_test.value1", "original_value1")
        config_service.set("backup_test.value2", "original_value2")
        
        # 创建备份
        backup = config_service.create_backup()
        
        # 修改配置
        config_service.set("backup_test.value1", "modified_value1")
        config_service.delete("backup_test.value2")
        config_service.set("backup_test.value3", "new_value3")
        
        # 验证修改
        assert config_service.get("backup_test.value1") == "modified_value1"
        assert not config_service.has("backup_test.value2")
        assert config_service.get("backup_test.value3") == "new_value3"
        
        # 恢复备份
        config_service.restore_backup(backup)
        
        # 验证恢复
        assert config_service.get("backup_test.value1") == "original_value1"
        assert config_service.get("backup_test.value2") == "original_value2"
        assert not config_service.has("backup_test.value3")
    
    def test_config_schema_evolution(self, config_service: ConfigService):
        """测试配置模式演进"""
        # 模拟配置模式升级
        old_config = {
            "database_url": "sqlite:///old.db",
            "cache_enabled": True,
            "log_level": "INFO"
        }
        
        # 定义迁移规则
        migration_rules = {
            "database_url": "database.url",
            "cache_enabled": "cache.enabled",
            "log_level": "logging.level"
        }
        
        # 应用旧配置
        config_service.merge(old_config)
        
        # 执行配置迁移
        config_service.migrate_schema(migration_rules)
        
        # 验证迁移结果
        assert config_service.get("database.url") == "sqlite:///old.db"
        assert config_service.get("cache.enabled") is True
        assert config_service.get("logging.level") == "INFO"
        
        # 验证旧键已删除
        assert not config_service.has("database_url")
        assert not config_service.has("cache_enabled")
        assert not config_service.has("log_level")