"""用户服务单元测试"""

import hashlib
import uuid
from datetime import datetime
from unittest.mock import Mock

import pytest
from exceptions.api_exceptions import NotFoundAPIError, ValidationAPIError, DuplicateAPIError, AuthenticationAPIError
from models.user import UserModel
from schemas.user import UserCreate, UserUpdate, UserLogin
from services.user_service import UserService

from tests.utils.test_helpers import MockTestCase


@pytest.mark.service
@pytest.mark.unit
class TestUserService(MockTestCase):
    """用户服务测试类"""
    
    @pytest.fixture
    def mock_db_session(self):
        """模拟数据库会话"""
        session = Mock()
        session.query.return_value = session
        session.filter.return_value = session
        session.filter_by.return_value = session
        session.first.return_value = None
        session.all.return_value = []
        session.count.return_value = 0
        session.add = Mock()
        session.commit = Mock()
        session.delete = Mock()
        session.rollback = Mock()
        return session
    
    @pytest.fixture
    def user_service(self, mock_db_session):
        """创建用户服务实例"""
        return UserService(db_session=mock_db_session)
    
    @pytest.fixture
    def sample_user_create(self):
        """示例用户创建数据"""
        return UserCreate(
            username="testuser",
            email="<EMAIL>",
            password="securepassword123",
            full_name="Test User",
            is_active=True
        )
    
    @pytest.fixture
    def sample_user_update(self):
        """示例用户更新数据"""
        return UserUpdate(
            full_name="Updated Test User",
            email="<EMAIL>",
            is_active=False
        )
    
    @pytest.fixture
    def sample_user_login(self):
        """示例用户登录数据"""
        return UserLogin(
            username="testuser",
            password="securepassword123"
        )
    
    @pytest.fixture
    def sample_user_model(self):
        """示例用户模型"""
        password_hash = hashlib.sha256("securepassword123".encode()).hexdigest()
        return UserModel(
            id=str(uuid.uuid4()),
            username="testuser",
            email="<EMAIL>",
            password_hash=password_hash,
            full_name="Test User",
            is_active=True,
            is_admin=False,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
            last_login_at=None,
            login_count=0
        )
    
    def test_create_user_success(self, user_service: UserService, mock_db_session, sample_user_create):
        """测试成功创建用户"""
        # 模拟用户名和邮箱不存在
        mock_db_session.query.return_value.filter.return_value.first.return_value = None
        
        # 创建用户
        result = user_service.create_user(sample_user_create)
        
        # 验证结果
        assert result.username == sample_user_create.username
        assert result.email == sample_user_create.email
        assert result.full_name == sample_user_create.full_name
        assert result.is_active == sample_user_create.is_active
        assert result.password_hash is not None
        assert result.password_hash != sample_user_create.password  # 密码应该被哈希
        
        # 验证数据库操作
        mock_db_session.add.assert_called_once()
        mock_db_session.commit.assert_called_once()
    
    def test_create_user_duplicate_username(self, user_service: UserService, mock_db_session, sample_user_create, sample_user_model):
        """测试创建重复用户名的用户"""
        # 模拟用户名已存在
        mock_db_session.query.return_value.filter.return_value.first.return_value = sample_user_model
        
        # 尝试创建重复用户名的用户
        with pytest.raises(DuplicateAPIError) as exc_info:
            user_service.create_user(sample_user_create)
        
        assert "Username already exists" in str(exc_info.value)
    
    def test_create_user_duplicate_email(self, user_service: UserService, mock_db_session, sample_user_create, sample_user_model):
        """测试创建重复邮箱的用户"""
        # 第一次查询用户名返回None，第二次查询邮箱返回已存在用户
        mock_db_session.query.return_value.filter.return_value.first.side_effect = [None, sample_user_model]
        
        # 尝试创建重复邮箱的用户
        with pytest.raises(DuplicateAPIError) as exc_info:
            user_service.create_user(sample_user_create)
        
        assert "Email already exists" in str(exc_info.value)
    
    def test_create_user_weak_password(self, user_service: UserService, mock_db_session):
        """测试创建弱密码用户"""
        weak_password_create = UserCreate(
            username="testuser",
            email="<EMAIL>",
            password="123",  # 弱密码
            full_name="Test User"
        )
        
        # 尝试创建弱密码用户
        with pytest.raises(ValidationAPIError) as exc_info:
            user_service.create_user(weak_password_create)
        
        assert "Password too weak" in str(exc_info.value)
    
    def test_get_user_by_id_success(self, user_service: UserService, mock_db_session, sample_user_model):
        """测试成功根据ID获取用户"""
        # 模拟数据库查询
        mock_db_session.query.return_value.filter.return_value.first.return_value = sample_user_model
        
        # 获取用户
        result = user_service.get_user_by_id(sample_user_model.id)
        
        # 验证结果
        assert result.id == sample_user_model.id
        assert result.username == sample_user_model.username
        assert result.email == sample_user_model.email
    
    def test_get_user_by_id_not_found(self, user_service: UserService, mock_db_session):
        """测试获取不存在的用户"""
        # 模拟用户不存在
        mock_db_session.query.return_value.filter.return_value.first.return_value = None
        
        # 尝试获取不存在的用户
        with pytest.raises(NotFoundAPIError) as exc_info:
            user_service.get_user_by_id("nonexistent-id")
        
        assert "User not found" in str(exc_info.value)
    
    def test_get_user_by_username_success(self, user_service: UserService, mock_db_session, sample_user_model):
        """测试成功根据用户名获取用户"""
        # 模拟数据库查询
        mock_db_session.query.return_value.filter_by.return_value.first.return_value = sample_user_model
        
        # 获取用户
        result = user_service.get_user_by_username(sample_user_model.username)
        
        # 验证结果
        assert result.username == sample_user_model.username
    
    def test_get_user_by_email_success(self, user_service: UserService, mock_db_session, sample_user_model):
        """测试成功根据邮箱获取用户"""
        # 模拟数据库查询
        mock_db_session.query.return_value.filter_by.return_value.first.return_value = sample_user_model
        
        # 获取用户
        result = user_service.get_user_by_email(sample_user_model.email)
        
        # 验证结果
        assert result.email == sample_user_model.email
    
    def test_get_users_list(self, user_service: UserService, mock_db_session, sample_user_model):
        """测试获取用户列表"""
        # 模拟数据库查询
        mock_db_session.query.return_value.all.return_value = [sample_user_model]
        
        # 获取用户列表
        result = user_service.get_users()
        
        # 验证结果
        assert len(result) == 1
        assert result[0].id == sample_user_model.id
    
    def test_get_active_users(self, user_service: UserService, mock_db_session, sample_user_model):
        """测试获取活跃用户"""
        # 模拟数据库查询
        mock_db_session.query.return_value.filter_by.return_value.all.return_value = [sample_user_model]
        
        # 获取活跃用户
        result = user_service.get_active_users()
        
        # 验证结果
        assert len(result) == 1
        assert result[0].is_active is True
    
    def test_update_user_success(self, user_service: UserService, mock_db_session, sample_user_model, sample_user_update):
        """测试成功更新用户"""
        # 模拟数据库查询
        mock_db_session.query.return_value.filter.return_value.first.return_value = sample_user_model
        
        # 更新用户
        result = user_service.update_user(sample_user_model.id, sample_user_update)
        
        # 验证结果
        assert result.full_name == sample_user_update.full_name
        assert result.email == sample_user_update.email
        assert result.is_active == sample_user_update.is_active
        
        # 验证数据库操作
        mock_db_session.commit.assert_called_once()
    
    def test_update_user_not_found(self, user_service: UserService, mock_db_session, sample_user_update):
        """测试更新不存在的用户"""
        # 模拟用户不存在
        mock_db_session.query.return_value.filter.return_value.first.return_value = None
        
        # 尝试更新不存在的用户
        with pytest.raises(NotFoundAPIError) as exc_info:
            user_service.update_user("nonexistent-id", sample_user_update)
        
        assert "User not found" in str(exc_info.value)
    
    def test_delete_user_success(self, user_service: UserService, mock_db_session, sample_user_model):
        """测试成功删除用户"""
        # 模拟数据库查询
        mock_db_session.query.return_value.filter.return_value.first.return_value = sample_user_model
        
        # 删除用户
        result = user_service.delete_user(sample_user_model.id)
        
        # 验证结果
        assert result is True
        
        # 验证数据库操作
        mock_db_session.delete.assert_called_once_with(sample_user_model)
        mock_db_session.commit.assert_called_once()
    
    def test_delete_user_not_found(self, user_service: UserService, mock_db_session):
        """测试删除不存在的用户"""
        # 模拟用户不存在
        mock_db_session.query.return_value.filter.return_value.first.return_value = None
        
        # 尝试删除不存在的用户
        with pytest.raises(NotFoundAPIError) as exc_info:
            user_service.delete_user("nonexistent-id")
        
        assert "User not found" in str(exc_info.value)
    
    def test_authenticate_user_success(self, user_service: UserService, mock_db_session, sample_user_model, sample_user_login):
        """测试成功认证用户"""
        # 模拟数据库查询
        mock_db_session.query.return_value.filter_by.return_value.first.return_value = sample_user_model
        
        # 认证用户
        result = user_service.authenticate_user(sample_user_login.username, sample_user_login.password)
        
        # 验证结果
        assert result.username == sample_user_model.username
        assert result.last_login_at is not None
        assert result.login_count == 1
        
        # 验证数据库操作
        mock_db_session.commit.assert_called_once()
    
    def test_authenticate_user_invalid_username(self, user_service: UserService, mock_db_session, sample_user_login):
        """测试认证无效用户名"""
        # 模拟用户不存在
        mock_db_session.query.return_value.filter_by.return_value.first.return_value = None
        
        # 尝试认证无效用户名
        with pytest.raises(AuthenticationAPIError) as exc_info:
            user_service.authenticate_user(sample_user_login.username, sample_user_login.password)
        
        assert "Invalid credentials" in str(exc_info.value)
    
    def test_authenticate_user_invalid_password(self, user_service: UserService, mock_db_session, sample_user_model):
        """测试认证无效密码"""
        # 模拟数据库查询
        mock_db_session.query.return_value.filter_by.return_value.first.return_value = sample_user_model
        
        # 尝试认证无效密码
        with pytest.raises(AuthenticationAPIError) as exc_info:
            user_service.authenticate_user(sample_user_model.username, "wrongpassword")
        
        assert "Invalid credentials" in str(exc_info.value)
    
    def test_authenticate_user_inactive(self, user_service: UserService, mock_db_session, sample_user_model, sample_user_login):
        """测试认证非活跃用户"""
        # 设置用户为非活跃状态
        sample_user_model.is_active = False
        
        # 模拟数据库查询
        mock_db_session.query.return_value.filter_by.return_value.first.return_value = sample_user_model
        
        # 尝试认证非活跃用户
        with pytest.raises(AuthenticationAPIError) as exc_info:
            user_service.authenticate_user(sample_user_login.username, sample_user_login.password)
        
        assert "User account is inactive" in str(exc_info.value)
    
    def test_change_password_success(self, user_service: UserService, mock_db_session, sample_user_model):
        """测试成功更改密码"""
        # 模拟数据库查询
        mock_db_session.query.return_value.filter.return_value.first.return_value = sample_user_model
        
        old_password = "securepassword123"
        new_password = "newsecurepassword456"
        
        # 更改密码
        result = user_service.change_password(sample_user_model.id, old_password, new_password)
        
        # 验证结果
        assert result is True
        assert sample_user_model.password_hash != hashlib.sha256(old_password.encode()).hexdigest()
        
        # 验证数据库操作
        mock_db_session.commit.assert_called_once()
    
    def test_change_password_invalid_old_password(self, user_service: UserService, mock_db_session, sample_user_model):
        """测试更改密码时旧密码错误"""
        # 模拟数据库查询
        mock_db_session.query.return_value.filter.return_value.first.return_value = sample_user_model
        
        old_password = "wrongpassword"
        new_password = "newsecurepassword456"
        
        # 尝试更改密码
        with pytest.raises(AuthenticationAPIError) as exc_info:
            user_service.change_password(sample_user_model.id, old_password, new_password)
        
        assert "Current password is incorrect" in str(exc_info.value)
    
    def test_reset_password_success(self, user_service: UserService, mock_db_session, sample_user_model):
        """测试成功重置密码"""
        # 模拟数据库查询
        mock_db_session.query.return_value.filter_by.return_value.first.return_value = sample_user_model
        
        # 重置密码
        new_password = user_service.reset_password(sample_user_model.email)
        
        # 验证结果
        assert new_password is not None
        assert len(new_password) >= 8
        
        # 验证数据库操作
        mock_db_session.commit.assert_called_once()
    
    def test_toggle_user_status(self, user_service: UserService, mock_db_session, sample_user_model):
        """测试切换用户状态"""
        # 模拟数据库查询
        mock_db_session.query.return_value.filter.return_value.first.return_value = sample_user_model
        
        original_status = sample_user_model.is_active
        
        # 切换用户状态
        result = user_service.toggle_user_status(sample_user_model.id)
        
        # 验证结果
        assert result.is_active != original_status
        
        # 验证数据库操作
        mock_db_session.commit.assert_called_once()
    
    def test_get_user_statistics(self, user_service: UserService, mock_db_session, sample_user_model):
        """测试获取用户统计"""
        # 设置统计数据
        sample_user_model.login_count = 50
        sample_user_model.last_login_at = datetime.utcnow()
        
        # 模拟数据库查询
        mock_db_session.query.return_value.filter.return_value.first.return_value = sample_user_model
        
        # 获取用户统计
        result = user_service.get_user_statistics(sample_user_model.id)
        
        # 验证结果
        assert result["login_count"] == 50
        assert result["last_login_at"] is not None
        assert "created_at" in result
        assert "is_active" in result
    
    def test_search_users(self, user_service: UserService, mock_db_session, sample_user_model):
        """测试搜索用户"""
        # 模拟数据库查询
        mock_db_session.query.return_value.filter.return_value.all.return_value = [sample_user_model]
        
        # 搜索用户
        result = user_service.search_users("test")
        
        # 验证结果
        assert len(result) == 1
        assert result[0].username == sample_user_model.username
    
    def test_get_users_pagination(self, user_service: UserService, mock_db_session, sample_user_model):
        """测试分页获取用户"""
        # 模拟数据库查询
        mock_query = mock_db_session.query.return_value
        mock_query.offset.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = [sample_user_model]
        mock_db_session.query.return_value.count.return_value = 1
        
        # 分页获取用户
        result, total = user_service.get_users_paginated(page=1, page_size=10)
        
        # 验证结果
        assert len(result) == 1
        assert total == 1
        
        # 验证分页参数
        mock_query.offset.assert_called_once_with(0)
        mock_query.limit.assert_called_once_with(10)
    
    def test_bulk_update_users(self, user_service: UserService, mock_db_session, sample_user_model):
        """测试批量更新用户"""
        # 模拟数据库查询
        mock_db_session.query.return_value.filter.return_value.all.return_value = [sample_user_model]
        
        user_ids = [sample_user_model.id]
        update_data = {"is_active": False}
        
        # 批量更新用户
        result = user_service.bulk_update_users(user_ids, update_data)
        
        # 验证结果
        assert len(result) == 1
        assert result[0].is_active is False
        
        # 验证数据库操作
        mock_db_session.commit.assert_called_once()
    
    def test_export_users(self, user_service: UserService, mock_db_session, sample_user_model):
        """测试导出用户"""
        # 模拟数据库查询
        mock_db_session.query.return_value.all.return_value = [sample_user_model]
        
        # 导出用户
        result = user_service.export_users()
        
        # 验证结果
        assert len(result) == 1
        assert result[0]["username"] == sample_user_model.username
        assert result[0]["email"] == sample_user_model.email
        # 密码哈希不应该被导出
        assert "password_hash" not in result[0]


@pytest.mark.service
@pytest.mark.unit
class TestUserServiceEdgeCases(MockTestCase):
    """用户服务边界情况测试类"""
    
    @pytest.fixture
    def mock_db_session(self):
        """模拟数据库会话"""
        session = Mock()
        return session
    
    @pytest.fixture
    def user_service(self, mock_db_session):
        """创建用户服务实例"""
        return UserService(db_session=mock_db_session)
    
    def test_create_user_with_special_characters(self, user_service: UserService, mock_db_session):
        """测试创建包含特殊字符的用户"""
        special_create = UserCreate(
            username="user_with-special.chars",
            email="<EMAIL>",
            password="securepassword123",
            full_name="用户 with émojis 👤"
        )
        
        # 模拟数据库操作
        mock_db_session.query.return_value.filter.return_value.first.return_value = None
        
        # 创建用户
        result = user_service.create_user(special_create)
        
        # 验证特殊字符被正确处理
        assert result.username == special_create.username
        assert result.email == special_create.email
        assert result.full_name == special_create.full_name
    
    def test_create_user_with_very_long_username(self, user_service: UserService, mock_db_session):
        """测试创建超长用户名的用户"""
        long_username = "a" * 1000  # 超长用户名
        
        long_username_create = UserCreate(
            username=long_username,
            email="<EMAIL>",
            password="securepassword123",
            full_name="Test User"
        )
        
        # 应该抛出验证错误
        with pytest.raises(ValidationAPIError) as exc_info:
            user_service.create_user(long_username_create)
        
        assert "Username too long" in str(exc_info.value)
    
    def test_create_user_with_invalid_email_format(self, user_service: UserService, mock_db_session):
        """测试创建无效邮箱格式的用户"""
        invalid_email_create = UserCreate(
            username="testuser",
            email="invalid-email-format",
            password="securepassword123",
            full_name="Test User"
        )
        
        # 应该抛出验证错误
        with pytest.raises(ValidationAPIError) as exc_info:
            user_service.create_user(invalid_email_create)
        
        assert "Invalid email format" in str(exc_info.value)
    
    def test_password_hashing_security(self, user_service: UserService, mock_db_session):
        """测试密码哈希安全性"""
        password = "securepassword123"
        
        # 测试相同密码产生不同哈希（如果使用盐）
        hash1 = user_service.hash_password(password)
        hash2 = user_service.hash_password(password)
        
        # 验证密码被正确哈希
        assert hash1 != password
        assert len(hash1) > 0
        
        # 验证密码验证功能
        assert user_service.verify_password(password, hash1) is True
        assert user_service.verify_password("wrongpassword", hash1) is False
    
    def test_concurrent_user_creation(self, user_service: UserService, mock_db_session):
        """测试并发创建用户"""
        import threading

        results = []
        errors = []
        
        def create_user(index):
            try:
                create_data = UserCreate(
                    username=f"user{index}",
                    email=f"user{index}@example.com",
                    password="securepassword123",
                    full_name=f"User {index}"
                )
                result = user_service.create_user(create_data)
                results.append(result)
            except Exception as e:
                errors.append(e)
        
        # 模拟数据库操作
        mock_db_session.query.return_value.filter.return_value.first.return_value = None
        
        # 创建多个线程同时创建用户
        threads = []
        for i in range(10):
            thread = threading.Thread(target=create_user, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证结果
        assert len(errors) == 0
        assert len(results) == 10
    
    def test_user_session_management(self, user_service: UserService, mock_db_session, sample_user_model):
        """测试用户会话管理"""
        # 模拟数据库查询
        mock_db_session.query.return_value.filter.return_value.first.return_value = sample_user_model
        
        # 创建用户会话
        session_token = user_service.create_user_session(sample_user_model.id)
        
        # 验证会话令牌
        assert session_token is not None
        assert len(session_token) > 0
        
        # 验证会话
        is_valid = user_service.validate_user_session(session_token)
        assert is_valid is True
        
        # 销毁会话
        user_service.destroy_user_session(session_token)
        
        # 验证会话已失效
        is_valid_after_destroy = user_service.validate_user_session(session_token)
        assert is_valid_after_destroy is False
    
    def test_user_role_management(self, user_service: UserService, mock_db_session, sample_user_model):
        """测试用户角色管理"""
        # 模拟数据库查询
        mock_db_session.query.return_value.filter.return_value.first.return_value = sample_user_model
        
        # 设置管理员权限
        result = user_service.set_admin_privileges(sample_user_model.id, True)
        assert result.is_admin is True
        
        # 取消管理员权限
        result = user_service.set_admin_privileges(sample_user_model.id, False)
        assert result.is_admin is False
        
        # 验证数据库操作
        assert mock_db_session.commit.call_count == 2
    
    def test_user_activity_tracking(self, user_service: UserService, mock_db_session, sample_user_model):
        """测试用户活动跟踪"""
        # 模拟数据库查询
        mock_db_session.query.return_value.filter.return_value.first.return_value = sample_user_model
        
        # 记录用户活动
        user_service.record_user_activity(sample_user_model.id, "login")
        user_service.record_user_activity(sample_user_model.id, "api_call")
        user_service.record_user_activity(sample_user_model.id, "logout")
        
        # 获取用户活动历史
        activities = user_service.get_user_activities(sample_user_model.id)
        
        # 验证活动记录
        assert len(activities) == 3
        assert activities[0]["action"] == "login"
        assert activities[1]["action"] == "api_call"
        assert activities[2]["action"] == "logout"
    
    def test_memory_usage_with_large_user_dataset(self, user_service: UserService, mock_db_session, memory_monitor):
        """测试大用户数据集的内存使用"""
        # 创建大量用户模型
        large_dataset = []
        for i in range(10000):
            user = UserModel(
                id=str(uuid.uuid4()),
                username=f"user{i}",
                email=f"user{i}@example.com",
                password_hash=hashlib.sha256(f"password{i}".encode()).hexdigest(),
                full_name=f"User {i}",
                is_active=True,
                created_at=datetime.utcnow()
            )
            large_dataset.append(user)
        
        # 模拟数据库返回大数据集
        mock_db_session.query.return_value.all.return_value = large_dataset
        
        with memory_monitor:
            # 获取大量用户
            result = user_service.get_users()
        
        # 验证内存使用在合理范围内
        memory_usage = memory_monitor.get_peak_memory()
        assert memory_usage < 100 * 1024 * 1024  # 小于100MB
        assert len(result) == 10000
    
    def test_performance_with_frequent_authentication(self, user_service: UserService, mock_db_session, sample_user_model, performance_timer):
        """测试频繁认证的性能"""
        # 模拟数据库操作
        mock_db_session.query.return_value.filter_by.return_value.first.return_value = sample_user_model
        
        with performance_timer:
            # 执行大量认证操作
            for i in range(1000):
                try:
                    user_service.authenticate_user(sample_user_model.username, "securepassword123")
                except:
                    pass  # 忽略认证错误，专注于性能测试
        
        # 验证性能在可接受范围内
        elapsed_time = performance_timer.elapsed
        assert elapsed_time < 5.0  # 1000次认证应该在5秒内完成