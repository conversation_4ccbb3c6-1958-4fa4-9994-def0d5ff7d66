from datetime import datetime, timedelta
from decimal import Decimal
from unittest.mock import Mock, patch

import pytest

from services.cost_service import CostService
from tests.utils.test_helpers import MockTestCase


@pytest.mark.service
@pytest.mark.unit
class TestCostService(MockTestCase):
    """成本服务测试类"""
    
    @pytest.fixture
    def mock_db_session(self):
        """模拟数据库会话"""
        session = Mock()
        session.query = Mock()
        session.add = Mock()
        session.commit = Mock()
        session.rollback = Mock()
        session.close = Mock()
        return session
    
    @pytest.fixture
    def mock_cache_service(self):
        """模拟缓存服务"""
        cache = Mock()
        cache.get = Mock(return_value=None)
        cache.set = Mock(return_value=True)
        cache.delete = Mock(return_value=True)
        return cache
    
    @pytest.fixture
    def cost_service(self, mock_db_session, mock_cache_service):
        """创建成本服务实例"""
        service = CostService()
        service.db_session = mock_db_session
        service.cache_service = mock_cache_service
        return service
    
    @pytest.fixture
    def sample_usage_data(self):
        """示例使用数据"""
        return {
            "user_id": "user123",
            "session_id": "session456",
            "model_name": "gpt-4",
            "input_tokens": 1000,
            "output_tokens": 500,
            "total_tokens": 1500,
            "timestamp": datetime.now(),
            "request_type": "chat_completion"
        }
    
    @pytest.fixture
    def sample_pricing_data(self):
        """示例定价数据"""
        return {
            "gpt-4": {
                "input_price_per_1k": Decimal("0.03"),
                "output_price_per_1k": Decimal("0.06"),
                "currency": "USD"
            },
            "gpt-3.5-turbo": {
                "input_price_per_1k": Decimal("0.001"),
                "output_price_per_1k": Decimal("0.002"),
                "currency": "USD"
            }
        }
    
    def test_calculate_token_cost_success(self, cost_service: CostService, sample_pricing_data):
        """测试成功计算令牌成本"""
        model_name = "gpt-4"
        input_tokens = 1000
        output_tokens = 500
        
        # 模拟获取定价数据
        with patch.object(cost_service, 'get_model_pricing', return_value=sample_pricing_data[model_name]):
            cost = cost_service.calculate_token_cost(model_name, input_tokens, output_tokens)
        
        # 验证成本计算
        expected_cost = (Decimal("1000") / 1000 * Decimal("0.03")) + (Decimal("500") / 1000 * Decimal("0.06"))
        assert cost == expected_cost
    
    def test_calculate_token_cost_unknown_model(self, cost_service: CostService):
        """测试未知模型的成本计算"""
        model_name = "unknown-model"
        input_tokens = 1000
        output_tokens = 500
        
        # 模拟获取定价数据失败
        with patch.object(cost_service, 'get_model_pricing', return_value=None):
            cost = cost_service.calculate_token_cost(model_name, input_tokens, output_tokens)
        
        # 应该返回0
        assert cost == Decimal("0")
    
    def test_record_usage_success(self, cost_service: CostService, mock_db_session, sample_usage_data):
        """测试成功记录使用情况"""
        # 记录使用情况
        result = cost_service.record_usage(sample_usage_data)
        
        # 验证数据库操作
        mock_db_session.add.assert_called_once()
        mock_db_session.commit.assert_called_once()
        assert result is True
    
    def test_record_usage_database_error(self, cost_service: CostService, mock_db_session, sample_usage_data):
        """测试记录使用情况时数据库错误"""
        # 模拟数据库错误
        mock_db_session.commit.side_effect = Exception("Database error")
        
        # 记录使用情况
        result = cost_service.record_usage(sample_usage_data)
        
        # 验证错误处理
        mock_db_session.rollback.assert_called_once()
        assert result is False
    
    def test_get_user_cost_summary_success(self, cost_service: CostService, mock_db_session):
        """测试成功获取用户成本摘要"""
        user_id = "user123"
        start_date = datetime.now() - timedelta(days=30)
        end_date = datetime.now()
        
        # 模拟查询结果
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.group_by.return_value = mock_query
        mock_query.all.return_value = [
            ("gpt-4", 10000, 5000, Decimal("1.50")),
            ("gpt-3.5-turbo", 50000, 25000, Decimal("0.075"))
        ]
        mock_db_session.query.return_value = mock_query
        
        # 获取成本摘要
        summary = cost_service.get_user_cost_summary(user_id, start_date, end_date)
        
        # 验证结果
        assert len(summary) == 2
        assert summary[0]["model_name"] == "gpt-4"
        assert summary[0]["total_cost"] == Decimal("1.50")
    
    def test_get_session_cost_success(self, cost_service: CostService, mock_db_session):
        """测试成功获取会话成本"""
        session_id = "session456"
        
        # 模拟查询结果
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.scalar.return_value = Decimal("2.50")
        mock_db_session.query.return_value = mock_query
        
        # 获取会话成本
        cost = cost_service.get_session_cost(session_id)
        
        # 验证结果
        assert cost == Decimal("2.50")
    
    def test_get_model_pricing_from_cache(self, cost_service: CostService, mock_cache_service, sample_pricing_data):
        """测试从缓存获取模型定价"""
        model_name = "gpt-4"
        
        # 模拟缓存命中
        mock_cache_service.get.return_value = sample_pricing_data[model_name]
        
        # 获取定价
        pricing = cost_service.get_model_pricing(model_name)
        
        # 验证结果
        assert pricing == sample_pricing_data[model_name]
        mock_cache_service.get.assert_called_once_with(f"model_pricing:{model_name}")
    
    def test_get_model_pricing_from_database(self, cost_service: CostService, mock_cache_service, mock_db_session, sample_pricing_data):
        """测试从数据库获取模型定价"""
        model_name = "gpt-4"
        
        # 模拟缓存未命中
        mock_cache_service.get.return_value = None
        
        # 模拟数据库查询
        mock_pricing = Mock()
        mock_pricing.input_price_per_1k = sample_pricing_data[model_name]["input_price_per_1k"]
        mock_pricing.output_price_per_1k = sample_pricing_data[model_name]["output_price_per_1k"]
        mock_pricing.currency = sample_pricing_data[model_name]["currency"]
        
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.first.return_value = mock_pricing
        mock_db_session.query.return_value = mock_query
        
        # 获取定价
        pricing = cost_service.get_model_pricing(model_name)
        
        # 验证结果和缓存设置
        assert pricing["input_price_per_1k"] == sample_pricing_data[model_name]["input_price_per_1k"]
        mock_cache_service.set.assert_called_once()
    
    def test_update_model_pricing_success(self, cost_service: CostService, mock_db_session, mock_cache_service):
        """测试成功更新模型定价"""
        model_name = "gpt-4"
        pricing_data = {
            "input_price_per_1k": Decimal("0.035"),
            "output_price_per_1k": Decimal("0.065"),
            "currency": "USD"
        }
        
        # 更新定价
        result = cost_service.update_model_pricing(model_name, pricing_data)
        
        # 验证数据库操作和缓存清除
        mock_db_session.commit.assert_called_once()
        mock_cache_service.delete.assert_called_once_with(f"model_pricing:{model_name}")
        assert result is True
    
    def test_get_cost_analytics_success(self, cost_service: CostService, mock_db_session):
        """测试成功获取成本分析"""
        start_date = datetime.now() - timedelta(days=30)
        end_date = datetime.now()
        
        # 模拟查询结果
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.group_by.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.all.return_value = [
            (datetime.now().date(), Decimal("10.50")),
            (datetime.now().date() - timedelta(days=1), Decimal("8.75"))
        ]
        mock_db_session.query.return_value = mock_query
        
        # 获取成本分析
        analytics = cost_service.get_cost_analytics(start_date, end_date)
        
        # 验证结果
        assert len(analytics) == 2
        assert "date" in analytics[0]
        assert "total_cost" in analytics[0]
    
    def test_estimate_request_cost_success(self, cost_service: CostService, sample_pricing_data):
        """测试成功估算请求成本"""
        model_name = "gpt-4"
        estimated_tokens = 1500
        
        # 模拟获取定价数据
        with patch.object(cost_service, 'get_model_pricing', return_value=sample_pricing_data[model_name]):
            cost = cost_service.estimate_request_cost(model_name, estimated_tokens)
        
        # 验证估算成本（假设输入输出比例为2:1）
        input_tokens = estimated_tokens * 2 // 3
        output_tokens = estimated_tokens // 3
        expected_cost = (Decimal(str(input_tokens)) / 1000 * Decimal("0.03")) + (Decimal(str(output_tokens)) / 1000 * Decimal("0.06"))
        assert cost == expected_cost
    
    def test_get_top_cost_users_success(self, cost_service: CostService, mock_db_session):
        """测试成功获取成本最高用户"""
        start_date = datetime.now() - timedelta(days=30)
        end_date = datetime.now()
        limit = 10
        
        # 模拟查询结果
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.group_by.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.limit.return_value = mock_query
        mock_query.all.return_value = [
            ("user123", Decimal("50.00")),
            ("user456", Decimal("35.75"))
        ]
        mock_db_session.query.return_value = mock_query
        
        # 获取成本最高用户
        top_users = cost_service.get_top_cost_users(start_date, end_date, limit)
        
        # 验证结果
        assert len(top_users) == 2
        assert top_users[0]["user_id"] == "user123"
        assert top_users[0]["total_cost"] == Decimal("50.00")
    
    def test_export_cost_report_success(self, cost_service: CostService, mock_db_session):
        """测试成功导出成本报告"""
        start_date = datetime.now() - timedelta(days=30)
        end_date = datetime.now()
        
        # 模拟查询结果
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.all.return_value = [
            Mock(user_id="user123", model_name="gpt-4", total_cost=Decimal("10.50"), timestamp=datetime.now()),
            Mock(user_id="user456", model_name="gpt-3.5-turbo", total_cost=Decimal("2.25"), timestamp=datetime.now())
        ]
        mock_db_session.query.return_value = mock_query
        
        # 导出报告
        report = cost_service.export_cost_report(start_date, end_date)
        
        # 验证结果
        assert "summary" in report
        assert "details" in report
        assert len(report["details"]) == 2
    
    def test_set_cost_alert_success(self, cost_service: CostService, mock_db_session):
        """测试成功设置成本警报"""
        user_id = "user123"
        threshold = Decimal("100.00")
        period = "monthly"
        
        # 设置警报
        result = cost_service.set_cost_alert(user_id, threshold, period)
        
        # 验证数据库操作
        mock_db_session.add.assert_called_once()
        mock_db_session.commit.assert_called_once()
        assert result is True
    
    def test_check_cost_alerts_success(self, cost_service: CostService, mock_db_session):
        """测试成功检查成本警报"""
        # 模拟警报查询
        mock_alert = Mock()
        mock_alert.user_id = "user123"
        mock_alert.threshold = Decimal("100.00")
        mock_alert.period = "monthly"
        
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.all.return_value = [mock_alert]
        mock_db_session.query.return_value = mock_query
        
        # 模拟用户成本查询
        with patch.object(cost_service, 'get_user_monthly_cost', return_value=Decimal("120.00")):
            alerts = cost_service.check_cost_alerts()
        
        # 验证结果
        assert len(alerts) == 1
        assert alerts[0]["user_id"] == "user123"
        assert alerts[0]["exceeded"] is True


@pytest.mark.service
@pytest.mark.unit
class TestCostServiceEdgeCases(MockTestCase):
    """成本服务边界情况测试类"""
    
    @pytest.fixture
    def mock_db_session(self):
        """模拟数据库会话"""
        session = Mock()
        return session
    
    @pytest.fixture
    def mock_cache_service(self):
        """模拟缓存服务"""
        cache = Mock()
        return cache
    
    @pytest.fixture
    def cost_service(self, mock_db_session, mock_cache_service):
        """创建成本服务实例"""
        service = CostService()
        service.db_session = mock_db_session
        service.cache_service = mock_cache_service
        return service
    
    @pytest.fixture
    def memory_monitor(self):
        """内存监控器"""
        return self.create_memory_monitor()
    
    @pytest.fixture
    def performance_timer(self):
        """性能计时器"""
        return self.create_performance_timer()
    
    def test_zero_tokens_cost_calculation(self, cost_service: CostService):
        """测试零令牌成本计算"""
        model_name = "gpt-4"
        
        # 模拟获取定价数据
        pricing_data = {
            "input_price_per_1k": Decimal("0.03"),
            "output_price_per_1k": Decimal("0.06"),
            "currency": "USD"
        }
        
        with patch.object(cost_service, 'get_model_pricing', return_value=pricing_data):
            cost = cost_service.calculate_token_cost(model_name, 0, 0)
        
        # 验证零成本
        assert cost == Decimal("0")
    
    def test_negative_tokens_handling(self, cost_service: CostService):
        """测试负数令牌处理"""
        model_name = "gpt-4"
        
        # 模拟获取定价数据
        pricing_data = {
            "input_price_per_1k": Decimal("0.03"),
            "output_price_per_1k": Decimal("0.06"),
            "currency": "USD"
        }
        
        with patch.object(cost_service, 'get_model_pricing', return_value=pricing_data):
            cost = cost_service.calculate_token_cost(model_name, -100, -50)
        
        # 负数令牌应该被处理为0
        assert cost == Decimal("0")
    
    def test_very_large_token_count(self, cost_service: CostService):
        """测试超大令牌数量"""
        model_name = "gpt-4"
        large_tokens = 10**9  # 10亿令牌
        
        # 模拟获取定价数据
        pricing_data = {
            "input_price_per_1k": Decimal("0.03"),
            "output_price_per_1k": Decimal("0.06"),
            "currency": "USD"
        }
        
        with patch.object(cost_service, 'get_model_pricing', return_value=pricing_data):
            cost = cost_service.calculate_token_cost(model_name, large_tokens, large_tokens)
        
        # 验证大数计算精度
        expected_cost = (Decimal(str(large_tokens)) / 1000 * Decimal("0.03")) + (Decimal(str(large_tokens)) / 1000 * Decimal("0.06"))
        assert cost == expected_cost
    
    def test_precision_handling(self, cost_service: CostService):
        """测试精度处理"""
        model_name = "gpt-4"
        
        # 使用高精度定价
        pricing_data = {
            "input_price_per_1k": Decimal("0.0000123456789"),
            "output_price_per_1k": Decimal("0.0000987654321"),
            "currency": "USD"
        }
        
        with patch.object(cost_service, 'get_model_pricing', return_value=pricing_data):
            cost = cost_service.calculate_token_cost(model_name, 1, 1)
        
        # 验证精度保持
        expected_cost = (Decimal("1") / 1000 * Decimal("0.0000123456789")) + (Decimal("1") / 1000 * Decimal("0.0000987654321"))
        assert cost == expected_cost
    
    def test_concurrent_cost_calculations(self, cost_service: CostService):
        """测试并发成本计算"""
        import threading

        results = []
        errors = []
        
        def calculate_cost(index):
            try:
                # 模拟获取定价数据
                pricing_data = {
                    "input_price_per_1k": Decimal("0.03"),
                    "output_price_per_1k": Decimal("0.06"),
                    "currency": "USD"
                }
                
                with patch.object(cost_service, 'get_model_pricing', return_value=pricing_data):
                    cost = cost_service.calculate_token_cost("gpt-4", 1000 + index, 500 + index)
                    results.append(cost)
            except Exception as e:
                errors.append(e)
        
        # 创建多个线程同时计算成本
        threads = []
        for i in range(10):
            thread = threading.Thread(target=calculate_cost, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证没有错误且结果正确
        assert len(errors) == 0
        assert len(results) == 10
        assert all(isinstance(cost, Decimal) for cost in results)
    
    def test_database_connection_failure(self, cost_service: CostService, mock_db_session, sample_usage_data):
        """测试数据库连接失败"""
        # 模拟数据库连接失败
        mock_db_session.add.side_effect = Exception("Database connection failed")
        
        # 记录使用情况
        result = cost_service.record_usage(sample_usage_data)
        
        # 验证错误处理
        assert result is False
        mock_db_session.rollback.assert_called_once()
    
    def test_cache_service_failure(self, cost_service: CostService, mock_cache_service):
        """测试缓存服务失败"""
        model_name = "gpt-4"
        
        # 模拟缓存服务失败
        mock_cache_service.get.side_effect = Exception("Cache service failed")
        
        # 模拟数据库查询成功
        mock_pricing = Mock()
        mock_pricing.input_price_per_1k = Decimal("0.03")
        mock_pricing.output_price_per_1k = Decimal("0.06")
        mock_pricing.currency = "USD"
        
        with patch.object(cost_service, '_get_pricing_from_db', return_value=mock_pricing):
            pricing = cost_service.get_model_pricing(model_name)
        
        # 应该回退到数据库查询
        assert pricing is not None
        assert pricing["input_price_per_1k"] == Decimal("0.03")
    
    def test_memory_usage_large_dataset(self, cost_service: CostService, memory_monitor):
        """测试大数据集内存使用"""
        with memory_monitor:
            # 创建大量使用数据
            usage_data_list = []
            for i in range(10000):
                usage_data = {
                    "user_id": f"user{i}",
                    "session_id": f"session{i}",
                    "model_name": "gpt-4",
                    "input_tokens": 1000 + i,
                    "output_tokens": 500 + i,
                    "total_tokens": 1500 + i,
                    "timestamp": datetime.now(),
                    "request_type": "chat_completion"
                }
                usage_data_list.append(usage_data)
        
        # 验证内存使用在合理范围内
        memory_usage = memory_monitor.get_peak_memory()
        assert memory_usage < 100 * 1024 * 1024  # 小于100MB
    
    def test_performance_cost_calculation_benchmark(self, cost_service: CostService, performance_timer):
        """测试成本计算性能基准"""
        # 模拟获取定价数据
        pricing_data = {
            "input_price_per_1k": Decimal("0.03"),
            "output_price_per_1k": Decimal("0.06"),
            "currency": "USD"
        }
        
        with performance_timer:
            with patch.object(cost_service, 'get_model_pricing', return_value=pricing_data):
                # 执行大量成本计算
                for i in range(10000):
                    cost_service.calculate_token_cost("gpt-4", 1000 + i, 500 + i)
        
        # 验证性能在可接受范围内
        elapsed_time = performance_timer.elapsed
        assert elapsed_time < 1.0  # 10000次计算应该在1秒内完成
    
    def test_currency_conversion_edge_cases(self, cost_service: CostService):
        """测试货币转换边界情况"""
        # 测试不同货币
        pricing_data_eur = {
            "input_price_per_1k": Decimal("0.025"),
            "output_price_per_1k": Decimal("0.055"),
            "currency": "EUR"
        }
        
        with patch.object(cost_service, 'get_model_pricing', return_value=pricing_data_eur):
            cost = cost_service.calculate_token_cost("gpt-4", 1000, 500)
        
        # 验证不同货币的成本计算
        expected_cost = (Decimal("1000") / 1000 * Decimal("0.025")) + (Decimal("500") / 1000 * Decimal("0.055"))
        assert cost == expected_cost
    
    def test_cost_aggregation_accuracy(self, cost_service: CostService, mock_db_session):
        """测试成本聚合精度"""
        user_id = "user123"
        start_date = datetime.now() - timedelta(days=30)
        end_date = datetime.now()
        
        # 模拟大量小额成本记录
        small_costs = [Decimal("0.000001") for _ in range(1000000)]  # 100万条小额记录
        
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.scalar.return_value = sum(small_costs)
        mock_db_session.query.return_value = mock_query
        
        # 获取用户成本摘要
        total_cost = cost_service.get_user_total_cost(user_id, start_date, end_date)
        
        # 验证聚合精度
        expected_total = sum(small_costs)
        assert total_cost == expected_total
    
    def test_cost_report_data_integrity(self, cost_service: CostService, mock_db_session):
        """测试成本报告数据完整性"""
        start_date = datetime.now() - timedelta(days=30)
        end_date = datetime.now()
        
        # 模拟包含特殊字符的数据
        mock_record = Mock()
        mock_record.user_id = "user@#$%^&*()123"
        mock_record.model_name = "gpt-4-with-special-chars"
        mock_record.total_cost = Decimal("10.50")
        mock_record.timestamp = datetime.now()
        
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.all.return_value = [mock_record]
        mock_db_session.query.return_value = mock_query
        
        # 导出报告
        report = cost_service.export_cost_report(start_date, end_date)
        
        # 验证数据完整性
        assert len(report["details"]) == 1
        assert report["details"][0]["user_id"] == "user@#$%^&*()123"
        assert report["details"][0]["model_name"] == "gpt-4-with-special-chars"