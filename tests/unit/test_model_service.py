"""模型服务单元测试"""

from uuid import uuid4

import pytest
from schemas.model import ModelCreate, ModelUpdate
from sqlalchemy.orm import Session

from api.exceptions.handlers import NotFoundAPIError, ValidationAPIError
from database.models import ModelModel, APIKeyModel
from services.model_service import ModelService
from tests.utils.test_helpers import MockTestCase


@pytest.mark.service
@pytest.mark.unit
class TestModelService(MockTestCase):
    """模型服务测试类"""
    
    @pytest.fixture
    def model_service(self, test_db_session: Session):
        """创建模型服务实例"""
        return ModelService(test_db_session)
    
    @pytest.fixture
    def sample_model_create(self):
        """示例模型创建数据"""
        return ModelCreate(
            name="gpt-3.5-turbo",
            provider="openai",
            model_type="chat",
            description="OpenAI GPT-3.5 Turbo模型",
            max_tokens=4096,
            cost_per_token=0.002,
            is_active=True
        )
    
    @pytest.fixture
    def sample_model_update(self):
        """示例模型更新数据"""
        return ModelUpdate(
            description="更新后的模型描述",
            max_tokens=8192,
            cost_per_token=0.001,
            is_active=False
        )
    
    @pytest.fixture
    def sample_api_key_data(self):
        """示例API密钥数据"""
        return {
            "provider": "openai",
            "key_value": "sk-test123456789",
            "is_active": True,
            "description": "测试API密钥"
        }
    
    def test_create_model_success(self, model_service: ModelService, sample_model_create: ModelCreate):
        """测试成功创建模型"""
        model = model_service.create_model(sample_model_create)
        
        assert model.id is not None
        assert model.name == sample_model_create.name
        assert model.provider == sample_model_create.provider
        assert model.model_type == sample_model_create.model_type
        assert model.description == sample_model_create.description
        assert model.max_tokens == sample_model_create.max_tokens
        assert model.cost_per_token == sample_model_create.cost_per_token
        assert model.is_active == sample_model_create.is_active
        assert model.created_at is not None
        assert model.updated_at is not None
    
    def test_create_model_duplicate_name(self, model_service: ModelService, 
                                        sample_model_create: ModelCreate, test_db_session: Session):
        """测试创建重复名称的模型"""
        # 先创建一个模型
        existing_model = ModelModel(
            name=sample_model_create.name,
            provider="openai",
            model_type="chat",
            description="已存在的模型",
            max_tokens=4096,
            cost_per_token=0.002
        )
        test_db_session.add(existing_model)
        test_db_session.commit()
        
        # 尝试创建同名模型
        with pytest.raises(ValidationAPIError) as exc_info:
            model_service.create_model(sample_model_create)
        
        assert "已存在" in str(exc_info.value)
    
    def test_create_model_invalid_provider(self, model_service: ModelService):
        """测试创建无效提供商的模型"""
        model_create = ModelCreate(
            name="test-model",
            provider="invalid_provider",  # 无效提供商
            model_type="chat",
            description="测试模型",
            max_tokens=4096,
            cost_per_token=0.002
        )
        
        with pytest.raises(ValidationAPIError) as exc_info:
            model_service.create_model(model_create)
        
        assert "提供商" in str(exc_info.value)
    
    def test_get_model_by_id_success(self, model_service: ModelService, test_db_session: Session):
        """测试成功根据ID获取模型"""
        # 创建测试模型
        model = ModelModel(
            name="test-model",
            provider="openai",
            model_type="chat",
            description="测试模型",
            max_tokens=4096,
            cost_per_token=0.002
        )
        test_db_session.add(model)
        test_db_session.commit()
        test_db_session.refresh(model)
        
        # 获取模型
        retrieved_model = model_service.get_model_by_id(model.id)
        
        assert retrieved_model.id == model.id
        assert retrieved_model.name == model.name
        assert retrieved_model.provider == model.provider
    
    def test_get_model_by_id_not_found(self, model_service: ModelService):
        """测试获取不存在的模型"""
        non_existent_id = uuid4()
        
        with pytest.raises(NotFoundAPIError) as exc_info:
            model_service.get_model_by_id(non_existent_id)
        
        assert "模型" in str(exc_info.value) and "未找到" in str(exc_info.value)
    
    def test_get_model_by_name_success(self, model_service: ModelService, test_db_session: Session):
        """测试成功根据名称获取模型"""
        # 创建测试模型
        model = ModelModel(
            name="gpt-3.5-turbo",
            provider="openai",
            model_type="chat",
            description="测试模型",
            max_tokens=4096,
            cost_per_token=0.002
        )
        test_db_session.add(model)
        test_db_session.commit()
        
        # 根据名称获取模型
        retrieved_model = model_service.get_model_by_name("gpt-3.5-turbo")
        
        assert retrieved_model.name == "gpt-3.5-turbo"
        assert retrieved_model.provider == "openai"
    
    def test_get_model_by_name_not_found(self, model_service: ModelService):
        """测试获取不存在名称的模型"""
        with pytest.raises(NotFoundAPIError) as exc_info:
            model_service.get_model_by_name("non-existent-model")
        
        assert "模型" in str(exc_info.value) and "未找到" in str(exc_info.value)
    
    def test_get_models_list(self, model_service: ModelService, test_db_session: Session):
        """测试获取模型列表"""
        # 创建多个测试模型
        models = [
            ModelModel(
                name=f"model-{i}",
                provider="openai",
                model_type="chat",
                description=f"模型{i}",
                max_tokens=4096,
                cost_per_token=0.002
            )
            for i in range(5)
        ]
        
        for model in models:
            test_db_session.add(model)
        test_db_session.commit()
        
        # 获取模型列表
        result = model_service.get_models(page=1, size=10)
        
        assert result["total"] >= 5
        assert len(result["models"]) >= 5
        assert result["page"] == 1
        assert result["size"] == 10
    
    def test_get_models_by_provider(self, model_service: ModelService, test_db_session: Session):
        """测试按提供商获取模型"""
        # 创建不同提供商的模型
        models = [
            ModelModel(
                name="gpt-3.5-turbo",
                provider="openai",
                model_type="chat",
                description="OpenAI模型",
                max_tokens=4096,
                cost_per_token=0.002
            ),
            ModelModel(
                name="claude-3-sonnet",
                provider="anthropic",
                model_type="chat",
                description="Anthropic模型",
                max_tokens=4096,
                cost_per_token=0.003
            )
        ]
        
        for model in models:
            test_db_session.add(model)
        test_db_session.commit()
        
        # 按提供商过滤
        openai_models = model_service.get_models_by_provider("openai")
        
        assert len(openai_models["models"]) >= 1
        assert all(m.provider == "openai" for m in openai_models["models"])
    
    def test_get_active_models(self, model_service: ModelService, test_db_session: Session):
        """测试获取活跃模型"""
        # 创建活跃和非活跃模型
        models = [
            ModelModel(
                name="active-model",
                provider="openai",
                model_type="chat",
                description="活跃模型",
                max_tokens=4096,
                cost_per_token=0.002,
                is_active=True
            ),
            ModelModel(
                name="inactive-model",
                provider="openai",
                model_type="chat",
                description="非活跃模型",
                max_tokens=4096,
                cost_per_token=0.002,
                is_active=False
            )
        ]
        
        for model in models:
            test_db_session.add(model)
        test_db_session.commit()
        
        # 获取活跃模型
        active_models = model_service.get_active_models()
        
        assert len(active_models["models"]) >= 1
        assert all(m.is_active for m in active_models["models"])
    
    def test_update_model_success(self, model_service: ModelService, 
                                sample_model_update: ModelUpdate, test_db_session: Session):
        """测试成功更新模型"""
        # 创建测试模型
        model = ModelModel(
            name="test-model",
            provider="openai",
            model_type="chat",
            description="原始描述",
            max_tokens=4096,
            cost_per_token=0.002
        )
        test_db_session.add(model)
        test_db_session.commit()
        test_db_session.refresh(model)
        
        # 更新模型
        updated_model = model_service.update_model(model.id, sample_model_update)
        
        assert updated_model.description == sample_model_update.description
        assert updated_model.max_tokens == sample_model_update.max_tokens
        assert updated_model.cost_per_token == sample_model_update.cost_per_token
        assert updated_model.is_active == sample_model_update.is_active
        assert updated_model.updated_at > updated_model.created_at
    
    def test_update_model_not_found(self, model_service: ModelService, sample_model_update: ModelUpdate):
        """测试更新不存在的模型"""
        non_existent_id = uuid4()
        
        with pytest.raises(NotFoundAPIError) as exc_info:
            model_service.update_model(non_existent_id, sample_model_update)
        
        assert "模型" in str(exc_info.value) and "未找到" in str(exc_info.value)
    
    def test_delete_model_success(self, model_service: ModelService, test_db_session: Session):
        """测试成功删除模型"""
        # 创建测试模型
        model = ModelModel(
            name="to-delete-model",
            provider="openai",
            model_type="chat",
            description="待删除模型",
            max_tokens=4096,
            cost_per_token=0.002
        )
        test_db_session.add(model)
        test_db_session.commit()
        test_db_session.refresh(model)
        
        # 删除模型
        model_service.delete_model(model.id)
        
        # 验证模型已被删除
        with pytest.raises(NotFoundAPIError):
            model_service.get_model_by_id(model.id)
    
    def test_delete_model_not_found(self, model_service: ModelService):
        """测试删除不存在的模型"""
        non_existent_id = uuid4()
        
        with pytest.raises(NotFoundAPIError) as exc_info:
            model_service.delete_model(non_existent_id)
        
        assert "模型" in str(exc_info.value) and "未找到" in str(exc_info.value)
    
    def test_toggle_model_status(self, model_service: ModelService, test_db_session: Session):
        """测试切换模型状态"""
        # 创建测试模型
        model = ModelModel(
            name="toggle-model",
            provider="openai",
            model_type="chat",
            description="切换状态模型",
            max_tokens=4096,
            cost_per_token=0.002,
            is_active=True
        )
        test_db_session.add(model)
        test_db_session.commit()
        test_db_session.refresh(model)
        
        # 切换状态
        updated_model = model_service.toggle_model_status(model.id)
        
        assert updated_model.is_active is False
        
        # 再次切换
        updated_model = model_service.toggle_model_status(model.id)
        
        assert updated_model.is_active is True
    
    def test_get_model_statistics(self, model_service: ModelService, test_db_session: Session):
        """测试获取模型统计信息"""
        from database.models import Conversation, Session
        
        # 创建测试模型
        model = ModelModel(
            name="stats-model",
            provider="openai",
            model_type="chat",
            description="统计模型",
            max_tokens=4096,
            cost_per_token=0.002
        )
        test_db_session.add(model)
        test_db_session.commit()
        test_db_session.refresh(model)
        
        # 创建相关数据
        session = Session(name="测试会话", description="描述", models=["stats-model"])
        test_db_session.add(session)
        test_db_session.commit()
        test_db_session.refresh(session)
        
        conversation = Conversation(title="测试对话", session_id=session.id)
        test_db_session.add(conversation)
        test_db_session.commit()
        test_db_session.refresh(conversation)
        
        # 注释掉消息创建，因为数据库模型中已经移除了Message模型
        # messages = [
        #     Message(
        #         content="测试消息1",
        #         role="assistant",
        #         conversation_id=conversation.id,
        #         model="stats-model",
        #         token_count=50
        #     ),
        #     Message(
        #         content="测试消息2",
        #         role="assistant",
        #         conversation_id=conversation.id,
        #         model="stats-model",
        #         token_count=75
        #     )
        # ]
        messages = []
        
        for message in messages:
            test_db_session.add(message)
        test_db_session.commit()
        
        # 获取统计信息
        stats = model_service.get_model_statistics(model.id)
        
        assert stats["model_id"] == str(model.id)
        assert stats["total_messages"] >= 2
        assert stats["total_tokens"] >= 125
        assert "usage_count" in stats
        assert "created_at" in stats
    
    def test_validate_provider(self, model_service: ModelService):
        """测试提供商验证"""
        # 测试有效提供商
        valid_providers = ["openai", "anthropic", "google", "baidu"]
        for provider in valid_providers:
            assert model_service._validate_provider(provider) is True
        
        # 测试无效提供商
        invalid_providers = ["invalid", "", None]
        for provider in invalid_providers:
            assert model_service._validate_provider(provider) is False
    
    def test_validate_model_type(self, model_service: ModelService):
        """测试模型类型验证"""
        # 测试有效类型
        valid_types = ["chat", "completion", "embedding"]
        for model_type in valid_types:
            assert model_service._validate_model_type(model_type) is True
        
        # 测试无效类型
        invalid_types = ["invalid", "", None]
        for model_type in invalid_types:
            assert model_service._validate_model_type(model_type) is False
    
    def test_calculate_cost(self, model_service: ModelService, test_db_session: Session):
        """测试成本计算"""
        # 创建测试模型
        model = ModelModel(
            name="cost-model",
            provider="openai",
            model_type="chat",
            description="成本计算模型",
            max_tokens=4096,
            cost_per_token=0.002
        )
        test_db_session.add(model)
        test_db_session.commit()
        test_db_session.refresh(model)
        
        # 计算成本
        token_count = 1000
        cost = model_service.calculate_cost(model.id, token_count)
        
        expected_cost = token_count * model.cost_per_token
        assert cost == expected_cost
    
    def test_search_models(self, model_service: ModelService, test_db_session: Session):
        """测试搜索模型"""
        # 创建测试模型
        models = [
            ModelModel(
                name="gpt-3.5-turbo",
                provider="openai",
                model_type="chat",
                description="OpenAI GPT模型",
                max_tokens=4096,
                cost_per_token=0.002
            ),
            ModelModel(
                name="claude-3-sonnet",
                provider="anthropic",
                model_type="chat",
                description="Anthropic Claude模型",
                max_tokens=4096,
                cost_per_token=0.003
            )
        ]
        
        for model in models:
            test_db_session.add(model)
        test_db_session.commit()
        
        # 搜索包含"GPT"的模型
        result = model_service.search_models("GPT")
        
        assert len(result["models"]) >= 1
        assert any("gpt" in m.name.lower() or "gpt" in m.description.lower() for m in result["models"])
    
    def test_batch_update_models(self, model_service: ModelService, test_db_session: Session):
        """测试批量更新模型"""
        # 创建多个测试模型
        models = [
            ModelModel(
                name=f"batch-model-{i}",
                provider="openai",
                model_type="chat",
                description=f"批量模型{i}",
                max_tokens=4096,
                cost_per_token=0.002,
                is_active=True
            )
            for i in range(3)
        ]
        
        for model in models:
            test_db_session.add(model)
        test_db_session.commit()
        
        model_ids = [model.id for model in models]
        
        # 批量更新
        update_data = {"is_active": False, "cost_per_token": 0.001}
        updated_models = model_service.batch_update_models(model_ids, update_data)
        
        assert len(updated_models) == 3
        assert all(not m.is_active for m in updated_models)
        assert all(m.cost_per_token == 0.001 for m in updated_models)


@pytest.mark.service
@pytest.mark.unit
class TestModelServiceEdgeCases(MockTestCase):
    """模型服务边界情况测试类"""
    
    @pytest.fixture
    def model_service(self, test_db_session: Session):
        """创建模型服务实例"""
        return ModelService(test_db_session)
    
    def test_create_model_with_special_characters(self, model_service: ModelService):
        """测试创建包含特殊字符的模型"""
        model_create = ModelCreate(
            name="model-with-special-chars-@#$",
            provider="openai",
            model_type="chat",
            description="包含特殊字符的模型描述：@#$%^&*()",
            max_tokens=4096,
            cost_per_token=0.002
        )
        
        model = model_service.create_model(model_create)
        
        assert model.name == model_create.name
        assert model.description == model_create.description
    
    def test_model_with_zero_cost(self, model_service: ModelService):
        """测试零成本模型"""
        model_create = ModelCreate(
            name="free-model",
            provider="openai",
            model_type="chat",
            description="免费模型",
            max_tokens=4096,
            cost_per_token=0.0  # 零成本
        )
        
        model = model_service.create_model(model_create)
        
        assert model.cost_per_token == 0.0
        
        # 测试成本计算
        cost = model_service.calculate_cost(model.id, 1000)
        assert cost == 0.0
    
    def test_model_with_very_high_token_limit(self, model_service: ModelService):
        """测试极高令牌限制的模型"""
        model_create = ModelCreate(
            name="high-token-model",
            provider="openai",
            model_type="chat",
            description="高令牌限制模型",
            max_tokens=1000000,  # 极高令牌限制
            cost_per_token=0.002
        )
        
        model = model_service.create_model(model_create)
        
        assert model.max_tokens == 1000000
    
    def test_get_models_empty_database(self, model_service: ModelService):
        """测试空数据库获取模型"""
        result = model_service.get_models(page=1, size=10)
        
        assert result["total"] == 0
        assert len(result["models"]) == 0
        assert result["page"] == 1
        assert result["size"] == 10
    
    def test_search_models_no_results(self, model_service: ModelService):
        """测试搜索无结果"""
        result = model_service.search_models("nonexistent-model")
        
        assert result["total"] == 0
        assert len(result["models"]) == 0
    
    def test_batch_update_empty_list(self, model_service: ModelService):
        """测试批量更新空列表"""
        updated_models = model_service.batch_update_models([], {"is_active": False})
        
        assert len(updated_models) == 0
    
    def test_model_name_case_sensitivity(self, model_service: ModelService, test_db_session: Session):
        """测试模型名称大小写敏感性"""
        # 创建小写名称模型
        model1 = ModelModel(
            name="test-model",
            provider="openai",
            model_type="chat",
            description="小写模型",
            max_tokens=4096,
            cost_per_token=0.002
        )
        test_db_session.add(model1)
        test_db_session.commit()
        
        # 尝试创建大写名称模型（应该被允许）
        model_create = ModelCreate(
            name="TEST-MODEL",
            provider="openai",
            model_type="chat",
            description="大写模型",
            max_tokens=4096,
            cost_per_token=0.002
        )
        
        model2 = model_service.create_model(model_create)
        
        assert model2.name == "TEST-MODEL"
        assert model1.name != model2.name
    
    def test_concurrent_model_creation(self, model_service: ModelService):
        """测试并发模型创建"""
        import threading

        results = []
        errors = []
        
        def create_model(index):
            try:
                model_create = ModelCreate(
                    name=f"concurrent-model-{index}",
                    provider="openai",
                    model_type="chat",
                    description=f"并发模型{index}",
                    max_tokens=4096,
                    cost_per_token=0.002
                )
                model = model_service.create_model(model_create)
                results.append(model)
            except Exception as e:
                errors.append(e)
        
        # 创建多个线程同时创建模型
        threads = []
        for i in range(5):
            thread = threading.Thread(target=create_model, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证结果
        assert len(results) == 5
        assert len(errors) == 0
        
        # 验证所有模型都有唯一名称
        names = [model.name for model in results]
        assert len(set(names)) == 5