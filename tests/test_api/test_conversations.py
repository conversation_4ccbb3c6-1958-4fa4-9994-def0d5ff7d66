"""对话API测试"""

from unittest.mock import Mock, patch

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from database.models import Conversation
from tests.utils.base_test import BaseAPITestCase, TestDataFactory


@pytest.mark.api
@pytest.mark.unit
class TestConversationsAPI(BaseAPITestCase):
    """对话API测试类"""
    
    def test_create_conversation_success(self, test_client: TestClient, test_db_session: Session):
        """测试成功创建对话"""
        # 使用基础测试类方法创建会话
        session = self.create_test_session(test_db_session)

        # 使用工厂方法获取测试数据
        conversation_data = {
            **TestDataFactory.get_sample_conversation_data(),
            "session_id": str(session.id)
        }
        auth_headers = TestDataFactory.get_auth_headers()
        
        response = test_client.post(
            "/api/conversations/",
            json=conversation_data,
            headers=auth_headers
        )
        
        assert response.status_code == 201
        data = response.json()
        assert "id" in data
        assert data["title"] == conversation_data["title"]
        assert data["session_id"] == conversation_data["session_id"]
        assert "created_at" in data
        assert "updated_at" in data
    
    def test_create_conversation_invalid_session(self, test_client: TestClient, auth_headers: dict,
                                                sample_conversation_data: dict):
        """测试使用无效会话ID创建对话"""
        conversation_data = {
            **sample_conversation_data,
            "session_id": "non-existent-session-id"
        }
        
        response = test_client.post(
            "/api/conversations/",
            json=conversation_data,
            headers=auth_headers
        )
        
        assert response.status_code == 404
        data = response.json()
        assert "error" in data
    
    def test_create_conversation_missing_title(self, test_client: TestClient, auth_headers: dict,
                                              test_db_session: Session):
        """测试创建对话时缺少标题"""
        # 先创建一个会话
        session = Session(
            name="测试会话",
            description="测试描述",
            models=["gpt-3.5-turbo"]
        )
        test_db_session.add(session)
        test_db_session.commit()
        test_db_session.refresh(session)
        
        conversation_data = {
            "session_id": str(session.id)
            # 缺少title
        }
        
        response = test_client.post(
            "/api/conversations/",
            json=conversation_data,
            headers=auth_headers
        )
        
        assert response.status_code == 422
    
    def test_get_conversations_list(self, test_client: TestClient, auth_headers: dict, test_db_session: Session):
        """测试获取对话列表"""
        # 创建测试会话
        session = Session(
            name="测试会话",
            description="测试描述",
            models=["gpt-3.5-turbo"]
        )
        test_db_session.add(session)
        test_db_session.commit()
        test_db_session.refresh(session)
        
        # 创建测试对话
        conversation1 = Conversation(
            title="对话1",
            session_id=session.id
        )
        conversation2 = Conversation(
            title="对话2",
            session_id=session.id
        )
        
        test_db_session.add(conversation1)
        test_db_session.add(conversation2)
        test_db_session.commit()
        
        response = test_client.get(
            "/api/conversations/",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "conversations" in data
        assert len(data["conversations"]) >= 2
        assert "total" in data
        assert "page" in data
        assert "size" in data
    
    def test_get_conversation_by_id(self, test_client: TestClient, auth_headers: dict, test_db_session: Session):
        """测试根据ID获取对话"""
        # 创建测试会话和对话
        session = Session(
            name="测试会话",
            description="测试描述",
            models=["gpt-3.5-turbo"]
        )
        test_db_session.add(session)
        test_db_session.commit()
        test_db_session.refresh(session)
        
        conversation = Conversation(
            title="测试对话",
            session_id=session.id
        )
        test_db_session.add(conversation)
        test_db_session.commit()
        test_db_session.refresh(conversation)
        
        response = test_client.get(
            f"/api/conversations/{conversation.id}",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == str(conversation.id)
        assert data["title"] == conversation.title
        assert data["session_id"] == str(conversation.session_id)
    
    def test_get_conversation_not_found(self, test_client: TestClient, auth_headers: dict):
        """测试获取不存在的对话"""
        response = test_client.get(
            "/api/conversations/non-existent-id",
            headers=auth_headers
        )
        
        assert response.status_code == 404
        data = response.json()
        assert "error" in data
    
    @patch('api.routers.conversations.ModelManager')
    @patch('api.routers.conversations.ComparisonEngine')
    def test_chat_endpoint_success(self, mock_comparison_engine, mock_model_manager,
                                 test_client: TestClient, auth_headers: dict, test_db_session: Session):
        """测试聊天端点成功响应"""
        # 创建测试会话和对话
        session = Session(
            name="测试会话",
            description="测试描述",
            models=["gpt-3.5-turbo", "claude-3-sonnet"]
        )
        test_db_session.add(session)
        test_db_session.commit()
        test_db_session.refresh(session)
        
        conversation = Conversation(
            title="测试对话",
            session_id=session.id
        )
        test_db_session.add(conversation)
        test_db_session.commit()
        test_db_session.refresh(conversation)
        
        # 模拟模型响应
        mock_model_manager_instance = Mock()
        mock_model_manager.return_value = mock_model_manager_instance
        mock_model_manager_instance.generate_response.return_value = {
            "gpt-3.5-turbo": {
                "content": "这是GPT的回复",
                "usage": {"prompt_tokens": 10, "completion_tokens": 20, "total_tokens": 30},
                "model": "gpt-3.5-turbo",
                "finish_reason": "stop"
            },
            "claude-3-sonnet": {
                "content": "这是Claude的回复",
                "usage": {"prompt_tokens": 12, "completion_tokens": 25, "total_tokens": 37},
                "model": "claude-3-sonnet",
                "finish_reason": "stop"
            }
        }
        
        # 模拟比较引擎
        mock_comparison_engine_instance = Mock()
        mock_comparison_engine.return_value = mock_comparison_engine_instance
        mock_comparison_engine_instance.compare_responses.return_value = {
            "similarity_score": 0.85,
            "quality_scores": {
                "gpt-3.5-turbo": 0.88,
                "claude-3-sonnet": 0.92
            },
            "analysis": "两个模型的回复质量都很高"
        }
        
        chat_data = {
            "message": "你好，请介绍一下人工智能",
            "conversation_id": str(conversation.id)
        }
        
        response = test_client.post(
            "/api/conversations/chat",
            json=chat_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "responses" in data
        assert "comparison" in data
        assert "conversation_id" in data
        assert len(data["responses"]) == 2
        assert "gpt-3.5-turbo" in data["responses"]
        assert "claude-3-sonnet" in data["responses"]
    
    def test_chat_endpoint_invalid_conversation(self, test_client: TestClient, auth_headers: dict):
        """测试使用无效对话ID的聊天端点"""
        chat_data = {
            "message": "你好",
            "conversation_id": "non-existent-conversation-id"
        }
        
        response = test_client.post(
            "/api/conversations/chat",
            json=chat_data,
            headers=auth_headers
        )
        
        assert response.status_code == 404
        data = response.json()
        assert "error" in data
    
    def test_chat_endpoint_missing_message(self, test_client: TestClient, auth_headers: dict, test_db_session: Session):
        """测试聊天端点缺少消息"""
        # 创建测试会话和对话
        session = Session(
            name="测试会话",
            description="测试描述",
            models=["gpt-3.5-turbo"]
        )
        test_db_session.add(session)
        test_db_session.commit()
        test_db_session.refresh(session)
        
        conversation = Conversation(
            title="测试对话",
            session_id=session.id
        )
        test_db_session.add(conversation)
        test_db_session.commit()
        test_db_session.refresh(conversation)
        
        chat_data = {
            "conversation_id": str(conversation.id)
            # 缺少message
        }
        
        response = test_client.post(
            "/api/conversations/chat",
            json=chat_data,
            headers=auth_headers
        )
        
        assert response.status_code == 422
    
    def test_get_conversation_messages(self, test_client: TestClient, auth_headers: dict, test_db_session: Session):
        """测试获取对话消息"""
        # 创建测试会话和对话
        session = Session(
            name="测试会话",
            description="测试描述",
            models=["gpt-3.5-turbo"]
        )
        test_db_session.add(session)
        test_db_session.commit()
        test_db_session.refresh(session)
        
        conversation = Conversation(
            title="测试对话",
            session_id=session.id
        )
        test_db_session.add(conversation)
        test_db_session.commit()
        test_db_session.refresh(conversation)
        
        response = test_client.get(
            f"/api/conversations/{conversation.id}/messages",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "messages" in data
        assert "total" in data
        assert "page" in data
        assert "size" in data
    
    def test_update_conversation(self, test_client: TestClient, auth_headers: dict, test_db_session: Session):
        """测试更新对话"""
        # 创建测试会话和对话
        session = Session(
            name="测试会话",
            description="测试描述",
            models=["gpt-3.5-turbo"]
        )
        test_db_session.add(session)
        test_db_session.commit()
        test_db_session.refresh(session)
        
        conversation = Conversation(
            title="原始标题",
            session_id=session.id
        )
        test_db_session.add(conversation)
        test_db_session.commit()
        test_db_session.refresh(conversation)
        
        update_data = {
            "title": "更新后的标题"
        }
        
        response = test_client.put(
            f"/api/conversations/{conversation.id}",
            json=update_data,
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["title"] == update_data["title"]
    
    def test_delete_conversation(self, test_client: TestClient, auth_headers: dict, test_db_session: Session):
        """测试删除对话"""
        # 创建测试会话和对话
        session = Session(
            name="测试会话",
            description="测试描述",
            models=["gpt-3.5-turbo"]
        )
        test_db_session.add(session)
        test_db_session.commit()
        test_db_session.refresh(session)
        
        conversation = Conversation(
            title="待删除对话",
            session_id=session.id
        )
        test_db_session.add(conversation)
        test_db_session.commit()
        test_db_session.refresh(conversation)
        
        response = test_client.delete(
            f"/api/conversations/{conversation.id}",
            headers=auth_headers
        )
        
        assert response.status_code == 204
        
        # 验证对话已被删除
        get_response = test_client.get(
            f"/api/conversations/{conversation.id}",
            headers=auth_headers
        )
        assert get_response.status_code == 404
    
    def test_conversation_search(self, test_client: TestClient, auth_headers: dict, test_db_session: Session):
        """测试对话搜索"""
        # 创建测试会话
        session = Session(
            name="测试会话",
            description="测试描述",
            models=["gpt-3.5-turbo"]
        )
        test_db_session.add(session)
        test_db_session.commit()
        test_db_session.refresh(session)
        
        # 创建测试对话
        conversation1 = Conversation(
            title="Python编程讨论",
            session_id=session.id
        )
        conversation2 = Conversation(
            title="机器学习算法",
            session_id=session.id
        )
        
        test_db_session.add(conversation1)
        test_db_session.add(conversation2)
        test_db_session.commit()
        
        # 搜索包含"Python"的对话
        response = test_client.get(
            "/api/conversations/?search=Python",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["conversations"]) >= 1
        assert any("Python" in conv["title"] for conv in data["conversations"])
    
    def test_conversation_filter_by_session(self, test_client: TestClient, auth_headers: dict, test_db_session: Session):
        """测试按会话过滤对话"""
        # 创建两个测试会话
        session1 = Session(
            name="会话1",
            description="描述1",
            models=["gpt-3.5-turbo"]
        )
        session2 = Session(
            name="会话2",
            description="描述2",
            models=["claude-3-sonnet"]
        )
        test_db_session.add(session1)
        test_db_session.add(session2)
        test_db_session.commit()
        test_db_session.refresh(session1)
        test_db_session.refresh(session2)
        
        # 为每个会话创建对话
        conversation1 = Conversation(
            title="会话1的对话",
            session_id=session1.id
        )
        conversation2 = Conversation(
            title="会话2的对话",
            session_id=session2.id
        )
        
        test_db_session.add(conversation1)
        test_db_session.add(conversation2)
        test_db_session.commit()
        
        # 按会话过滤
        response = test_client.get(
            f"/api/conversations/?session_id={session1.id}",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["conversations"]) >= 1
        assert all(conv["session_id"] == str(session1.id) for conv in data["conversations"])