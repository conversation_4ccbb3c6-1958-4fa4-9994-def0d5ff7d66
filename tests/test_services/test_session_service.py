"""会话服务测试"""

from uuid import uuid4

import pytest
from schemas.session import SessionCreate, SessionUpdate
from sqlalchemy.orm import Session

from api.exceptions.handlers import NotFoundAPIError, ValidationAPIError
from database.models import Session
from services.session_service import SessionService


@pytest.mark.service
@pytest.mark.unit
class TestSessionService:
    """会话服务测试类"""
    
    @pytest.fixture
    def session_service(self, test_db_session: Session):
        """创建会话服务实例"""
        return SessionService(test_db_session)
    
    @pytest.fixture
    def sample_session_create(self):
        """示例会话创建数据"""
        return SessionCreate(
            name="测试会话",
            description="这是一个测试会话",
            models=["gpt-3.5-turbo", "claude-3-sonnet"]
        )
    
    @pytest.fixture
    def sample_session_update(self):
        """示例会话更新数据"""
        return SessionUpdate(
            name="更新后的会话",
            description="更新后的描述",
            models=["gpt-4", "claude-3-opus"]
        )
    
    def test_create_session_success(self, session_service: SessionService, sample_session_create: SessionCreate):
        """测试成功创建会话"""
        session = session_service.create_session(sample_session_create)
        
        assert session.id is not None
        assert session.name == sample_session_create.name
        assert session.description == sample_session_create.description
        assert session.models == sample_session_create.models
        assert session.created_at is not None
        assert session.updated_at is not None
    
    def test_create_session_duplicate_name(self, session_service: SessionService, 
                                          sample_session_create: SessionCreate, test_db_session: Session):
        """测试创建重复名称的会话"""
        # 先创建一个会话
        existing_session = Session(
            name=sample_session_create.name,
            description="已存在的会话",
            models=["gpt-3.5-turbo"]
        )
        test_db_session.add(existing_session)
        test_db_session.commit()
        
        # 尝试创建同名会话
        with pytest.raises(ValidationAPIError) as exc_info:
            session_service.create_session(sample_session_create)
        
        assert "已存在" in str(exc_info.value)
    
    def test_create_session_empty_models(self, session_service: SessionService):
        """测试创建空模型列表的会话"""
        session_create = SessionCreate(
            name="测试会话",
            description="测试描述",
            models=[]  # 空模型列表
        )
        
        with pytest.raises(ValidationAPIError) as exc_info:
            session_service.create_session(session_create)
        
        assert "至少" in str(exc_info.value)
    
    def test_get_session_by_id_success(self, session_service: SessionService, test_db_session: Session):
        """测试成功根据ID获取会话"""
        # 创建测试会话
        session = Session(
            name="测试会话",
            description="测试描述",
            models=["gpt-3.5-turbo"]
        )
        test_db_session.add(session)
        test_db_session.commit()
        test_db_session.refresh(session)
        
        # 获取会话
        retrieved_session = session_service.get_session_by_id(session.id)
        
        assert retrieved_session.id == session.id
        assert retrieved_session.name == session.name
        assert retrieved_session.description == session.description
        assert retrieved_session.models == session.models
    
    def test_get_session_by_id_not_found(self, session_service: SessionService):
        """测试获取不存在的会话"""
        non_existent_id = uuid4()
        
        with pytest.raises(NotFoundAPIError) as exc_info:
            session_service.get_session_by_id(non_existent_id)
        
        assert "未找到" in str(exc_info.value)
    
    def test_get_sessions_list(self, session_service: SessionService, test_db_session: Session):
        """测试获取会话列表"""
        # 创建多个测试会话
        sessions = [
            Session(name=f"会话{i}", description=f"描述{i}", models=["gpt-3.5-turbo"])
            for i in range(5)
        ]
        
        for session in sessions:
            test_db_session.add(session)
        test_db_session.commit()
        
        # 获取会话列表
        result = session_service.get_sessions(page=1, size=10)
        
        assert result["total"] >= 5
        assert len(result["sessions"]) >= 5
        assert result["page"] == 1
        assert result["size"] == 10
    
    def test_get_sessions_with_pagination(self, session_service: SessionService, test_db_session: Session):
        """测试分页获取会话列表"""
        # 创建多个测试会话
        sessions = [
            Session(name=f"会话{i}", description=f"描述{i}", models=["gpt-3.5-turbo"])
            for i in range(15)
        ]
        
        for session in sessions:
            test_db_session.add(session)
        test_db_session.commit()
        
        # 获取第一页
        page1 = session_service.get_sessions(page=1, size=5)
        assert len(page1["sessions"]) == 5
        assert page1["page"] == 1
        
        # 获取第二页
        page2 = session_service.get_sessions(page=2, size=5)
        assert len(page2["sessions"]) == 5
        assert page2["page"] == 2
        
        # 确保两页的会话不同
        page1_ids = {s.id for s in page1["sessions"]}
        page2_ids = {s.id for s in page2["sessions"]}
        assert page1_ids.isdisjoint(page2_ids)
    
    def test_search_sessions(self, session_service: SessionService, test_db_session: Session):
        """测试搜索会话"""
        # 创建测试会话
        sessions = [
            Session(name="Python编程会话", description="关于Python的讨论", models=["gpt-3.5-turbo"]),
            Session(name="机器学习会话", description="关于ML的讨论", models=["claude-3-sonnet"]),
            Session(name="数据分析会话", description="关于数据分析的讨论", models=["gpt-4"])
        ]
        
        for session in sessions:
            test_db_session.add(session)
        test_db_session.commit()
        
        # 搜索包含"Python"的会话
        result = session_service.search_sessions("Python")
        
        assert len(result["sessions"]) >= 1
        assert any("Python" in s.name for s in result["sessions"])
    
    def test_filter_sessions_by_model(self, session_service: SessionService, test_db_session: Session):
        """测试按模型过滤会话"""
        # 创建测试会话
        sessions = [
            Session(name="GPT会话", description="使用GPT", models=["gpt-3.5-turbo"]),
            Session(name="Claude会话", description="使用Claude", models=["claude-3-sonnet"]),
            Session(name="混合会话", description="使用多个模型", models=["gpt-3.5-turbo", "claude-3-sonnet"])
        ]
        
        for session in sessions:
            test_db_session.add(session)
        test_db_session.commit()
        
        # 按模型过滤
        result = session_service.filter_sessions_by_model("gpt-3.5-turbo")
        
        assert len(result["sessions"]) >= 2
        assert all("gpt-3.5-turbo" in s.models for s in result["sessions"])
    
    def test_update_session_success(self, session_service: SessionService, 
                                  sample_session_update: SessionUpdate, test_db_session: Session):
        """测试成功更新会话"""
        # 创建测试会话
        session = Session(
            name="原始会话",
            description="原始描述",
            models=["gpt-3.5-turbo"]
        )
        test_db_session.add(session)
        test_db_session.commit()
        test_db_session.refresh(session)
        
        # 更新会话
        updated_session = session_service.update_session(session.id, sample_session_update)
        
        assert updated_session.name == sample_session_update.name
        assert updated_session.description == sample_session_update.description
        assert updated_session.models == sample_session_update.models
        assert updated_session.updated_at > updated_session.created_at
    
    def test_update_session_not_found(self, session_service: SessionService, sample_session_update: SessionUpdate):
        """测试更新不存在的会话"""
        non_existent_id = uuid4()
        
        with pytest.raises(NotFoundAPIError) as exc_info:
            session_service.update_session(non_existent_id, sample_session_update)
        
        assert "未找到" in str(exc_info.value)
    
    def test_update_session_duplicate_name(self, session_service: SessionService, test_db_session: Session):
        """测试更新会话为重复名称"""
        # 创建两个测试会话
        session1 = Session(name="会话1", description="描述1", models=["gpt-3.5-turbo"])
        session2 = Session(name="会话2", description="描述2", models=["claude-3-sonnet"])
        
        test_db_session.add(session1)
        test_db_session.add(session2)
        test_db_session.commit()
        test_db_session.refresh(session1)
        test_db_session.refresh(session2)
        
        # 尝试将session2的名称更新为session1的名称
        update_data = SessionUpdate(name="会话1")
        
        with pytest.raises(ValidationAPIError) as exc_info:
            session_service.update_session(session2.id, update_data)
        
        assert "已存在" in str(exc_info.value)
    
    def test_delete_session_success(self, session_service: SessionService, test_db_session: Session):
        """测试成功删除会话"""
        # 创建测试会话
        session = Session(
            name="待删除会话",
            description="待删除描述",
            models=["gpt-3.5-turbo"]
        )
        test_db_session.add(session)
        test_db_session.commit()
        test_db_session.refresh(session)
        
        # 删除会话
        session_service.delete_session(session.id)
        
        # 验证会话已被删除
        with pytest.raises(NotFoundAPIError):
            session_service.get_session_by_id(session.id)
    
    def test_delete_session_not_found(self, session_service: SessionService):
        """测试删除不存在的会话"""
        non_existent_id = uuid4()
        
        with pytest.raises(NotFoundAPIError) as exc_info:
            session_service.delete_session(non_existent_id)
        
        assert "未找到" in str(exc_info.value)
    
    def test_delete_session_with_conversations(self, session_service: SessionService, test_db_session: Session):
        """测试删除包含对话的会话"""
        from database.models import Conversation
        
        # 创建测试会话
        session = Session(
            name="有对话的会话",
            description="包含对话的会话",
            models=["gpt-3.5-turbo"]
        )
        test_db_session.add(session)
        test_db_session.commit()
        test_db_session.refresh(session)
        
        # 创建关联的对话
        conversation = Conversation(
            title="测试对话",
            session_id=session.id
        )
        test_db_session.add(conversation)
        test_db_session.commit()
        
        # 删除会话（应该级联删除对话）
        session_service.delete_session(session.id)
        
        # 验证会话和对话都被删除
        with pytest.raises(NotFoundAPIError):
            session_service.get_session_by_id(session.id)
        
        # 验证对话也被删除
        deleted_conversation = test_db_session.query(Conversation).filter(
            Conversation.id == conversation.id
        ).first()
        assert deleted_conversation is None
    
    def test_get_session_statistics(self, session_service: SessionService, test_db_session: Session):
        """测试获取会话统计信息"""
        from database.models import Conversation
        
        # 创建测试会话
        session = Session(
            name="统计测试会话",
            description="用于统计的会话",
            models=["gpt-3.5-turbo", "claude-3-sonnet"]
        )
        test_db_session.add(session)
        test_db_session.commit()
        test_db_session.refresh(session)
        
        # 创建对话和消息
        conversation = Conversation(title="测试对话", session_id=session.id)
        test_db_session.add(conversation)
        test_db_session.commit()
        test_db_session.refresh(conversation)
        
        # 由于Message模型不存在，这里不再创建消息
        # 直接测试删除会话功能
        
        # 获取统计信息
        stats = session_service.get_session_statistics(session.id)
        
        assert stats["session_id"] == str(session.id)
        assert stats["total_conversations"] >= 1
        assert stats["total_messages"] >= 2
        assert "models_used" in stats
        assert "created_at" in stats
    
    def test_validate_models(self, session_service: SessionService):
        """测试模型验证"""
        # 测试有效模型
        valid_models = ["gpt-3.5-turbo", "claude-3-sonnet"]
        assert session_service._validate_models(valid_models) is True
        
        # 测试无效模型
        invalid_models = ["invalid-model"]
        assert session_service._validate_models(invalid_models) is False
        
        # 测试空列表
        empty_models = []
        assert session_service._validate_models(empty_models) is False
    
    def test_session_name_uniqueness_check(self, session_service: SessionService, test_db_session: Session):
        """测试会话名称唯一性检查"""
        # 创建测试会话
        session = Session(
            name="唯一名称测试",
            description="测试描述",
            models=["gpt-3.5-turbo"]
        )
        test_db_session.add(session)
        test_db_session.commit()
        
        # 检查已存在的名称
        assert session_service._is_name_unique("唯一名称测试") is False
        
        # 检查不存在的名称
        assert session_service._is_name_unique("不存在的名称") is True
        
        # 检查更新时排除自身
        assert session_service._is_name_unique("唯一名称测试", exclude_id=session.id) is True