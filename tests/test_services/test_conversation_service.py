"""对话服务测试"""

from uuid import uuid4

import pytest
from schemas.conversation import ConversationCreate, ConversationUpdate
from schemas.message import MessageCreate
from sqlalchemy.orm import Session

from api.exceptions.handlers import NotFoundAPIError, ValidationAPIError
from database.models import Session, Conversation
from services.conversation_service import ConversationService


@pytest.mark.service
@pytest.mark.unit
class TestConversationService:
    """对话服务测试类"""
    
    @pytest.fixture
    def conversation_service(self, test_db_session: Session):
        """创建对话服务实例"""
        return ConversationService(test_db_session)
    
    @pytest.fixture
    def test_session(self, test_db_session: Session):
        """创建测试会话"""
        session = Session(
            name="测试会话",
            description="测试描述",
            models=["gpt-3.5-turbo", "claude-3-sonnet"]
        )
        test_db_session.add(session)
        test_db_session.commit()
        test_db_session.refresh(session)
        return session
    
    @pytest.fixture
    def sample_conversation_create(self, test_session: Session):
        """示例对话创建数据"""
        return ConversationCreate(
            title="测试对话",
            session_id=test_session.id
        )
    
    @pytest.fixture
    def sample_conversation_update(self):
        """示例对话更新数据"""
        return ConversationUpdate(
            title="更新后的对话标题"
        )
    
    def test_create_conversation_success(self, conversation_service: ConversationService, 
                                       sample_conversation_create: ConversationCreate):
        """测试成功创建对话"""
        conversation = conversation_service.create_conversation(sample_conversation_create)
        
        assert conversation.id is not None
        assert conversation.title == sample_conversation_create.title
        assert conversation.session_id == sample_conversation_create.session_id
        assert conversation.created_at is not None
        assert conversation.updated_at is not None
    
    def test_create_conversation_invalid_session(self, conversation_service: ConversationService):
        """测试使用无效会话ID创建对话"""
        conversation_create = ConversationCreate(
            title="测试对话",
            session_id=uuid4()  # 不存在的会话ID
        )
        
        with pytest.raises(NotFoundAPIError) as exc_info:
            conversation_service.create_conversation(conversation_create)
        
        assert "会话" in str(exc_info.value) and "未找到" in str(exc_info.value)
    
    def test_create_conversation_empty_title(self, conversation_service: ConversationService, test_session: Session):
        """测试创建空标题的对话"""
        conversation_create = ConversationCreate(
            title="",  # 空标题
            session_id=test_session.id
        )
        
        with pytest.raises(ValidationAPIError) as exc_info:
            conversation_service.create_conversation(conversation_create)
        
        assert "标题" in str(exc_info.value)
    
    def test_get_conversation_by_id_success(self, conversation_service: ConversationService, 
                                          test_session: Session, test_db_session: Session):
        """测试成功根据ID获取对话"""
        # 创建测试对话
        conversation = Conversation(
            title="测试对话",
            session_id=test_session.id
        )
        test_db_session.add(conversation)
        test_db_session.commit()
        test_db_session.refresh(conversation)
        
        # 获取对话
        retrieved_conversation = conversation_service.get_conversation_by_id(conversation.id)
        
        assert retrieved_conversation.id == conversation.id
        assert retrieved_conversation.title == conversation.title
        assert retrieved_conversation.session_id == conversation.session_id
    
    def test_get_conversation_by_id_not_found(self, conversation_service: ConversationService):
        """测试获取不存在的对话"""
        non_existent_id = uuid4()
        
        with pytest.raises(NotFoundAPIError) as exc_info:
            conversation_service.get_conversation_by_id(non_existent_id)
        
        assert "对话" in str(exc_info.value) and "未找到" in str(exc_info.value)
    
    def test_get_conversations_list(self, conversation_service: ConversationService, 
                                  test_session: Session, test_db_session: Session):
        """测试获取对话列表"""
        # 创建多个测试对话
        conversations = [
            Conversation(title=f"对话{i}", session_id=test_session.id)
            for i in range(5)
        ]
        
        for conversation in conversations:
            test_db_session.add(conversation)
        test_db_session.commit()
        
        # 获取对话列表
        result = conversation_service.get_conversations(page=1, size=10)
        
        assert result["total"] >= 5
        assert len(result["conversations"]) >= 5
        assert result["page"] == 1
        assert result["size"] == 10
    
    def test_get_conversations_with_pagination(self, conversation_service: ConversationService, 
                                             test_session: Session, test_db_session: Session):
        """测试分页获取对话列表"""
        # 创建多个测试对话
        conversations = [
            Conversation(title=f"对话{i}", session_id=test_session.id)
            for i in range(15)
        ]
        
        for conversation in conversations:
            test_db_session.add(conversation)
        test_db_session.commit()
        
        # 获取第一页
        page1 = conversation_service.get_conversations(page=1, size=5)
        assert len(page1["conversations"]) == 5
        assert page1["page"] == 1
        
        # 获取第二页
        page2 = conversation_service.get_conversations(page=2, size=5)
        assert len(page2["conversations"]) == 5
        assert page2["page"] == 2
        
        # 确保两页的对话不同
        page1_ids = {c.id for c in page1["conversations"]}
        page2_ids = {c.id for c in page2["conversations"]}
        assert page1_ids.isdisjoint(page2_ids)
    
    def test_search_conversations(self, conversation_service: ConversationService, 
                                test_session: Session, test_db_session: Session):
        """测试搜索对话"""
        # 创建测试对话
        conversations = [
            Conversation(title="Python编程讨论", session_id=test_session.id),
            Conversation(title="机器学习算法", session_id=test_session.id),
            Conversation(title="数据分析方法", session_id=test_session.id)
        ]
        
        for conversation in conversations:
            test_db_session.add(conversation)
        test_db_session.commit()
        
        # 搜索包含"Python"的对话
        result = conversation_service.search_conversations("Python")
        
        assert len(result["conversations"]) >= 1
        assert any("Python" in c.title for c in result["conversations"])
    
    def test_filter_conversations_by_session(self, conversation_service: ConversationService, 
                                           test_db_session: Session):
        """测试按会话过滤对话"""
        # 创建两个测试会话
        session1 = Session(name="会话1", description="描述1", models=["gpt-3.5-turbo"])
        session2 = Session(name="会话2", description="描述2", models=["claude-3-sonnet"])
        test_db_session.add(session1)
        test_db_session.add(session2)
        test_db_session.commit()
        test_db_session.refresh(session1)
        test_db_session.refresh(session2)
        
        # 为每个会话创建对话
        conversations = [
            Conversation(title="会话1的对话1", session_id=session1.id),
            Conversation(title="会话1的对话2", session_id=session1.id),
            Conversation(title="会话2的对话1", session_id=session2.id)
        ]
        
        for conversation in conversations:
            test_db_session.add(conversation)
        test_db_session.commit()
        
        # 按会话过滤
        result = conversation_service.filter_conversations_by_session(session1.id)
        
        assert len(result["conversations"]) >= 2
        assert all(c.session_id == session1.id for c in result["conversations"])
    
    def test_update_conversation_success(self, conversation_service: ConversationService, 
                                       sample_conversation_update: ConversationUpdate,
                                       test_session: Session, test_db_session: Session):
        """测试成功更新对话"""
        # 创建测试对话
        conversation = Conversation(
            title="原始标题",
            session_id=test_session.id
        )
        test_db_session.add(conversation)
        test_db_session.commit()
        test_db_session.refresh(conversation)
        
        # 更新对话
        updated_conversation = conversation_service.update_conversation(conversation.id, sample_conversation_update)
        
        assert updated_conversation.title == sample_conversation_update.title
        assert updated_conversation.updated_at > updated_conversation.created_at
    
    def test_update_conversation_not_found(self, conversation_service: ConversationService, 
                                         sample_conversation_update: ConversationUpdate):
        """测试更新不存在的对话"""
        non_existent_id = uuid4()
        
        with pytest.raises(NotFoundAPIError) as exc_info:
            conversation_service.update_conversation(non_existent_id, sample_conversation_update)
        
        assert "对话" in str(exc_info.value) and "未找到" in str(exc_info.value)
    
    def test_delete_conversation_success(self, conversation_service: ConversationService, 
                                       test_session: Session, test_db_session: Session):
        """测试成功删除对话"""
        # 创建测试对话
        conversation = Conversation(
            title="待删除对话",
            session_id=test_session.id
        )
        test_db_session.add(conversation)
        test_db_session.commit()
        test_db_session.refresh(conversation)
        
        # 删除对话
        conversation_service.delete_conversation(conversation.id)
        
        # 验证对话已被删除
        with pytest.raises(NotFoundAPIError):
            conversation_service.get_conversation_by_id(conversation.id)
    
    def test_delete_conversation_not_found(self, conversation_service: ConversationService):
        """测试删除不存在的对话"""
        non_existent_id = uuid4()
        
        with pytest.raises(NotFoundAPIError) as exc_info:
            conversation_service.delete_conversation(non_existent_id)
        
        assert "对话" in str(exc_info.value) and "未找到" in str(exc_info.value)
    
    def test_delete_conversation_with_messages(self, conversation_service: ConversationService, 
                                             test_session: Session, test_db_session: Session):
        """测试删除包含消息的对话"""
        # 创建测试对话
        conversation = Conversation(
            title="有消息的对话",
            session_id=test_session.id
        )
        test_db_session.add(conversation)
        test_db_session.commit()
        test_db_session.refresh(conversation)
        
        # 注释掉消息创建，因为数据库模型中已经移除了Message模型
        # message = Message(
        #     content="测试消息",
        #     role="user",
        #     conversation_id=conversation.id
        # )
        # test_db_session.add(message)
        # test_db_session.commit()
        
        # 删除对话（应该级联删除消息）
        conversation_service.delete_conversation(conversation.id)
        
        # 验证对话和消息都被删除
        with pytest.raises(NotFoundAPIError):
            conversation_service.get_conversation_by_id(conversation.id)
        
        # 验证消息也被删除（注释掉，因为Message模型已移除）
        # deleted_message = test_db_session.query(Message).filter(
        #     Message.conversation_id == conversation.id
        # ).first()
        # assert deleted_message is None
    
    def test_add_message_to_conversation(self, conversation_service: ConversationService, 
                                       test_session: Session, test_db_session: Session):
        """测试向对话添加消息"""
        # 创建测试对话
        conversation = Conversation(
            title="测试对话",
            session_id=test_session.id
        )
        test_db_session.add(conversation)
        test_db_session.commit()
        test_db_session.refresh(conversation)
        
        # 创建消息
        message_create = MessageCreate(
            content="这是一条测试消息",
            role="user",
            conversation_id=conversation.id
        )
        
        message = conversation_service.add_message_to_conversation(message_create)
        
        assert message.id is not None
        assert message.content == message_create.content
        assert message.role == message_create.role
        assert message.conversation_id == conversation.id
        assert message.created_at is not None
    
    def test_add_message_invalid_conversation(self, conversation_service: ConversationService):
        """测试向无效对话添加消息"""
        message_create = MessageCreate(
            content="测试消息",
            role="user",
            conversation_id=uuid4()  # 不存在的对话ID
        )
        
        with pytest.raises(NotFoundAPIError) as exc_info:
            conversation_service.add_message_to_conversation(message_create)
        
        assert "对话" in str(exc_info.value) and "未找到" in str(exc_info.value)
    
    def test_get_conversation_messages(self, conversation_service: ConversationService, 
                                     test_session: Session, test_db_session: Session):
        """测试获取对话消息"""
        # 创建测试对话
        conversation = Conversation(
            title="测试对话",
            session_id=test_session.id
        )
        test_db_session.add(conversation)
        test_db_session.commit()
        test_db_session.refresh(conversation)
        
        # 创建多条消息
        # 注释掉消息创建，因为数据库模型中已经移除了Message模型
        # messages = [
        #     Message(
        #         content=f"消息{i}",
        #         role="user" if i % 2 == 0 else "assistant",
        #         conversation_id=conversation.id
        #     )
        #     for i in range(5)
        # ]
        messages = []
        
        for message in messages:
            test_db_session.add(message)
        test_db_session.commit()
        
        # 获取消息列表
        result = conversation_service.get_conversation_messages(conversation.id, page=1, size=10)
        
        assert result["total"] >= 5
        assert len(result["messages"]) >= 5
        assert result["page"] == 1
        assert result["size"] == 10
        assert all(m.conversation_id == conversation.id for m in result["messages"])
    
    def test_get_conversation_statistics(self, conversation_service: ConversationService, 
                                       test_session: Session, test_db_session: Session):
        """测试获取对话统计信息"""
        # 创建测试对话
        conversation = Conversation(
            title="统计测试对话",
            session_id=test_session.id
        )
        test_db_session.add(conversation)
        test_db_session.commit()
        test_db_session.refresh(conversation)
        
        # 注释掉消息创建，因为数据库模型中已经移除了Message模型
        # messages = [
        #     Message(
        #         content="用户消息1",
        #         role="user",
        #         conversation_id=conversation.id
        #     ),
        #     Message(
        #         content="助手回复1",
        #         role="assistant",
        #         conversation_id=conversation.id,
        #         model="gpt-3.5-turbo",
        #         token_count=50
        #     ),
        #     Message(
        #         content="用户消息2",
        #         role="user",
        #         conversation_id=conversation.id
        #     ),
        #     Message(
        #         content="助手回复2",
        #         role="assistant",
        #         conversation_id=conversation.id,
        #         model="claude-3-sonnet",
        #         token_count=75
        #     )
        # ]
        messages = []
        
        for message in messages:
            test_db_session.add(message)
        test_db_session.commit()
        
        # 获取统计信息
        stats = conversation_service.get_conversation_statistics(conversation.id)
        
        assert stats["conversation_id"] == str(conversation.id)
        assert stats["total_messages"] >= 4
        assert stats["user_messages"] >= 2
        assert stats["assistant_messages"] >= 2
        assert stats["total_tokens"] >= 125
        assert "models_used" in stats
        assert "created_at" in stats
        assert "last_message_at" in stats
    
    def test_conversation_title_validation(self, conversation_service: ConversationService):
        """测试对话标题验证"""
        # 测试有效标题
        assert conversation_service._validate_title("有效的对话标题") is True
        
        # 测试空标题
        assert conversation_service._validate_title("") is False
        assert conversation_service._validate_title("   ") is False
        
        # 测试过长标题
        long_title = "a" * 256  # 假设最大长度为255
        assert conversation_service._validate_title(long_title) is False
    
    def test_get_recent_conversations(self, conversation_service: ConversationService, 
                                    test_session: Session, test_db_session: Session):
        """测试获取最近的对话"""
        # 创建多个测试对话，时间间隔不同
        conversations = []
        for i in range(5):
            conversation = Conversation(
                title=f"对话{i}",
                session_id=test_session.id
            )
            test_db_session.add(conversation)
            test_db_session.commit()
            test_db_session.refresh(conversation)
            conversations.append(conversation)
        
        # 获取最近的对话
        recent_conversations = conversation_service.get_recent_conversations(limit=3)
        
        assert len(recent_conversations) <= 3
        # 验证按时间倒序排列
        for i in range(len(recent_conversations) - 1):
            assert recent_conversations[i].created_at >= recent_conversations[i + 1].created_at
    
    def test_conversation_session_validation(self, conversation_service: ConversationService, test_db_session: Session):
        """测试对话会话验证"""
        # 创建测试会话
        session = Session(
            name="验证测试会话",
            description="用于验证的会话",
            models=["gpt-3.5-turbo"]
        )
        test_db_session.add(session)
        test_db_session.commit()
        test_db_session.refresh(session)
        
        # 测试有效会话ID
        assert conversation_service._validate_session_exists(session.id) is True
        
        # 测试无效会话ID
        assert conversation_service._validate_session_exists(uuid4()) is False