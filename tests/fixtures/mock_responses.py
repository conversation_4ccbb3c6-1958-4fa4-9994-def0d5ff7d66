"""模拟API响应数据"""

from typing import Dict, List, Any

# OpenAI API响应
MOCK_OPENAI_RESPONSE = {
    "id": "chatcmpl-123",
    "object": "chat.completion",
    "created": **********,
    "model": "gpt-3.5-turbo",
    "choices": [
        {
            "index": 0,
            "message": {
                "role": "assistant",
                "content": "人工智能（Artificial Intelligence，简称AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。AI的发展历史可以追溯到20世纪50年代，经历了多个重要阶段的发展。"
            },
            "finish_reason": "stop"
        }
    ],
    "usage": {
        "prompt_tokens": 20,
        "completion_tokens": 150,
        "total_tokens": 170
    }
}

# Anthropic Claude API响应
MOCK_ANTHROPIC_RESPONSE = {
    "id": "msg_123",
    "type": "message",
    "role": "assistant",
    "content": [
        {
            "type": "text",
            "text": "人工智能是一门致力于创建智能机器的科学和工程学科。它涉及开发能够执行通常需要人类认知能力的任务的计算机系统，如学习、推理、感知、理解自然语言和解决问题。"
        }
    ],
    "model": "claude-3-sonnet-20240229",
    "stop_reason": "end_turn",
    "stop_sequence": None,
    "usage": {
        "input_tokens": 20,
        "output_tokens": 160,
        "total_tokens": 180
    }
}

# 模型列表响应
MOCK_MODEL_LIST_RESPONSE = {
    "openai": {
        "data": [
            {
                "id": "gpt-3.5-turbo",
                "object": "model",
                "created": 1677610602,
                "owned_by": "openai",
                "permission": [],
                "root": "gpt-3.5-turbo",
                "parent": None
            },
            {
                "id": "gpt-4",
                "object": "model",
                "created": 1687882411,
                "owned_by": "openai",
                "permission": [],
                "root": "gpt-4",
                "parent": None
            },
            {
                "id": "gpt-4-turbo",
                "object": "model",
                "created": 1712361441,
                "owned_by": "system",
                "permission": [],
                "root": "gpt-4-turbo",
                "parent": None
            }
        ],
        "object": "list"
    },
    "anthropic": [
        {
            "name": "claude-3-haiku-20240307",
            "type": "text",
            "display_name": "Claude 3 Haiku"
        },
        {
            "name": "claude-3-sonnet-20240229",
            "type": "text",
            "display_name": "Claude 3 Sonnet"
        },
        {
            "name": "claude-3-opus-20240229",
            "type": "text",
            "display_name": "Claude 3 Opus"
        }
    ]
}

# 错误响应
MOCK_ERROR_RESPONSE = {
    "error": {
        "message": "Invalid API key provided",
        "type": "invalid_request_error",
        "param": None,
        "code": "invalid_api_key"
    }
}

# 速率限制响应
MOCK_RATE_LIMIT_RESPONSE = {
    "error": {
        "message": "Rate limit reached for requests",
        "type": "rate_limit_error",
        "param": None,
        "code": "rate_limit_exceeded"
    }
}

# 服务不可用响应
MOCK_SERVICE_UNAVAILABLE_RESPONSE = {
    "error": {
        "message": "The server is temporarily overloaded or under maintenance",
        "type": "server_error",
        "param": None,
        "code": "service_unavailable"
    }
}

# 比较结果响应
MOCK_COMPARISON_RESPONSE = {
    "comparison_id": "comp_123456",
    "prompt": "请解释量子计算的基本原理",
    "models": ["gpt-3.5-turbo", "gpt-4", "claude-3-sonnet"],
    "responses": [
        {
            "model": "gpt-3.5-turbo",
            "content": "量子计算是一种利用量子力学现象进行计算的技术...",
            "usage": {
                "prompt_tokens": 20,
                "completion_tokens": 150,
                "total_tokens": 170
            },
            "response_time": 1.2,
            "finish_reason": "stop",
            "timestamp": "2024-01-01T00:00:00Z"
        },
        {
            "model": "gpt-4",
            "content": "量子计算是基于量子力学原理的计算范式...",
            "usage": {
                "prompt_tokens": 20,
                "completion_tokens": 180,
                "total_tokens": 200
            },
            "response_time": 2.5,
            "finish_reason": "stop",
            "timestamp": "2024-01-01T00:00:05Z"
        },
        {
            "model": "claude-3-sonnet",
            "content": "量子计算是一种革命性的计算技术...",
            "usage": {
                "input_tokens": 20,
                "output_tokens": 160,
                "total_tokens": 180
            },
            "response_time": 1.8,
            "finish_reason": "end_turn",
            "timestamp": "2024-01-01T00:00:03Z"
        }
    ],
    "analysis": {
        "best_model": "gpt-4",
        "ranking": ["gpt-4", "claude-3-sonnet", "gpt-3.5-turbo"],
        "criteria_scores": {
            "gpt-3.5-turbo": {
                "accuracy": 8.0,
                "clarity": 7.5,
                "completeness": 7.0,
                "creativity": 6.5,
                "overall": 7.25
            },
            "gpt-4": {
                "accuracy": 9.0,
                "clarity": 8.5,
                "completeness": 9.0,
                "creativity": 8.0,
                "overall": 8.625
            },
            "claude-3-sonnet": {
                "accuracy": 8.5,
                "clarity": 8.0,
                "completeness": 8.0,
                "creativity": 7.5,
                "overall": 8.0
            }
        },
        "metrics": {
            "average_response_time": 1.83,
            "total_tokens": 550,
            "cost_estimate": 0.0011,
            "fastest_model": "gpt-3.5-turbo",
            "most_efficient": "gpt-3.5-turbo",
            "highest_quality": "gpt-4"
        }
    },
    "metadata": {
        "session_id": 1,
        "conversation_id": 1,
        "user_id": 1,
        "timestamp": "2024-01-01T00:00:00Z",
        "parameters": {
            "temperature": 0.7,
            "max_tokens": 1000,
            "top_p": 0.9
        }
    }
}

# 流式响应数据
MOCK_STREAMING_RESPONSE = [
    {
        "id": "chatcmpl-123",
        "object": "chat.completion.chunk",
        "created": **********,
        "model": "gpt-3.5-turbo",
        "choices": [
            {
                "index": 0,
                "delta": {
                    "role": "assistant",
                    "content": "人工"
                },
                "finish_reason": None
            }
        ]
    },
    {
        "id": "chatcmpl-123",
        "object": "chat.completion.chunk",
        "created": **********,
        "model": "gpt-3.5-turbo",
        "choices": [
            {
                "index": 0,
                "delta": {
                    "content": "智能"
                },
                "finish_reason": None
            }
        ]
    },
    {
        "id": "chatcmpl-123",
        "object": "chat.completion.chunk",
        "created": **********,
        "model": "gpt-3.5-turbo",
        "choices": [
            {
                "index": 0,
                "delta": {},
                "finish_reason": "stop"
            }
        ]
    }
]

# 健康检查响应
MOCK_HEALTH_CHECK_RESPONSE = {
    "status": "healthy",
    "timestamp": "2024-01-01T00:00:00Z",
    "services": {
        "database": {
            "status": "healthy",
            "response_time": 0.05
        },
        "cache": {
            "status": "healthy",
            "response_time": 0.01
        },
        "models": {
            "openai": {
                "status": "healthy",
                "response_time": 0.5
            },
            "anthropic": {
                "status": "healthy",
                "response_time": 0.8
            }
        }
    },
    "version": "1.0.0",
    "uptime": 86400
}

# 统计数据响应
MOCK_STATISTICS_RESPONSE = {
    "period": "last_30_days",
    "total_requests": 1250,
    "total_tokens": 125000,
    "total_cost": 25.50,
    "models": {
        "gpt-3.5-turbo": {
            "requests": 800,
            "tokens": 80000,
            "cost": 16.00,
            "avg_response_time": 1.2
        },
        "gpt-4": {
            "requests": 300,
            "tokens": 30000,
            "cost": 6.00,
            "avg_response_time": 2.5
        },
        "claude-3-sonnet": {
            "requests": 150,
            "tokens": 15000,
            "cost": 3.50,
            "avg_response_time": 1.8
        }
    },
    "daily_usage": [
        {"date": "2024-01-01", "requests": 45, "tokens": 4500, "cost": 0.90},
        {"date": "2024-01-02", "requests": 52, "tokens": 5200, "cost": 1.04},
        {"date": "2024-01-03", "requests": 38, "tokens": 3800, "cost": 0.76}
    ]
}

# 用户认证响应
MOCK_AUTH_RESPONSE = {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "bearer",
    "expires_in": 3600,
    "user": {
        "id": 1,
        "username": "testuser",
        "email": "<EMAIL>",
        "role": "user"
    }
}

# 会话列表响应
MOCK_SESSIONS_RESPONSE = {
    "sessions": [
        {
            "id": 1,
            "name": "AI研究讨论",
            "description": "关于人工智能最新发展的讨论",
            "models": ["gpt-4", "claude-3-sonnet"],
            "conversation_count": 5,
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T12:00:00Z"
        },
        {
            "id": 2,
            "name": "编程助手",
            "description": "代码编写和调试助手",
            "models": ["gpt-3.5-turbo", "gpt-4"],
            "conversation_count": 12,
            "created_at": "2024-01-02T00:00:00Z",
            "updated_at": "2024-01-02T15:30:00Z"
        }
    ],
    "total": 2,
    "page": 1,
    "per_page": 10
}

# 对话历史响应
MOCK_CONVERSATION_HISTORY_RESPONSE = {
    "conversation": {
        "id": 1,
        "title": "量子计算讨论",
        "session_id": 1,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:30:00Z"
    },
    "messages": [
        {
            "id": 1,
            "content": "请解释量子计算的基本原理",
            "role": "user",
            "timestamp": "2024-01-01T00:00:00Z"
        },
        {
            "id": 2,
            "content": "量子计算是一种利用量子力学现象进行计算的技术...",
            "role": "assistant",
            "model": "gpt-4",
            "timestamp": "2024-01-01T00:00:05Z",
            "usage": {
                "prompt_tokens": 20,
                "completion_tokens": 180,
                "total_tokens": 200
            }
        }
    ],
    "total_messages": 2
}


# 模型列表响应
MOCK_MODEL_LIST_RESPONSE = {
    "object": "list",
    "data": [
        {
            "id": "gpt-3.5-turbo",
            "object": "model",
            "created": 1677610602,
            "owned_by": "openai"
        },
        {
            "id": "gpt-4",
            "object": "model",
            "created": 1687882411,
            "owned_by": "openai"
        }
    ]
}

# 错误响应集合
MOCK_ERROR_RESPONSES = {
    "invalid_api_key": MOCK_ERROR_RESPONSE,
    "rate_limit": MOCK_RATE_LIMIT_RESPONSE,
    "service_unavailable": MOCK_SERVICE_UNAVAILABLE_RESPONSE
}

# 比较结果别名
MOCK_COMPARISON_RESULT = MOCK_COMPARISON_RESPONSE

# 工具函数
def create_mock_response(model: str, content: str, **kwargs) -> Dict[str, Any]:
    """创建模拟响应"""
    if model.startswith("gpt"):
        response = MOCK_OPENAI_RESPONSE.copy()
        response["model"] = model
        response["choices"][0]["message"]["content"] = content
    elif model.startswith("claude"):
        response = MOCK_ANTHROPIC_RESPONSE.copy()
        response["model"] = model
        response["content"][0]["text"] = content
    else:
        raise ValueError(f"Unsupported model: {model}")
    
    # 更新其他字段
    for key, value in kwargs.items():
        if key in response:
            response[key] = value
    
    return response


def create_mock_error_response(error_type: str, message: str, code: str = None) -> Dict[str, Any]:
    """创建模拟错误响应"""
    return {
        "error": {
            "message": message,
            "type": error_type,
            "param": None,
            "code": code or error_type
        }
    }


def create_mock_streaming_response(model: str, content: str) -> List[Dict[str, Any]]:
    """创建模拟流式响应"""
    chunks = []
    words = content.split()
    
    # 第一个chunk包含角色信息
    chunks.append({
        "id": "chatcmpl-123",
        "object": "chat.completion.chunk",
        "created": **********,
        "model": model,
        "choices": [{
            "index": 0,
            "delta": {
                "role": "assistant",
                "content": words[0] if words else ""
            },
            "finish_reason": None
        }]
    })
    
    # 后续chunk包含内容
    for word in words[1:]:
        chunks.append({
            "id": "chatcmpl-123",
            "object": "chat.completion.chunk",
            "created": **********,
            "model": model,
            "choices": [{
                "index": 0,
                "delta": {
                    "content": f" {word}"
                },
                "finish_reason": None
            }]
        })
    
    # 最后一个chunk表示结束
    chunks.append({
        "id": "chatcmpl-123",
        "object": "chat.completion.chunk",
        "created": **********,
        "model": model,
        "choices": [{
            "index": 0,
            "delta": {},
            "finish_reason": "stop"
        }]
    })
    
    return chunks


def create_mock_comparison_response(prompt: str, models: List[str], **kwargs) -> Dict[str, Any]:
    """创建模拟比较响应"""
    response = MOCK_COMPARISON_RESPONSE.copy()
    response["prompt"] = prompt
    response["models"] = models
    
    # 生成每个模型的响应
    responses = []
    for i, model in enumerate(models):
        model_response = {
            "model": model,
            "content": f"这是来自{model}的回复：{prompt}",
            "usage": {
                "prompt_tokens": 20,
                "completion_tokens": 100 + i * 10,
                "total_tokens": 120 + i * 10
            },
            "response_time": 1.0 + i * 0.5,
            "finish_reason": "stop",
            "timestamp": "2024-01-01T00:00:00Z"
        }
        responses.append(model_response)
    
    response["responses"] = responses
    
    # 更新其他字段
    for key, value in kwargs.items():
        if key in response:
            response[key] = value
    
    return response


def get_mock_response_by_status_code(status_code: int) -> Dict[str, Any]:
    """根据状态码获取模拟响应"""
    if status_code == 200:
        return MOCK_OPENAI_RESPONSE
    elif status_code == 401:
        return MOCK_ERROR_RESPONSE
    elif status_code == 429:
        return MOCK_RATE_LIMIT_RESPONSE
    elif status_code == 503:
        return MOCK_SERVICE_UNAVAILABLE_RESPONSE
    else:
        return create_mock_error_response(
            "unknown_error",
            f"Unknown error with status code {status_code}"
        )


# 响应验证
def validate_openai_response(response: Dict[str, Any]) -> bool:
    """验证OpenAI响应格式"""
    required_fields = ["id", "object", "created", "model", "choices", "usage"]
    return all(field in response for field in required_fields)


def validate_anthropic_response(response: Dict[str, Any]) -> bool:
    """验证Anthropic响应格式"""
    required_fields = ["id", "type", "role", "content", "model", "usage"]
    return all(field in response for field in required_fields)


def validate_comparison_response(response: Dict[str, Any]) -> bool:
    """验证比较响应格式"""
    required_fields = ["comparison_id", "prompt", "models", "responses", "analysis"]
    return all(field in response for field in required_fields)


if __name__ == "__main__":
    # 测试响应验证
    print("验证OpenAI响应:", validate_openai_response(MOCK_OPENAI_RESPONSE))
    print("验证Anthropic响应:", validate_anthropic_response(MOCK_ANTHROPIC_RESPONSE))
    print("验证比较响应:", validate_comparison_response(MOCK_COMPARISON_RESPONSE))
    
    # 测试响应创建
    custom_response = create_mock_response("gpt-4", "这是一个自定义响应")
    print("自定义响应创建成功:", validate_openai_response(custom_response))