import pytest
import asyncio
import os
import tempfile
from unittest.mock import Mock, patch, AsyncMock, MagicMock
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.exc import OperationalError, DatabaseError
from typing import Dict, Any, Optional

from src.database.connection import (
    DatabaseManager,
    ConnectionPool,
    DatabaseConfig,
    DatabaseError as CustomDatabaseError,
    ConnectionError as CustomConnectionError,
    TransactionManager,
    DatabaseMigrator,
    DatabaseMonitor,
    QueryOptimizer,
    BackupManager
)
from src.database.models import Base


class TestDatabaseConfig:
    """数据库配置测试类"""
    
    def test_create_config_from_dict(self):
        """测试从字典创建配置"""
        config_dict = {
            "host": "localhost",
            "port": 5432,
            "database": "test_db",
            "username": "test_user",
            "password": "test_pass",
            "driver": "postgresql+asyncpg",
            "pool_size": 10,
            "max_overflow": 20,
            "pool_timeout": 30,
            "pool_recycle": 3600,
            "echo": False
        }
        
        config = DatabaseConfig.from_dict(config_dict)
        
        assert config.host == "localhost"
        assert config.port == 5432
        assert config.database == "test_db"
        assert config.username == "test_user"
        assert config.password == "test_pass"
        assert config.pool_size == 10
    
    def test_create_config_from_url(self):
        """测试从URL创建配置"""
        url = "postgresql+asyncpg://user:pass@localhost:5432/testdb"
        config = DatabaseConfig.from_url(url)
        
        assert config.host == "localhost"
        assert config.port == 5432
        assert config.database == "testdb"
        assert config.username == "user"
        assert config.password == "pass"
        assert config.driver == "postgresql+asyncpg"
    
    def test_config_validation(self):
        """测试配置验证"""
        # 有效配置
        valid_config = DatabaseConfig(
            host="localhost",
            database="test_db",
            username="user",
            password="pass"
        )
        assert valid_config.validate() is True
        
        # 无效配置 - 缺少必填字段
        with pytest.raises(ValueError, match="数据库主机不能为空"):
            invalid_config = DatabaseConfig(
                host="",
                database="test_db",
                username="user",
                password="pass"
            )
            invalid_config.validate()
    
    def test_config_to_url(self):
        """测试配置转换为URL"""
        config = DatabaseConfig(
            host="localhost",
            port=5432,
            database="testdb",
            username="user",
            password="pass",
            driver="postgresql+asyncpg"
        )
        
        url = config.to_url()
        expected_url = "postgresql+asyncpg://user:pass@localhost:5432/testdb"
        assert url == expected_url
    
    def test_config_with_options(self):
        """测试带选项的配置"""
        config = DatabaseConfig(
            host="localhost",
            database="testdb",
            username="user",
            password="pass",
            options={
                "sslmode": "require",
                "connect_timeout": "10"
            }
        )
        
        url = config.to_url()
        assert "sslmode=require" in url
        assert "connect_timeout=10" in url


class TestConnectionPool:
    """连接池测试类"""
    
    @pytest.fixture
    def pool_config(self):
        """连接池配置"""
        return {
            "pool_size": 5,
            "max_overflow": 10,
            "pool_timeout": 30,
            "pool_recycle": 3600,
            "pool_pre_ping": True
        }
    
    @pytest.fixture
    def connection_pool(self, pool_config):
        """创建连接池实例"""
        return ConnectionPool(
            url="sqlite+aiosqlite:///:memory:",
            **pool_config
        )
    
    async def test_create_connection_pool(self, connection_pool):
        """测试创建连接池"""
        await connection_pool.initialize()
        
        assert connection_pool.engine is not None
        assert connection_pool.is_initialized is True
        
        await connection_pool.close()
    
    async def test_get_connection(self, connection_pool):
        """测试获取连接"""
        await connection_pool.initialize()
        
        async with connection_pool.get_connection() as conn:
            assert conn is not None
            # 测试连接是否可用
            result = await conn.execute(text("SELECT 1"))
            assert result.scalar() == 1
        
        await connection_pool.close()
    
    async def test_connection_pool_stats(self, connection_pool):
        """测试连接池统计"""
        await connection_pool.initialize()
        
        stats = connection_pool.get_stats()
        
        assert "pool_size" in stats
        assert "checked_in" in stats
        assert "checked_out" in stats
        assert "overflow" in stats
        
        await connection_pool.close()
    
    async def test_connection_pool_health_check(self, connection_pool):
        """测试连接池健康检查"""
        await connection_pool.initialize()
        
        # 健康的连接池
        health = await connection_pool.health_check()
        assert health["status"] == "healthy"
        assert health["connections_available"] >= 0
        
        await connection_pool.close()
    
    async def test_connection_pool_overflow(self, connection_pool):
        """测试连接池溢出"""
        await connection_pool.initialize()
        
        connections = []
        try:
            # 获取超过池大小的连接
            for i in range(connection_pool.pool_size + connection_pool.max_overflow + 1):
                conn = await connection_pool.get_connection()
                connections.append(conn)
            
            # 应该在某个点抛出超时异常
            pytest.fail("应该抛出连接池溢出异常")
            
        except Exception as e:
            assert "timeout" in str(e).lower() or "overflow" in str(e).lower()
        
        finally:
            # 清理连接
            for conn in connections:
                try:
                    await conn.close()
                except:
                    pass
            await connection_pool.close()
    
    async def test_connection_recovery(self, connection_pool):
        """测试连接恢复"""
        await connection_pool.initialize()
        
        # 模拟连接失败
        with patch.object(connection_pool.engine, 'connect', side_effect=OperationalError("Connection failed", None, None)):
            with pytest.raises(CustomConnectionError):
                async with connection_pool.get_connection():
                    pass
        
        # 连接应该能够恢复
        async with connection_pool.get_connection() as conn:
            result = await conn.execute(text("SELECT 1"))
            assert result.scalar() == 1
        
        await connection_pool.close()


class TestDatabaseManager:
    """数据库管理器测试类"""
    
    @pytest.fixture
    def db_config(self):
        """数据库配置"""
        return DatabaseConfig(
            host="localhost",
            database=":memory:",
            username="test",
            password="test",
            driver="sqlite+aiosqlite"
        )
    
    @pytest.fixture
    async def db_manager(self, db_config):
        """创建数据库管理器实例"""
        manager = DatabaseManager(db_config)
        await manager.initialize()
        
        # 创建表
        async with manager.get_session() as session:
            async with session.begin():
                await session.run_sync(Base.metadata.create_all)
        
        yield manager
        
        await manager.close()
    
    async def test_initialize_database(self, db_config):
        """测试数据库初始化"""
        manager = DatabaseManager(db_config)
        
        assert manager.is_initialized is False
        
        await manager.initialize()
        
        assert manager.is_initialized is True
        assert manager.engine is not None
        
        await manager.close()
    
    async def test_get_session(self, db_manager):
        """测试获取会话"""
        async with db_manager.get_session() as session:
            assert session is not None
            assert isinstance(session, AsyncSession)
            
            # 测试会话是否可用
            result = await session.execute(text("SELECT 1"))
            assert result.scalar() == 1
    
    async def test_execute_query(self, db_manager):
        """测试执行查询"""
        # 执行简单查询
        result = await db_manager.execute_query("SELECT 1 as test_value")
        assert result.scalar() == 1
        
        # 执行带参数的查询
        result = await db_manager.execute_query(
            "SELECT :value as test_value",
            {"value": 42}
        )
        assert result.scalar() == 42
    
    async def test_execute_transaction(self, db_manager):
        """测试执行事务"""
        async def transaction_func(session):
            # 在事务中执行操作
            await session.execute(text("CREATE TEMP TABLE test_table (id INTEGER, name TEXT)"))
            await session.execute(text("INSERT INTO test_table (id, name) VALUES (1, 'test')"))
            
            result = await session.execute(text("SELECT COUNT(*) FROM test_table"))
            return result.scalar()
        
        result = await db_manager.execute_transaction(transaction_func)
        assert result == 1
    
    async def test_transaction_rollback(self, db_manager):
        """测试事务回滚"""
        async def failing_transaction(session):
            await session.execute(text("CREATE TEMP TABLE test_table (id INTEGER)"))
            await session.execute(text("INSERT INTO test_table (id) VALUES (1)"))
            
            # 故意引发错误
            raise Exception("Transaction failed")
        
        with pytest.raises(Exception, match="Transaction failed"):
            await db_manager.execute_transaction(failing_transaction)
        
        # 验证事务已回滚（表不应该存在）
        try:
            await db_manager.execute_query("SELECT COUNT(*) FROM test_table")
            pytest.fail("表应该不存在（事务已回滚）")
        except Exception:
            pass  # 预期的异常
    
    async def test_database_health_check(self, db_manager):
        """测试数据库健康检查"""
        health = await db_manager.health_check()
        
        assert health["status"] == "healthy"
        assert "connection_pool" in health
        assert "response_time" in health
        assert health["response_time"] > 0
    
    async def test_database_stats(self, db_manager):
        """测试数据库统计"""
        stats = await db_manager.get_stats()
        
        assert "connection_pool" in stats
        assert "query_count" in stats
        assert "error_count" in stats
        assert "avg_response_time" in stats
    
    async def test_connection_retry(self, db_config):
        """测试连接重试"""
        # 使用无效的数据库配置
        invalid_config = DatabaseConfig(
            host="invalid_host",
            database="invalid_db",
            username="invalid_user",
            password="invalid_pass",
            driver="postgresql+asyncpg"
        )
        
        manager = DatabaseManager(invalid_config, max_retries=3, retry_delay=0.1)
        
        with pytest.raises(CustomConnectionError):
            await manager.initialize()
    
    async def test_concurrent_sessions(self, db_manager):
        """测试并发会话"""
        async def worker(worker_id):
            async with db_manager.get_session() as session:
                result = await session.execute(text(f"SELECT {worker_id} as worker_id"))
                return result.scalar()
            return None

        # 创建多个并发任务
        tasks = [worker(i) for i in range(10)]
        results = await asyncio.gather(*tasks)
        
        # 验证所有任务都成功完成
        assert len(results) == 10
        assert results == list(range(10))


class TestTransactionManager:
    """事务管理器测试类"""
    
    @pytest.fixture
    async def transaction_manager(self, db_manager):
        """创建事务管理器实例"""
        return TransactionManager(db_manager)
    
    async def test_simple_transaction(self, transaction_manager):
        """测试简单事务"""
        async with transaction_manager.transaction() as tx:
            await tx.session.execute(text("CREATE TEMP TABLE test_tx (id INTEGER)"))
            await tx.session.execute(text("INSERT INTO test_tx (id) VALUES (1)"))
            
            result = await tx.session.execute(text("SELECT COUNT(*) FROM test_tx"))
            count = result.scalar()
            assert count == 1
    
    async def test_transaction_rollback_on_exception(self, transaction_manager):
        """测试异常时事务回滚"""
        try:
            async with transaction_manager.transaction() as tx:
                await tx.session.execute(text("CREATE TEMP TABLE test_tx (id INTEGER)"))
                await tx.session.execute(text("INSERT INTO test_tx (id) VALUES (1)"))
                
                # 故意引发异常
                raise ValueError("Test exception")
        
        except ValueError:
            pass  # 预期的异常
        
        # 验证事务已回滚
        async with transaction_manager.db_manager.get_session() as session:
            try:
                await session.execute(text("SELECT COUNT(*) FROM test_tx"))
                pytest.fail("表应该不存在（事务已回滚）")
            except Exception:
                pass  # 预期的异常
    
    async def test_nested_transactions(self, transaction_manager):
        """测试嵌套事务"""
        async with transaction_manager.transaction() as outer_tx:
            await outer_tx.session.execute(text("CREATE TEMP TABLE test_nested (id INTEGER)"))
            await outer_tx.session.execute(text("INSERT INTO test_nested (id) VALUES (1)"))
            
            # 嵌套事务（保存点）
            async with transaction_manager.savepoint(outer_tx.session) as sp:
                await sp.session.execute(text("INSERT INTO test_nested (id) VALUES (2)"))
                
                # 回滚保存点
                await sp.rollback()
            
            # 外层事务应该仍然有效
            result = await outer_tx.session.execute(text("SELECT COUNT(*) FROM test_nested"))
            count = result.scalar()
            assert count == 1  # 只有第一条记录
    
    async def test_transaction_timeout(self, transaction_manager):
        """测试事务超时"""
        with pytest.raises(asyncio.TimeoutError):
            async with transaction_manager.transaction(timeout=0.1) as tx:
                # 模拟长时间运行的操作
                await asyncio.sleep(0.2)
    
    async def test_transaction_isolation_levels(self, transaction_manager):
        """测试事务隔离级别"""
        # 测试不同的隔离级别
        isolation_levels = [
            "READ_UNCOMMITTED",
            "READ_COMMITTED",
            "REPEATABLE_READ",
            "SERIALIZABLE"
        ]
        
        for level in isolation_levels:
            try:
                async with transaction_manager.transaction(isolation_level=level) as tx:
                    await tx.session.execute(text("SELECT 1"))
            except Exception as e:
                # 某些数据库可能不支持所有隔离级别
                if "not supported" not in str(e).lower():
                    raise


class TestDatabaseMigrator:
    """数据库迁移器测试类"""
    
    @pytest.fixture
    def migrator(self, db_manager):
        """创建迁移器实例"""
        return DatabaseMigrator(db_manager)
    
    @pytest.fixture
    def sample_migrations(self):
        """示例迁移"""
        return [
            {
                "version": "001",
                "name": "create_users_table",
                "up": "CREATE TABLE users (id INTEGER PRIMARY KEY, name TEXT)",
                "down": "DROP TABLE users"
            },
            {
                "version": "002",
                "name": "add_email_to_users",
                "up": "ALTER TABLE users ADD COLUMN email TEXT",
                "down": "ALTER TABLE users DROP COLUMN email"
            }
        ]
    
    async def test_apply_migration(self, migrator, sample_migrations):
        """测试应用迁移"""
        migration = sample_migrations[0]
        
        # 应用迁移
        await migrator.apply_migration(migration)
        
        # 验证迁移已应用
        applied = await migrator.is_migration_applied(migration["version"])
        assert applied is True
        
        # 验证表已创建
        async with migrator.db_manager.get_session() as session:
            result = await session.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='users'"))
            assert result.scalar() == "users"
    
    async def test_rollback_migration(self, migrator, sample_migrations):
        """测试回滚迁移"""
        migration = sample_migrations[0]
        
        # 先应用迁移
        await migrator.apply_migration(migration)
        
        # 然后回滚
        await migrator.rollback_migration(migration)
        
        # 验证迁移已回滚
        applied = await migrator.is_migration_applied(migration["version"])
        assert applied is False
        
        # 验证表已删除
        async with migrator.db_manager.get_session() as session:
            result = await session.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='users'"))
            assert result.scalar() is None
    
    async def test_migration_history(self, migrator, sample_migrations):
        """测试迁移历史"""
        # 应用多个迁移
        for migration in sample_migrations:
            await migrator.apply_migration(migration)
        
        # 获取迁移历史
        history = await migrator.get_migration_history()
        
        assert len(history) == 2
        assert history[0]["version"] == "001"
        assert history[1]["version"] == "002"
    
    async def test_migration_validation(self, migrator):
        """测试迁移验证"""
        # 无效迁移 - 缺少必填字段
        invalid_migration = {
            "version": "003",
            "up": "CREATE TABLE test (id INTEGER)"
            # 缺少 name 和 down
        }
        
        with pytest.raises(ValueError, match="迁移必须包含"):
            await migrator.validate_migration(invalid_migration)
        
        # 有效迁移
        valid_migration = {
            "version": "003",
            "name": "create_test_table",
            "up": "CREATE TABLE test (id INTEGER)",
            "down": "DROP TABLE test"
        }
        
        assert await migrator.validate_migration(valid_migration) is True


class TestDatabaseMonitor:
    """数据库监控器测试类"""
    
    @pytest.fixture
    def monitor(self, db_manager):
        """创建监控器实例"""
        return DatabaseMonitor(db_manager)
    
    async def test_performance_metrics(self, monitor):
        """测试性能指标"""
        # 执行一些查询以生成指标
        for i in range(5):
            await monitor.db_manager.execute_query("SELECT 1")
        
        metrics = await monitor.get_performance_metrics()
        
        assert "query_count" in metrics
        assert "avg_response_time" in metrics
        assert "error_rate" in metrics
        assert metrics["query_count"] >= 5
    
    async def test_connection_monitoring(self, monitor):
        """测试连接监控"""
        conn_metrics = await monitor.get_connection_metrics()
        
        assert "active_connections" in conn_metrics
        assert "pool_size" in conn_metrics
        assert "pool_usage" in conn_metrics
    
    async def test_slow_query_detection(self, monitor):
        """测试慢查询检测"""
        # 设置慢查询阈值
        monitor.slow_query_threshold = 0.01  # 10ms
        
        # 执行一个慢查询
        await monitor.db_manager.execute_query("SELECT 1")
        await asyncio.sleep(0.02)  # 模拟慢查询
        
        slow_queries = await monitor.get_slow_queries()
        
        # 注意：实际的慢查询检测需要在查询执行过程中进行监控
        # 这里只是测试接口
        assert isinstance(slow_queries, list)
    
    async def test_health_monitoring(self, monitor):
        """测试健康监控"""
        health_status = await monitor.check_health()
        
        assert "overall_status" in health_status
        assert "database_status" in health_status
        assert "connection_pool_status" in health_status
        assert health_status["overall_status"] in ["healthy", "warning", "critical"]
    
    async def test_alert_generation(self, monitor):
        """测试告警生成"""
        # 模拟高错误率
        monitor.error_count = 100
        monitor.query_count = 100
        
        alerts = await monitor.check_alerts()
        
        assert isinstance(alerts, list)
        # 如果实现了告警逻辑，应该有高错误率告警
        if alerts:
            assert any("error_rate" in alert["type"] for alert in alerts)


class TestQueryOptimizer:
    """查询优化器测试类"""
    
    @pytest.fixture
    def optimizer(self, db_manager):
        """创建查询优化器实例"""
        return QueryOptimizer(db_manager)
    
    async def test_query_analysis(self, optimizer):
        """测试查询分析"""
        query = "SELECT * FROM users WHERE email = :email"
        
        analysis = await optimizer.analyze_query(query)
        
        assert "query" in analysis
        assert "estimated_cost" in analysis
        assert "suggestions" in analysis
        assert isinstance(analysis["suggestions"], list)
    
    async def test_index_recommendations(self, optimizer):
        """测试索引推荐"""
        # 创建测试表
        await optimizer.db_manager.execute_query(
            "CREATE TEMP TABLE test_users (id INTEGER, email TEXT, name TEXT)"
        )
        
        # 分析需要索引的查询
        queries = [
            "SELECT * FROM test_users WHERE email = '<EMAIL>'",
            "SELECT * FROM test_users WHERE name LIKE 'John%'"
        ]
        
        recommendations = await optimizer.recommend_indexes(queries)
        
        assert isinstance(recommendations, list)
        # 应该推荐在 email 和 name 字段上创建索引
        if recommendations:
            assert any("email" in rec["column"] for rec in recommendations)
    
    async def test_query_optimization(self, optimizer):
        """测试查询优化"""
        original_query = "SELECT * FROM users WHERE 1=1 AND email = :email"
        
        optimized = await optimizer.optimize_query(original_query)
        
        assert "optimized_query" in optimized
        assert "improvements" in optimized
        # 优化后的查询应该移除无用的条件
        assert "1=1" not in optimized["optimized_query"]
    
    async def test_execution_plan_analysis(self, optimizer):
        """测试执行计划分析"""
        query = "SELECT 1"
        
        plan = await optimizer.get_execution_plan(query)
        
        assert "plan" in plan
        assert "cost" in plan
        assert "operations" in plan


class TestBackupManager:
    """备份管理器测试类"""
    
    @pytest.fixture
    def backup_manager(self, db_manager):
        """创建备份管理器实例"""
        return BackupManager(db_manager)
    
    @pytest.fixture
    def temp_backup_dir(self):
        """临时备份目录"""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir
    
    async def test_create_backup(self, backup_manager, temp_backup_dir):
        """测试创建备份"""
        # 创建一些测试数据
        await backup_manager.db_manager.execute_query(
            "CREATE TEMP TABLE test_backup (id INTEGER, data TEXT)"
        )
        await backup_manager.db_manager.execute_query(
            "INSERT INTO test_backup (id, data) VALUES (1, 'test data')"
        )
        
        backup_path = os.path.join(temp_backup_dir, "test_backup.sql")
        
        result = await backup_manager.create_backup(backup_path)
        
        assert result["success"] is True
        assert os.path.exists(backup_path)
        assert result["backup_size"] > 0
    
    async def test_restore_backup(self, backup_manager, temp_backup_dir):
        """测试恢复备份"""
        # 创建备份文件
        backup_content = """
        CREATE TABLE test_restore (id INTEGER, data TEXT);
        INSERT INTO test_restore (id, data) VALUES (1, 'restored data');
        """
        
        backup_path = os.path.join(temp_backup_dir, "test_restore.sql")
        with open(backup_path, 'w') as f:
            f.write(backup_content)
        
        result = await backup_manager.restore_backup(backup_path)
        
        assert result["success"] is True
        
        # 验证数据已恢复
        query_result = await backup_manager.db_manager.execute_query(
            "SELECT data FROM test_restore WHERE id = 1"
        )
        assert query_result.scalar() == "restored data"
    
    async def test_backup_validation(self, backup_manager, temp_backup_dir):
        """测试备份验证"""
        # 创建有效备份
        valid_backup = os.path.join(temp_backup_dir, "valid.sql")
        with open(valid_backup, 'w') as f:
            f.write("CREATE TABLE test (id INTEGER);")
        
        validation = await backup_manager.validate_backup(valid_backup)
        assert validation["valid"] is True
        
        # 创建无效备份
        invalid_backup = os.path.join(temp_backup_dir, "invalid.sql")
        with open(invalid_backup, 'w') as f:
            f.write("INVALID SQL SYNTAX;")
        
        validation = await backup_manager.validate_backup(invalid_backup)
        assert validation["valid"] is False
        assert "errors" in validation
    
    async def test_scheduled_backup(self, backup_manager, temp_backup_dir):
        """测试定时备份"""
        backup_config = {
            "schedule": "daily",
            "backup_dir": temp_backup_dir,
            "retention_days": 7,
            "compress": True
        }
        
        # 设置定时备份
        result = await backup_manager.setup_scheduled_backup(backup_config)
        
        assert result["success"] is True
        assert "schedule_id" in result
        
        # 手动触发备份
        backup_result = await backup_manager.trigger_scheduled_backup(result["schedule_id"])
        
        assert backup_result["success"] is True
        assert "backup_path" in backup_result
    
    async def test_backup_cleanup(self, backup_manager, temp_backup_dir):
        """测试备份清理"""
        # 创建多个备份文件
        backup_files = []
        for i in range(5):
            backup_path = os.path.join(temp_backup_dir, f"backup_{i}.sql")
            with open(backup_path, 'w') as f:
                f.write(f"-- Backup {i}\nCREATE TABLE test_{i} (id INTEGER);")
            backup_files.append(backup_path)
        
        # 清理旧备份（保留最新的2个）
        cleanup_result = await backup_manager.cleanup_old_backups(
            temp_backup_dir, 
            retention_count=2
        )
        
        assert cleanup_result["success"] is True
        assert cleanup_result["deleted_count"] == 3
        
        # 验证只剩下2个文件
        remaining_files = [f for f in os.listdir(temp_backup_dir) if f.endswith('.sql')]
        assert len(remaining_files) == 2


class TestDatabaseIntegration:
    """数据库集成测试类"""
    
    async def test_complete_database_workflow(self):
        """测试完整的数据库工作流程"""
        # 1. 创建数据库配置
        config = DatabaseConfig(
            host="localhost",
            database=":memory:",
            username="test",
            password="test",
            driver="sqlite+aiosqlite"
        )
        
        # 2. 初始化数据库管理器
        db_manager = DatabaseManager(config)
        await db_manager.initialize()
        
        try:
            # 3. 创建表结构
            async with db_manager.get_session() as session:
                await session.execute(text("""
                    CREATE TABLE users (
                        id INTEGER PRIMARY KEY,
                        username TEXT UNIQUE NOT NULL,
                        email TEXT UNIQUE NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """))
                await session.commit()
            
            # 4. 插入测试数据
            async def insert_user(session):
                await session.execute(text("""
                    INSERT INTO users (username, email) 
                    VALUES ('testuser', '<EMAIL>')
                """))
                return True
            
            result = await db_manager.execute_transaction(insert_user)
            assert result is True
            
            # 5. 查询数据
            query_result = await db_manager.execute_query(
                "SELECT username, email FROM users WHERE username = :username",
                {"username": "testuser"}
            )
            
            row = query_result.fetchone()
            assert row[0] == "testuser"
            assert row[1] == "<EMAIL>"
            
            # 6. 检查数据库健康状态
            health = await db_manager.health_check()
            assert health["status"] == "healthy"
            
            # 7. 获取统计信息
            stats = await db_manager.get_stats()
            assert stats["query_count"] > 0
            
        finally:
            # 8. 清理资源
            await db_manager.close()
    
    async def test_error_handling_and_recovery(self):
        """测试错误处理和恢复"""
        config = DatabaseConfig(
            host="localhost",
            database=":memory:",
            username="test",
            password="test",
            driver="sqlite+aiosqlite"
        )
        
        db_manager = DatabaseManager(config)
        await db_manager.initialize()
        
        try:
            # 测试SQL错误处理
            with pytest.raises(Exception):
                await db_manager.execute_query("INVALID SQL SYNTAX")
            
            # 测试事务错误处理
            async def failing_transaction(session):
                await session.execute(text("CREATE TABLE test (id INTEGER)"))
                await session.execute(text("INSERT INTO test (id) VALUES (1)"))
                raise Exception("Transaction failed")
            
            with pytest.raises(Exception):
                await db_manager.execute_transaction(failing_transaction)
            
            # 验证数据库仍然可用
            result = await db_manager.execute_query("SELECT 1")
            assert result.scalar() == 1
            
        finally:
            await db_manager.close()
    
    async def test_concurrent_access(self):
        """测试并发访问"""
        config = DatabaseConfig(
            host="localhost",
            database=":memory:",
            username="test",
            password="test",
            driver="sqlite+aiosqlite"
        )
        
        db_manager = DatabaseManager(config)
        await db_manager.initialize()
        
        try:
            # 创建测试表
            await db_manager.execute_query(
                "CREATE TABLE concurrent_test (id INTEGER, value TEXT)"
            )
            
            # 并发插入数据
            async def insert_worker(worker_id):
                async def insert_data(session):
                    await session.execute(text(
                        "INSERT INTO concurrent_test (id, value) VALUES (:id, :value)"
                    ), {"id": worker_id, "value": f"worker_{worker_id}"})
                
                return await db_manager.execute_transaction(insert_data)
            
            # 创建多个并发任务
            tasks = [insert_worker(i) for i in range(10)]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 验证所有插入都成功
            successful_inserts = sum(1 for r in results if r is True)
            assert successful_inserts == 10
            
            # 验证数据完整性
            count_result = await db_manager.execute_query(
                "SELECT COUNT(*) FROM concurrent_test"
            )
            assert count_result.scalar() == 10
            
        finally:
            await db_manager.close()