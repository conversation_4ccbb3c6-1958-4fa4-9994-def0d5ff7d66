from datetime import datetime

import pytest
from src.database.connection import DatabaseManager, DatabaseConfig
from src.database.models import (
    User, Session, Conversation, Message, Model, APIKey,
    AuditLog, SystemConfig, UserPreference, ModelComparison,
    PromptTemplate, ConversationTag, MessageAttachment, Base
)
from src.database.repositories import (
    BaseRepository,
    UserRepository,
    SessionRepository,
    ConversationRepository,
    MessageRepository,
    ModelRepository,
    APIKeyRepository,
    AuditLogRepository,
    SystemConfigRepository,
    UserPreferenceRepository,
    ModelComparisonRepository,
    PromptTemplateRepository,
    ConversationTagRepository,
    MessageAttachmentRepository,
    RepositoryManager
)


class TestBaseRepository:
    """基础仓储测试类"""
    
    @pytest.fixture
    async def db_session(self):
        """创建测试数据库会话"""
        config = DatabaseConfig(
            host="localhost",
            database=":memory:",
            username="test",
            password="test",
            driver="sqlite+aiosqlite"
        )
        
        db_manager = DatabaseManager(config)
        await db_manager.initialize()
        
        # 创建表
        async with db_manager.get_session() as session:
            async with session.begin():
                await session.run_sync(Base.metadata.create_all)
        
        async with db_manager.get_session() as session:
            yield session
        
        await db_manager.close()
    
    @pytest.fixture
    def base_repository(self, db_session):
        """创建基础仓储实例"""
        return BaseRepository(User, db_session)
    
    async def test_create_entity(self, base_repository, db_session):
        """测试创建实体"""
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password_hash": "hashed_password"
        }
        
        user = await base_repository.create(user_data)
        
        assert user.id is not None
        assert user.username == "testuser"
        assert user.email == "<EMAIL>"
    
    async def test_get_by_id(self, base_repository, db_session):
        """测试根据ID获取实体"""
        # 先创建一个用户
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password_hash": "hashed_password"
        }
        created_user = await base_repository.create(user_data)
        
        # 根据ID获取用户
        retrieved_user = await base_repository.get_by_id(created_user.id)
        
        assert retrieved_user is not None
        assert retrieved_user.id == created_user.id
        assert retrieved_user.username == "testuser"
    
    async def test_get_by_id_not_found(self, base_repository):
        """测试获取不存在的实体"""
        user = await base_repository.get_by_id(999)
        assert user is None
    
    async def test_get_all(self, base_repository):
        """测试获取所有实体"""
        # 创建多个用户
        users_data = [
            {"username": "user1", "email": "<EMAIL>", "password_hash": "hash1"},
            {"username": "user2", "email": "<EMAIL>", "password_hash": "hash2"},
            {"username": "user3", "email": "<EMAIL>", "password_hash": "hash3"}
        ]
        
        for user_data in users_data:
            await base_repository.create(user_data)
        
        # 获取所有用户
        all_users = await base_repository.get_all()
        
        assert len(all_users) == 3
        usernames = [user.username for user in all_users]
        assert "user1" in usernames
        assert "user2" in usernames
        assert "user3" in usernames
    
    async def test_get_all_with_pagination(self, base_repository):
        """测试分页获取实体"""
        # 创建5个用户
        for i in range(5):
            await base_repository.create({
                "username": f"user{i}",
                "email": f"user{i}@example.com",
                "password_hash": f"hash{i}"
            })
        
        # 分页获取（每页2个，第1页）
        page1_users = await base_repository.get_all(limit=2, offset=0)
        assert len(page1_users) == 2
        
        # 分页获取（每页2个，第2页）
        page2_users = await base_repository.get_all(limit=2, offset=2)
        assert len(page2_users) == 2
        
        # 分页获取（每页2个，第3页）
        page3_users = await base_repository.get_all(limit=2, offset=4)
        assert len(page3_users) == 1
    
    async def test_update_entity(self, base_repository):
        """测试更新实体"""
        # 创建用户
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password_hash": "hashed_password"
        }
        user = await base_repository.create(user_data)
        
        # 更新用户
        update_data = {"email": "<EMAIL>"}
        updated_user = await base_repository.update(user.id, update_data)
        
        assert updated_user.email == "<EMAIL>"
        assert updated_user.username == "testuser"  # 未更新的字段保持不变
    
    async def test_update_nonexistent_entity(self, base_repository):
        """测试更新不存在的实体"""
        update_data = {"email": "<EMAIL>"}
        updated_user = await base_repository.update(999, update_data)
        
        assert updated_user is None
    
    async def test_delete_entity(self, base_repository):
        """测试删除实体"""
        # 创建用户
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password_hash": "hashed_password"
        }
        user = await base_repository.create(user_data)
        
        # 删除用户
        deleted = await base_repository.delete(user.id)
        assert deleted is True
        
        # 验证用户已删除
        retrieved_user = await base_repository.get_by_id(user.id)
        assert retrieved_user is None
    
    async def test_delete_nonexistent_entity(self, base_repository):
        """测试删除不存在的实体"""
        deleted = await base_repository.delete(999)
        assert deleted is False
    
    async def test_count_entities(self, base_repository):
        """测试统计实体数量"""
        # 初始数量应该为0
        initial_count = await base_repository.count()
        assert initial_count == 0
        
        # 创建3个用户
        for i in range(3):
            await base_repository.create({
                "username": f"user{i}",
                "email": f"user{i}@example.com",
                "password_hash": f"hash{i}"
            })
        
        # 统计数量
        count = await base_repository.count()
        assert count == 3
    
    async def test_exists_entity(self, base_repository):
        """测试检查实体是否存在"""
        # 不存在的实体
        exists = await base_repository.exists(999)
        assert exists is False
        
        # 创建用户
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password_hash": "hashed_password"
        }
        user = await base_repository.create(user_data)
        
        # 存在的实体
        exists = await base_repository.exists(user.id)
        assert exists is True


class TestUserRepository:
    """用户仓储测试类"""
    
    @pytest.fixture
    async def user_repository(self, db_session):
        """创建用户仓储实例"""
        return UserRepository(db_session)
    
    async def test_get_by_username(self, user_repository):
        """测试根据用户名获取用户"""
        # 创建用户
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password_hash": "hashed_password"
        }
        await user_repository.create(user_data)
        
        # 根据用户名获取用户
        user = await user_repository.get_by_username("testuser")
        
        assert user is not None
        assert user.username == "testuser"
        assert user.email == "<EMAIL>"
    
    async def test_get_by_username_not_found(self, user_repository):
        """测试获取不存在的用户名"""
        user = await user_repository.get_by_username("nonexistent")
        assert user is None
    
    async def test_get_by_email(self, user_repository):
        """测试根据邮箱获取用户"""
        # 创建用户
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password_hash": "hashed_password"
        }
        await user_repository.create(user_data)
        
        # 根据邮箱获取用户
        user = await user_repository.get_by_email("<EMAIL>")
        
        assert user is not None
        assert user.username == "testuser"
        assert user.email == "<EMAIL>"
    
    async def test_get_by_email_not_found(self, user_repository):
        """测试获取不存在的邮箱"""
        user = await user_repository.get_by_email("<EMAIL>")
        assert user is None
    
    async def test_get_active_users(self, user_repository):
        """测试获取活跃用户"""
        # 创建活跃和非活跃用户
        active_user_data = {
            "username": "activeuser",
            "email": "<EMAIL>",
            "password_hash": "hash",
            "is_active": True
        }
        inactive_user_data = {
            "username": "inactiveuser",
            "email": "<EMAIL>",
            "password_hash": "hash",
            "is_active": False
        }
        
        await user_repository.create(active_user_data)
        await user_repository.create(inactive_user_data)
        
        # 获取活跃用户
        active_users = await user_repository.get_active_users()
        
        assert len(active_users) == 1
        assert active_users[0].username == "activeuser"
    
    async def test_search_users(self, user_repository):
        """测试搜索用户"""
        # 创建多个用户
        users_data = [
            {"username": "john_doe", "email": "<EMAIL>", "password_hash": "hash"},
            {"username": "jane_smith", "email": "<EMAIL>", "password_hash": "hash"},
            {"username": "bob_johnson", "email": "<EMAIL>", "password_hash": "hash"}
        ]
        
        for user_data in users_data:
            await user_repository.create(user_data)
        
        # 搜索包含"john"的用户
        search_results = await user_repository.search_users("john")
        
        assert len(search_results) == 2  # john_doe 和 bob_johnson
        usernames = [user.username for user in search_results]
        assert "john_doe" in usernames
        assert "bob_johnson" in usernames
    
    async def test_get_users_by_role(self, user_repository):
        """测试根据角色获取用户"""
        # 创建不同角色的用户
        admin_user_data = {
            "username": "admin",
            "email": "<EMAIL>",
            "password_hash": "hash",
            "role": "admin"
        }
        user_data = {
            "username": "user",
            "email": "<EMAIL>",
            "password_hash": "hash",
            "role": "user"
        }
        
        await user_repository.create(admin_user_data)
        await user_repository.create(user_data)
        
        # 获取管理员用户
        admin_users = await user_repository.get_users_by_role("admin")
        
        assert len(admin_users) == 1
        assert admin_users[0].username == "admin"
    
    async def test_update_last_login(self, user_repository):
        """测试更新最后登录时间"""
        # 创建用户
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password_hash": "hash"
        }
        user = await user_repository.create(user_data)
        
        # 更新最后登录时间
        login_time = datetime.utcnow()
        updated_user = await user_repository.update_last_login(user.id, login_time)
        
        assert updated_user.last_login_at is not None
        assert abs((updated_user.last_login_at - login_time).total_seconds()) < 1


class TestSessionRepository:
    """会话仓储测试类"""
    
    @pytest.fixture
    async def session_repository(self, db_session):
        """创建会话仓储实例"""
        return SessionRepository(db_session)
    
    @pytest.fixture
    async def user_repository(self, db_session):
        """创建用户仓储实例"""
        return UserRepository(db_session)
    
    @pytest.fixture
    async def test_user(self, user_repository):
        """创建测试用户"""
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password_hash": "hash"
        }
        return await user_repository.create(user_data)
    
    async def test_get_sessions_by_user(self, session_repository, test_user):
        """测试根据用户获取会话"""
        # 创建会话
        session_data = {
            "name": "Test Session",
            "user_id": test_user.id,
            "models": ["gpt-3.5-turbo", "gpt-4"]
        }
        await session_repository.create(session_data)
        
        # 获取用户的会话
        sessions = await session_repository.get_sessions_by_user(test_user.id)
        
        assert len(sessions) == 1
        assert sessions[0].name == "Test Session"
        assert sessions[0].user_id == test_user.id
    
    async def test_get_sessions_by_model(self, session_repository, test_user):
        """测试根据模型获取会话"""
        # 创建包含不同模型的会话
        session1_data = {
            "name": "GPT Session",
            "user_id": test_user.id,
            "models": ["gpt-3.5-turbo", "gpt-4"]
        }
        session2_data = {
            "name": "Claude Session",
            "user_id": test_user.id,
            "models": ["claude-3-sonnet"]
        }
        
        await session_repository.create(session1_data)
        await session_repository.create(session2_data)
        
        # 获取包含GPT-4的会话
        gpt4_sessions = await session_repository.get_sessions_by_model("gpt-4")
        
        assert len(gpt4_sessions) == 1
        assert gpt4_sessions[0].name == "GPT Session"
    
    async def test_search_sessions(self, session_repository, test_user):
        """测试搜索会话"""
        # 创建多个会话
        sessions_data = [
            {"name": "Machine Learning Session", "user_id": test_user.id, "models": ["gpt-4"]},
            {"name": "Data Analysis Session", "user_id": test_user.id, "models": ["gpt-3.5-turbo"]},
            {"name": "Web Development Session", "user_id": test_user.id, "models": ["claude-3-sonnet"]}
        ]
        
        for session_data in sessions_data:
            await session_repository.create(session_data)
        
        # 搜索包含"data"的会话
        search_results = await session_repository.search_sessions("data")
        
        assert len(search_results) == 1
        assert search_results[0].name == "Data Analysis Session"
    
    async def test_get_recent_sessions(self, session_repository, test_user):
        """测试获取最近的会话"""
        # 创建多个会话（模拟不同的创建时间）
        for i in range(5):
            session_data = {
                "name": f"Session {i}",
                "user_id": test_user.id,
                "models": ["gpt-3.5-turbo"]
            }
            await session_repository.create(session_data)
        
        # 获取最近的3个会话
        recent_sessions = await session_repository.get_recent_sessions(test_user.id, limit=3)
        
        assert len(recent_sessions) == 3
        # 应该按创建时间倒序排列
        assert recent_sessions[0].name == "Session 4"
        assert recent_sessions[1].name == "Session 3"
        assert recent_sessions[2].name == "Session 2"
    
    async def test_get_session_stats(self, session_repository, test_user):
        """测试获取会话统计"""
        # 创建多个会话
        for i in range(3):
            session_data = {
                "name": f"Session {i}",
                "user_id": test_user.id,
                "models": ["gpt-3.5-turbo"]
            }
            await session_repository.create(session_data)
        
        # 获取会话统计
        stats = await session_repository.get_session_stats(test_user.id)
        
        assert stats["total_sessions"] == 3
        assert "most_used_models" in stats
        assert "recent_activity" in stats


class TestConversationRepository:
    """对话仓储测试类"""
    
    @pytest.fixture
    async def conversation_repository(self, db_session):
        """创建对话仓储实例"""
        return ConversationRepository(db_session)
    
    @pytest.fixture
    async def test_session(self, db_session):
        """创建测试会话"""
        # 先创建用户
        user_repo = UserRepository(db_session)
        user = await user_repo.create({
            "username": "testuser",
            "email": "<EMAIL>",
            "password_hash": "hash"
        })
        
        # 创建会话
        session_repo = SessionRepository(db_session)
        session = await session_repo.create({
            "name": "Test Session",
            "user_id": user.id,
            "models": ["gpt-3.5-turbo"]
        })
        
        return session
    
    async def test_get_conversations_by_session(self, conversation_repository, test_session):
        """测试根据会话获取对话"""
        # 创建对话
        conversation_data = {
            "title": "Test Conversation",
            "session_id": test_session.id
        }
        await conversation_repository.create(conversation_data)
        
        # 获取会话的对话
        conversations = await conversation_repository.get_conversations_by_session(test_session.id)
        
        assert len(conversations) == 1
        assert conversations[0].title == "Test Conversation"
        assert conversations[0].session_id == test_session.id
    
    async def test_search_conversations(self, conversation_repository, test_session):
        """测试搜索对话"""
        # 创建多个对话
        conversations_data = [
            {"title": "Python Programming", "session_id": test_session.id},
            {"title": "JavaScript Tutorial", "session_id": test_session.id},
            {"title": "Data Science with Python", "session_id": test_session.id}
        ]
        
        for conv_data in conversations_data:
            await conversation_repository.create(conv_data)
        
        # 搜索包含"Python"的对话
        search_results = await conversation_repository.search_conversations("Python")
        
        assert len(search_results) == 2
        titles = [conv.title for conv in search_results]
        assert "Python Programming" in titles
        assert "Data Science with Python" in titles
    
    async def test_get_recent_conversations(self, conversation_repository, test_session):
        """测试获取最近的对话"""
        # 创建多个对话
        for i in range(5):
            conversation_data = {
                "title": f"Conversation {i}",
                "session_id": test_session.id
            }
            await conversation_repository.create(conversation_data)
        
        # 获取最近的3个对话
        recent_conversations = await conversation_repository.get_recent_conversations(
            test_session.id, limit=3
        )
        
        assert len(recent_conversations) == 3
        # 应该按更新时间倒序排列
        assert recent_conversations[0].title == "Conversation 4"
    
    async def test_get_conversation_with_messages(self, conversation_repository, test_session):
        """测试获取包含消息的对话"""
        # 创建对话
        conversation_data = {
            "title": "Test Conversation",
            "session_id": test_session.id
        }
        conversation = await conversation_repository.create(conversation_data)
        
        # 创建消息
        message_repo = MessageRepository(conversation_repository.session)
        message_data = {
            "content": "Hello, world!",
            "role": "user",
            "conversation_id": conversation.id
        }
        await message_repo.create(message_data)
        
        # 获取包含消息的对话
        conv_with_messages = await conversation_repository.get_conversation_with_messages(conversation.id)
        
        assert conv_with_messages is not None
        assert len(conv_with_messages.messages) == 1
        assert conv_with_messages.messages[0].content == "Hello, world!"
    
    async def test_update_conversation_title(self, conversation_repository, test_session):
        """测试更新对话标题"""
        # 创建对话
        conversation_data = {
            "title": "Original Title",
            "session_id": test_session.id
        }
        conversation = await conversation_repository.create(conversation_data)
        
        # 更新标题
        updated_conversation = await conversation_repository.update_title(
            conversation.id, "Updated Title"
        )
        
        assert updated_conversation.title == "Updated Title"
    
    async def test_archive_conversation(self, conversation_repository, test_session):
        """测试归档对话"""
        # 创建对话
        conversation_data = {
            "title": "Test Conversation",
            "session_id": test_session.id
        }
        conversation = await conversation_repository.create(conversation_data)
        
        # 归档对话
        archived_conversation = await conversation_repository.archive_conversation(conversation.id)
        
        assert archived_conversation.is_archived is True
    
    async def test_get_conversation_stats(self, conversation_repository, test_session):
        """测试获取对话统计"""
        # 创建多个对话
        for i in range(3):
            conversation_data = {
                "title": f"Conversation {i}",
                "session_id": test_session.id
            }
            await conversation_repository.create(conversation_data)
        
        # 获取对话统计
        stats = await conversation_repository.get_conversation_stats(test_session.id)
        
        assert stats["total_conversations"] == 3
        assert "active_conversations" in stats
        assert "archived_conversations" in stats


class TestMessageRepository:
    """消息仓储测试类"""
    
    @pytest.fixture
    async def message_repository(self, db_session):
        """创建消息仓储实例"""
        return MessageRepository(db_session)
    
    @pytest.fixture
    async def test_conversation(self, db_session):
        """创建测试对话"""
        # 创建用户
        user_repo = UserRepository(db_session)
        user = await user_repo.create({
            "username": "testuser",
            "email": "<EMAIL>",
            "password_hash": "hash"
        })
        
        # 创建会话
        session_repo = SessionRepository(db_session)
        session = await session_repo.create({
            "name": "Test Session",
            "user_id": user.id,
            "models": ["gpt-3.5-turbo"]
        })
        
        # 创建对话
        conversation_repo = ConversationRepository(db_session)
        conversation = await conversation_repo.create({
            "title": "Test Conversation",
            "session_id": session.id
        })
        
        return conversation
    
    async def test_get_messages_by_conversation(self, message_repository, test_conversation):
        """测试根据对话获取消息"""
        # 创建消息
        messages_data = [
            {"content": "Hello", "role": "user", "conversation_id": test_conversation.id},
            {"content": "Hi there!", "role": "assistant", "conversation_id": test_conversation.id},
            {"content": "How are you?", "role": "user", "conversation_id": test_conversation.id}
        ]
        
        for msg_data in messages_data:
            await message_repository.create(msg_data)
        
        # 获取对话的消息
        messages = await message_repository.get_messages_by_conversation(test_conversation.id)
        
        assert len(messages) == 3
        assert messages[0].content == "Hello"
        assert messages[0].role == "user"
        assert messages[1].content == "Hi there!"
        assert messages[1].role == "assistant"
    
    async def test_get_messages_by_role(self, message_repository, test_conversation):
        """测试根据角色获取消息"""
        # 创建不同角色的消息
        messages_data = [
            {"content": "User message 1", "role": "user", "conversation_id": test_conversation.id},
            {"content": "Assistant message 1", "role": "assistant", "conversation_id": test_conversation.id},
            {"content": "User message 2", "role": "user", "conversation_id": test_conversation.id}
        ]
        
        for msg_data in messages_data:
            await message_repository.create(msg_data)
        
        # 获取用户消息
        user_messages = await message_repository.get_messages_by_role(test_conversation.id, "user")
        
        assert len(user_messages) == 2
        assert all(msg.role == "user" for msg in user_messages)
    
    async def test_search_messages(self, message_repository, test_conversation):
        """测试搜索消息"""
        # 创建消息
        messages_data = [
            {"content": "Python programming tutorial", "role": "user", "conversation_id": test_conversation.id},
            {"content": "JavaScript is great", "role": "user", "conversation_id": test_conversation.id},
            {"content": "Python is awesome", "role": "assistant", "conversation_id": test_conversation.id}
        ]
        
        for msg_data in messages_data:
            await message_repository.create(msg_data)
        
        # 搜索包含"Python"的消息
        search_results = await message_repository.search_messages("Python")
        
        assert len(search_results) == 2
        contents = [msg.content for msg in search_results]
        assert "Python programming tutorial" in contents
        assert "Python is awesome" in contents
    
    async def test_get_message_history(self, message_repository, test_conversation):
        """测试获取消息历史"""
        # 创建消息序列
        messages_data = [
            {"content": "Message 1", "role": "user", "conversation_id": test_conversation.id},
            {"content": "Response 1", "role": "assistant", "conversation_id": test_conversation.id},
            {"content": "Message 2", "role": "user", "conversation_id": test_conversation.id},
            {"content": "Response 2", "role": "assistant", "conversation_id": test_conversation.id}
        ]
        
        for msg_data in messages_data:
            await message_repository.create(msg_data)
        
        # 获取消息历史（限制数量）
        history = await message_repository.get_message_history(test_conversation.id, limit=3)
        
        assert len(history) == 3
        # 应该按时间顺序排列
        assert history[0].content == "Message 1"
        assert history[1].content == "Response 1"
        assert history[2].content == "Message 2"
    
    async def test_update_message_content(self, message_repository, test_conversation):
        """测试更新消息内容"""
        # 创建消息
        message_data = {
            "content": "Original content",
            "role": "user",
            "conversation_id": test_conversation.id
        }
        message = await message_repository.create(message_data)
        
        # 更新消息内容
        updated_message = await message_repository.update_content(
            message.id, "Updated content"
        )
        
        assert updated_message.content == "Updated content"
    
    async def test_get_message_stats(self, message_repository, test_conversation):
        """测试获取消息统计"""
        # 创建不同角色的消息
        messages_data = [
            {"content": "User message 1", "role": "user", "conversation_id": test_conversation.id},
            {"content": "User message 2", "role": "user", "conversation_id": test_conversation.id},
            {"content": "Assistant message 1", "role": "assistant", "conversation_id": test_conversation.id}
        ]
        
        for msg_data in messages_data:
            await message_repository.create(msg_data)
        
        # 获取消息统计
        stats = await message_repository.get_message_stats(test_conversation.id)
        
        assert stats["total_messages"] == 3
        assert stats["user_messages"] == 2
        assert stats["assistant_messages"] == 1


class TestModelRepository:
    """模型仓储测试类"""
    
    @pytest.fixture
    async def model_repository(self, db_session):
        """创建模型仓储实例"""
        return ModelRepository(db_session)
    
    async def test_get_models_by_provider(self, model_repository):
        """测试根据提供商获取模型"""
        # 创建不同提供商的模型
        models_data = [
            {"name": "gpt-3.5-turbo", "provider": "openai", "model_type": "chat"},
            {"name": "gpt-4", "provider": "openai", "model_type": "chat"},
            {"name": "claude-3-sonnet", "provider": "anthropic", "model_type": "chat"}
        ]
        
        for model_data in models_data:
            await model_repository.create(model_data)
        
        # 获取OpenAI的模型
        openai_models = await model_repository.get_models_by_provider("openai")
        
        assert len(openai_models) == 2
        model_names = [model.name for model in openai_models]
        assert "gpt-3.5-turbo" in model_names
        assert "gpt-4" in model_names
    
    async def test_get_models_by_type(self, model_repository):
        """测试根据类型获取模型"""
        # 创建不同类型的模型
        models_data = [
            {"name": "gpt-3.5-turbo", "provider": "openai", "model_type": "chat"},
            {"name": "text-embedding-ada-002", "provider": "openai", "model_type": "embedding"},
            {"name": "dall-e-3", "provider": "openai", "model_type": "image"}
        ]
        
        for model_data in models_data:
            await model_repository.create(model_data)
        
        # 获取聊天模型
        chat_models = await model_repository.get_models_by_type("chat")
        
        assert len(chat_models) == 1
        assert chat_models[0].name == "gpt-3.5-turbo"
    
    async def test_get_available_models(self, model_repository):
        """测试获取可用模型"""
        # 创建可用和不可用的模型
        models_data = [
            {"name": "available-model", "provider": "test", "model_type": "chat", "is_available": True},
            {"name": "unavailable-model", "provider": "test", "model_type": "chat", "is_available": False}
        ]
        
        for model_data in models_data:
            await model_repository.create(model_data)
        
        # 获取可用模型
        available_models = await model_repository.get_available_models()
        
        assert len(available_models) == 1
        assert available_models[0].name == "available-model"
    
    async def test_search_models(self, model_repository):
        """测试搜索模型"""
        # 创建模型
        models_data = [
            {"name": "gpt-3.5-turbo", "provider": "openai", "model_type": "chat"},
            {"name": "gpt-4-turbo", "provider": "openai", "model_type": "chat"},
            {"name": "claude-3-sonnet", "provider": "anthropic", "model_type": "chat"}
        ]
        
        for model_data in models_data:
            await model_repository.create(model_data)
        
        # 搜索包含"turbo"的模型
        search_results = await model_repository.search_models("turbo")
        
        assert len(search_results) == 2
        model_names = [model.name for model in search_results]
        assert "gpt-3.5-turbo" in model_names
        assert "gpt-4-turbo" in model_names
    
    async def test_update_model_availability(self, model_repository):
        """测试更新模型可用性"""
        # 创建模型
        model_data = {
            "name": "test-model",
            "provider": "test",
            "model_type": "chat",
            "is_available": True
        }
        model = await model_repository.create(model_data)
        
        # 更新可用性
        updated_model = await model_repository.update_availability(model.id, False)
        
        assert updated_model.is_available is False
    
    async def test_get_model_usage_stats(self, model_repository):
        """测试获取模型使用统计"""
        # 创建模型
        model_data = {
            "name": "test-model",
            "provider": "test",
            "model_type": "chat"
        }
        model = await model_repository.create(model_data)
        
        # 获取使用统计
        stats = await model_repository.get_usage_stats(model.id)
        
        assert "total_requests" in stats
        assert "total_tokens" in stats
        assert "avg_response_time" in stats


class TestRepositoryManager:
    """仓储管理器测试类"""
    
    @pytest.fixture
    async def repository_manager(self, db_session):
        """创建仓储管理器实例"""
        return RepositoryManager(db_session)
    
    def test_get_user_repository(self, repository_manager):
        """测试获取用户仓储"""
        user_repo = repository_manager.users
        assert isinstance(user_repo, UserRepository)
    
    def test_get_session_repository(self, repository_manager):
        """测试获取会话仓储"""
        session_repo = repository_manager.sessions
        assert isinstance(session_repo, SessionRepository)
    
    def test_get_conversation_repository(self, repository_manager):
        """测试获取对话仓储"""
        conversation_repo = repository_manager.conversations
        assert isinstance(conversation_repo, ConversationRepository)
    
    def test_get_message_repository(self, repository_manager):
        """测试获取消息仓储"""
        message_repo = repository_manager.messages
        assert isinstance(message_repo, MessageRepository)
    
    def test_get_model_repository(self, repository_manager):
        """测试获取模型仓储"""
        model_repo = repository_manager.models
        assert isinstance(model_repo, ModelRepository)
    
    async def test_repository_transaction(self, repository_manager):
        """测试仓储事务"""
        # 在事务中创建用户和会话
        async with repository_manager.transaction():
            # 创建用户
            user_data = {
                "username": "testuser",
                "email": "<EMAIL>",
                "password_hash": "hash"
            }
            user = await repository_manager.users.create(user_data)
            
            # 创建会话
            session_data = {
                "name": "Test Session",
                "user_id": user.id,
                "models": ["gpt-3.5-turbo"]
            }
            session = await repository_manager.sessions.create(session_data)
            
            assert user.id is not None
            assert session.id is not None
            assert session.user_id == user.id
    
    async def test_repository_rollback(self, repository_manager):
        """测试仓储回滚"""
        try:
            async with repository_manager.transaction():
                # 创建用户
                user_data = {
                    "username": "testuser",
                    "email": "<EMAIL>",
                    "password_hash": "hash"
                }
                user = await repository_manager.users.create(user_data)
                
                # 故意引发异常
                raise Exception("Test rollback")
        
        except Exception:
            pass  # 预期的异常
        
        # 验证用户未被创建（事务已回滚）
        user = await repository_manager.users.get_by_username("testuser")
        assert user is None
    
    async def test_bulk_operations(self, repository_manager):
        """测试批量操作"""
        # 批量创建用户
        users_data = [
            {"username": f"user{i}", "email": f"user{i}@example.com", "password_hash": "hash"}
            for i in range(5)
        ]
        
        created_users = await repository_manager.users.bulk_create(users_data)
        
        assert len(created_users) == 5
        assert all(user.id is not None for user in created_users)
    
    async def test_repository_caching(self, repository_manager):
        """测试仓储缓存"""
        # 创建用户
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password_hash": "hash"
        }
        user = await repository_manager.users.create(user_data)
        
        # 第一次获取（从数据库）
        user1 = await repository_manager.users.get_by_id(user.id)
        
        # 第二次获取（从缓存）
        user2 = await repository_manager.users.get_by_id(user.id)
        
        assert user1.id == user2.id
        assert user1.username == user2.username
    
    async def test_repository_performance(self, repository_manager):
        """测试仓储性能"""
        import time
        
        # 批量创建用户
        start_time = time.time()
        
        users_data = [
            {"username": f"user{i}", "email": f"user{i}@example.com", "password_hash": "hash"}
            for i in range(100)
        ]
        
        await repository_manager.users.bulk_create(users_data)
        
        end_time = time.time()
        creation_time = end_time - start_time
        
        # 批量查询用户
        start_time = time.time()
        
        all_users = await repository_manager.users.get_all()
        
        end_time = time.time()
        query_time = end_time - start_time
        
        assert len(all_users) == 100
        assert creation_time < 5.0  # 创建100个用户应该在5秒内完成
        assert query_time < 1.0     # 查询100个用户应该在1秒内完成


class TestRepositoryIntegration:
    """仓储集成测试类"""
    
    async def test_complete_workflow(self, db_session):
        """测试完整的工作流程"""
        repo_manager = RepositoryManager(db_session)
        
        # 1. 创建用户
        user_data = {
            "username": "testuser",
            "email": "<EMAIL>",
            "password_hash": "hash"
        }
        user = await repo_manager.users.create(user_data)
        
        # 2. 创建会话
        session_data = {
            "name": "Test Session",
            "user_id": user.id,
            "models": ["gpt-3.5-turbo", "gpt-4"]
        }
        session = await repo_manager.sessions.create(session_data)
        
        # 3. 创建对话
        conversation_data = {
            "title": "Test Conversation",
            "session_id": session.id
        }
        conversation = await repo_manager.conversations.create(conversation_data)
        
        # 4. 创建消息
        messages_data = [
            {"content": "Hello", "role": "user", "conversation_id": conversation.id},
            {"content": "Hi there!", "role": "assistant", "conversation_id": conversation.id}
        ]
        
        for msg_data in messages_data:
            await repo_manager.messages.create(msg_data)
        
        # 5. 验证数据完整性
        # 获取用户的所有会话
        user_sessions = await repo_manager.sessions.get_sessions_by_user(user.id)
        assert len(user_sessions) == 1
        assert user_sessions[0].name == "Test Session"
        
        # 获取会话的所有对话
        session_conversations = await repo_manager.conversations.get_conversations_by_session(session.id)
        assert len(session_conversations) == 1
        assert session_conversations[0].title == "Test Conversation"
        
        # 获取对话的所有消息
        conversation_messages = await repo_manager.messages.get_messages_by_conversation(conversation.id)
        assert len(conversation_messages) == 2
        assert conversation_messages[0].content == "Hello"
        assert conversation_messages[1].content == "Hi there!"
    
    async def test_cascade_deletion(self, db_session):
        """测试级联删除"""
        repo_manager = RepositoryManager(db_session)
        
        # 创建完整的数据结构
        user = await repo_manager.users.create({
            "username": "testuser",
            "email": "<EMAIL>",
            "password_hash": "hash"
        })
        
        session = await repo_manager.sessions.create({
            "name": "Test Session",
            "user_id": user.id,
            "models": ["gpt-3.5-turbo"]
        })
        
        conversation = await repo_manager.conversations.create({
            "title": "Test Conversation",
            "session_id": session.id
        })
        
        message = await repo_manager.messages.create({
            "content": "Test message",
            "role": "user",
            "conversation_id": conversation.id
        })
        
        # 删除用户（应该级联删除所有相关数据）
        await repo_manager.users.delete(user.id)
        
        # 验证所有相关数据都被删除
        assert await repo_manager.users.get_by_id(user.id) is None
        assert await repo_manager.sessions.get_by_id(session.id) is None
        assert await repo_manager.conversations.get_by_id(conversation.id) is None
        assert await repo_manager.messages.get_by_id(message.id) is None
    
    async def test_data_consistency(self, db_session):
        """测试数据一致性"""
        repo_manager = RepositoryManager(db_session)
        
        # 创建用户和会话
        user = await repo_manager.users.create({
            "username": "testuser",
            "email": "<EMAIL>",
            "password_hash": "hash"
        })
        
        session = await repo_manager.sessions.create({
            "name": "Test Session",
            "user_id": user.id,
            "models": ["gpt-3.5-turbo"]
        })
        
        # 尝试创建引用不存在用户的会话（应该失败）
        with pytest.raises(Exception):
            await repo_manager.sessions.create({
                "name": "Invalid Session",
                "user_id": 999,  # 不存在的用户ID
                "models": ["gpt-3.5-turbo"]
            })
        
        # 验证原有数据未受影响
        retrieved_session = await repo_manager.sessions.get_by_id(session.id)
        assert retrieved_session is not None
        assert retrieved_session.user_id == user.id